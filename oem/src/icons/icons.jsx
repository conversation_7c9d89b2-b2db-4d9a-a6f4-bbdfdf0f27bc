import * as React from "react";

import { COLOR } from "~/constants/colors";

export const ThreeDots = ({ strokeColor = "#172B4D", ...props }) => (
  <svg
    width="10"
    height="2"
    viewBox="0 0 10 2"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M5.625 1C5.625 1.34518 5.34518 1.625 5 1.625C4.65482 1.625 4.375 1.34518 4.375 1C4.375 0.654822 4.65482 0.375 5 0.375C5.34518 0.375 5.625 0.654822 5.625 1Z"
      fill={strokeColor}
      stroke={strokeColor}
      strokeWidth="0.75"
    />
    <path
      d="M9 2C9.55228 2 10 1.55228 10 1C10 0.447715 9.55228 0 9 0C8.44772 0 8 0.447715 8 1C8 1.55228 8.44772 2 9 2Z"
      fill={strokeColor}
    />
    <path
      d="M1 2C1.55228 2 2 1.55228 2 1C2 0.447715 1.55228 0 1 0C0.447715 0 0 0.447715 0 1C0 1.55228 0.447715 2 1 2Z"
      fill={strokeColor}
    />
  </svg>
);

export const SidebarOpenTicketsFolder = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.5 16.25V5C2.5 4.83424 2.56585 4.67527 2.68306 4.55806C2.80027 4.44085 2.95924 4.375 3.125 4.375H7.28906C7.4242 4.37556 7.55562 4.41936 7.66406 4.5L9.83594 6.125C9.94438 6.20564 10.0758 6.24944 10.2109 6.25H15.625C15.7908 6.25 15.9497 6.31585 16.0669 6.43306C16.1842 6.55027 16.25 6.70924 16.25 6.875V8.75"
      stroke="#748094"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 16.25L4.85938 9.17969C4.9005 9.05467 4.98004 8.94583 5.08666 8.86868C5.19327 8.79152 5.32152 8.74999 5.45313 8.75H17.8828C17.9816 8.74999 18.0791 8.77342 18.1671 8.81836C18.2551 8.86331 18.3312 8.92848 18.3892 9.00854C18.4471 9.0886 18.4852 9.18126 18.5004 9.27891C18.5156 9.37657 18.5074 9.47643 18.4766 9.57031L16.25 16.25H2.5Z"
      stroke="#748094"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SidebarMyTicketfolderIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.5 6.25V4.375C2.5 4.20924 2.56585 4.05027 2.68306 3.93306C2.80027 3.81585 2.95924 3.75 3.125 3.75H7.24219C7.32334 3.74972 7.40376 3.76544 7.47883 3.79628C7.5539 3.82711 7.62216 3.87245 7.67969 3.92969L10 6.25"
      fill="#F3F4F6"
    />
    <path
      d="M2.5 6.25V4.375C2.5 4.20924 2.56585 4.05027 2.68306 3.93306C2.80027 3.81585 2.95924 3.75 3.125 3.75H7.24219C7.32334 3.74972 7.40376 3.76544 7.47883 3.79628C7.5539 3.82711 7.62216 3.87245 7.67969 3.92969L10 6.25"
      stroke="#748094"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.42187 16.25H3.07813C3.0022 16.25 2.92703 16.235 2.85689 16.206C2.78674 16.1769 2.72301 16.1344 2.66933 16.0807C2.61564 16.027 2.57306 15.9633 2.54401 15.8931C2.51495 15.823 2.5 15.7478 2.5 15.6719V6.25H16.875C17.0408 6.25 17.1997 6.31585 17.3169 6.43306C17.4342 6.55027 17.5 6.70924 17.5 6.875V9.375"
      stroke="#748094"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.6875 15.5078L17.0078 16.875L16.375 14.3281L18.4375 12.625L15.7266 12.4141L14.6875 10L13.6484 12.4141L10.9375 12.625L13 14.3281L12.3672 16.875L14.6875 15.5078Z"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SidebarClosedTicketsFolderIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M2.5 6.25V4.375C2.5 4.20924 2.56585 4.05027 2.68306 3.93306C2.80027 3.81585 2.95924 3.75 3.125 3.75H7.24219C7.32334 3.74972 7.40376 3.76544 7.47883 3.79628C7.5539 3.82711 7.62216 3.87245 7.67969 3.92969L10 6.25"
      fill="#F3F4F6"
    />
    <path
      d="M2.5 6.25V4.375C2.5 4.20924 2.56585 4.05027 2.68306 3.93306C2.80027 3.81585 2.95924 3.75 3.125 3.75H7.24219C7.32334 3.74972 7.40376 3.76544 7.47883 3.79628C7.5539 3.82711 7.62216 3.87245 7.67969 3.92969L10 6.25"
      stroke="#748094"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.5 12.8125H11.875V16.25H17.5V12.8125Z"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.79687 16.25H3.07813C3.0022 16.25 2.92703 16.235 2.85689 16.206C2.78674 16.1769 2.72301 16.1344 2.66933 16.0807C2.61564 16.027 2.57306 15.9633 2.54401 15.8931C2.51495 15.823 2.5 15.7478 2.5 15.6719V6.25H16.875C17.0408 6.25 17.1997 6.31585 17.3169 6.43306C17.4342 6.55027 17.5 6.70924 17.5 6.875V8.125"
      stroke="#748094"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.125 12.8125V11.875C13.125 11.4606 13.2896 11.0632 13.5826 10.7701C13.8757 10.4771 14.2731 10.3125 14.6875 10.3125C15.1019 10.3125 15.4993 10.4771 15.7924 10.7701C16.0854 11.0632 16.25 11.4606 16.25 11.875V12.8125"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SidebarAssetsIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.77803 8.03596C3.86277 8.03596 4.74213 8.91529 4.74213 10C4.74213 11.0847 3.86277 11.964 2.77803 11.964C1.69328 11.964 0.813925 11.0847 0.813926 10C0.813926 8.91529 1.69328 8.03596 2.77803 8.03596Z"
      fill="#748094"
      stroke={strokeColor || "#748094"}
    />
    <path
      d="M2.77803 8.03596C3.86277 8.03596 4.74213 8.91529 4.74213 10C4.74213 11.0847 3.86277 11.964 2.77803 11.964C1.69328 11.964 0.813925 11.0847 0.813925 10C0.813925 8.91529 1.69328 8.03596 2.77803 8.03596"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
      stroke={strokeColor || "#748094"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.2219 8.03596C18.3066 8.03596 19.186 8.91529 19.186 10C19.186 11.0847 18.3066 11.964 17.2219 11.964C16.1371 11.964 15.2578 11.0847 15.2578 10C15.2578 8.91529 16.1371 8.03596 17.2219 8.03596Z"
      fill="#748094"
      stroke={strokeColor || "#748094"}
    />
    <path
      d="M17.2219 8.03596C18.3066 8.03596 19.186 8.91529 19.186 10C19.186 11.0847 18.3066 11.964 17.2219 11.964C16.1371 11.964 15.2578 11.0847 15.2578 10C15.2578 8.91529 16.1371 8.03596 17.2219 8.03596"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
      stroke={strokeColor || "#748094"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.55572 13.3343L6.66679 14.4453L5.55572 13.3343Z"
      fill="#748094"
      stroke={strokeColor || "#748094"}
    />
    <path
      d="M5.55572 13.3343L6.66679 14.4453"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
      stroke={strokeColor || "#748094"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3331 5.55549L14.4441 6.66652L13.3331 5.55549Z"
      fill="#748094"
      stroke={strokeColor || "#748094"}
    />
    <path
      d="M13.3331 5.55549L14.4441 6.66652"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
      stroke={strokeColor || "#748094"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.99983 15.2584C11.0846 15.2584 11.9639 16.1377 11.9639 17.2224C11.9639 18.3071 11.0846 19.1864 9.99983 19.1864C8.91508 19.1864 8.03573 18.3071 8.03573 17.2224C8.03573 16.1377 8.91509 15.2584 9.99983 15.2584Z"
      fill="#748094"
      stroke={strokeColor || "#748094"}
    />
    <path
      d="M9.99983 15.2584C11.0846 15.2584 11.9639 16.1377 11.9639 17.2224C11.9639 18.3071 11.0846 19.1864 9.99983 19.1864C8.91508 19.1864 8.03573 18.3071 8.03573 17.2224C8.03573 16.1377 8.91509 15.2584 9.99983 15.2584"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
      stroke={strokeColor || "#748094"}
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.99983 0.813551C11.0846 0.813551 11.9639 1.69288 11.9639 2.77759C11.9639 3.8623 11.0846 4.74163 9.99983 4.74162C8.91508 4.74162 8.03573 3.8623 8.03573 2.77759C8.03573 1.69288 8.91509 0.81355 9.99983 0.813551Z"
      fill="#748094"
      stroke={strokeColor || "#748094"}
    />
    <path
      d="M9.99983 0.813551C11.0846 0.813551 11.9639 1.69288 11.9639 2.77759C11.9639 3.8623 11.0846 4.74163 9.99983 4.74162C8.91508 4.74162 8.03573 3.8623 8.03573 2.77759C8.03573 1.69288 8.91509 0.81355 9.99983 0.813551"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3331 14.4445L14.4441 13.3335L13.3331 14.4445Z"
      fill="#748094"
      stroke={strokeColor || "#748094"}
    />
    <path
      d="M13.3331 14.4445L14.4441 13.3335"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.55572 6.66545L6.66679 5.55442L5.55572 6.66545Z"
      fill="#748094"
      stroke={strokeColor || "#748094"}
    />
    <path
      d="M5.55572 6.66545L6.66679 5.55442"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SidebarMachinesIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9.5554 2.31911L7.31255 4.56196C7.06847 4.80604 7.06847 5.20177 7.31255 5.44585L9.55374 7.68704C9.79782 7.93112 10.1935 7.93112 10.4376 7.68704L12.6805 5.44419C12.9246 5.20011 12.9246 4.80438 12.6805 4.56031L10.4393 2.31911C10.1952 2.07503 9.79948 2.07503 9.5554 2.31911Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.5559 7.31911L12.313 9.56196C12.069 9.80604 12.069 10.2018 12.313 10.4458L14.5542 12.687C14.7983 12.9311 15.194 12.9311 15.4381 12.687L17.681 10.4442C17.9251 10.2001 17.9251 9.80438 17.681 9.56031L15.4398 7.31911C15.1957 7.07503 14.8 7.07503 14.5559 7.31911Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.5554 7.3113L2.31255 9.55415C2.06847 9.79823 2.06847 10.194 2.31255 10.438L4.55374 12.6792C4.79782 12.9233 5.19355 12.9233 5.43763 12.6792L7.68048 10.4364C7.92456 10.1923 7.92456 9.79657 7.68048 9.5525L5.43928 7.3113C5.19521 7.06722 4.79948 7.06722 4.5554 7.3113Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.55589 12.3113L7.31304 14.5542C7.06896 14.7982 7.06896 15.194 7.31304 15.438L9.55424 17.6792C9.79831 17.9233 10.194 17.9233 10.4381 17.6792L12.681 15.4364C12.9251 15.1923 12.9251 14.7966 12.681 14.5525L10.4398 12.3113C10.1957 12.0672 9.79997 12.0672 9.55589 12.3113Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SidebarFacilityIcon = ({
  strokeColor = "#748094",
  fillColor = "#748094",
  ...props
}) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_20377_46458)">
      <path
        d="M11.2627 7.42773H17.2777V15.9992H11.2627V7.42773Z"
        stroke={strokeColor || "#748094"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <rect
        x="13.0674"
        y="9.26562"
        width="2.40601"
        height="1"
        rx="0.5"
        fill={fillColor || "#748094"}
      />
      <path
        d="M2.8418 4H11.2628V16H2.8418V4Z"
        stroke={strokeColor || "#748094"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <rect
        x="5.36816"
        y="6.57227"
        width="3.36842"
        height="1"
        rx="0.5"
        fill={fillColor || "#748094"}
      />
      <path
        d="M2 16L18 16"
        stroke={strokeColor || "#748094"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_20377_46458">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SettingsIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10 13.75C12.0711 13.75 13.75 12.0711 13.75 10C13.75 7.92893 12.0711 6.25 10 6.25C7.92893 6.25 6.25 7.92893 6.25 10C6.25 12.0711 7.92893 13.75 10 13.75Z"
      stroke="#748094"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.3516 5.08594C14.5495 5.26823 14.737 5.45573 14.9141 5.64844L17.0469 5.95313C17.3944 6.55665 17.6622 7.20265 17.8438 7.875L16.5469 9.60156C16.5469 9.60156 16.5703 10.1328 16.5469 10.3984L17.8438 12.125C17.6631 12.7976 17.3952 13.4438 17.0469 14.0469L14.9141 14.3516C14.9141 14.3516 14.5469 14.7344 14.3516 14.9141L14.0469 17.0469C13.4434 17.3944 12.7974 17.6622 12.125 17.8438L10.3984 16.5469C10.1333 16.5703 9.86667 16.5703 9.60156 16.5469L7.875 17.8438C7.20236 17.6631 6.55625 17.3952 5.95313 17.0469L5.64844 14.9141C5.45573 14.7318 5.26823 14.5443 5.08594 14.3516L2.95313 14.0469C2.60561 13.4434 2.33776 12.7974 2.15625 12.125L3.45313 10.3984C3.45313 10.3984 3.42969 9.86719 3.45313 9.60156L2.15625 7.875C2.33692 7.20236 2.60481 6.55625 2.95313 5.95313L5.08594 5.64844C5.26823 5.45573 5.45573 5.26823 5.64844 5.08594L5.95313 2.95313C6.55665 2.60561 7.20265 2.33776 7.875 2.15625L9.60156 3.45313C9.86667 3.42968 10.1333 3.42968 10.3984 3.45313L12.125 2.15625C12.7976 2.33692 13.4438 2.60481 14.0469 2.95313L14.3516 5.08594Z"
      stroke="#748094"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LogoutIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10.875 5.375L13.5 8L10.875 10.625"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.5 8H13.5"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.5 13.5H3C2.86739 13.5 2.74021 13.4473 2.64645 13.3536C2.55268 13.2598 2.5 13.1326 2.5 13V3C2.5 2.86739 2.55268 2.74021 2.64645 2.64645C2.74021 2.55268 2.86739 2.5 3 2.5H6.5"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BtnFilterIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M10 8.4375V16.875"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 3.125V5.3125"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 8.4375C10.8629 8.4375 11.5625 7.73794 11.5625 6.875C11.5625 6.01206 10.8629 5.3125 10 5.3125C9.13706 5.3125 8.4375 6.01206 8.4375 6.875C8.4375 7.73794 9.13706 8.4375 10 8.4375Z"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.625 14.6875V16.875"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.625 3.125V11.5625"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.625 14.6875C16.4879 14.6875 17.1875 13.9879 17.1875 13.125C17.1875 12.2621 16.4879 11.5625 15.625 11.5625C14.7621 11.5625 14.0625 12.2621 14.0625 13.125C14.0625 13.9879 14.7621 14.6875 15.625 14.6875Z"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.375 12.1875V16.875"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.375 3.125V9.0625"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.375 12.1875C5.23794 12.1875 5.9375 11.4879 5.9375 10.625C5.9375 9.76206 5.23794 9.0625 4.375 9.0625C3.51206 9.0625 2.8125 9.76206 2.8125 10.625C2.8125 11.4879 3.51206 12.1875 4.375 12.1875Z"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BtnPlusIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.125 10H16.875"
      stroke="black"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 3.125V16.875"
      stroke="black"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BtnCrossIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M15.625 4.375L4.375 15.625"
      stroke="black"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.625 15.625L4.375 4.375"
      stroke="black"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BtnPaperPlaneIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M17.1485 9.45312L3.95314 2.06249C3.84199 2.00109 3.71477 1.97499 3.58843 1.98769C3.46208 2.00038 3.3426 2.05126 3.24589 2.13355C3.14918 2.21584 3.07982 2.32564 3.04707 2.44832C3.01431 2.57101 3.01969 2.70076 3.06251 2.8203L5.54689 9.78905C5.60162 9.92435 5.60162 10.0756 5.54689 10.2109L3.06251 17.1797C3.01969 17.2992 3.01431 17.429 3.04707 17.5517C3.07982 17.6743 3.14918 17.7841 3.24589 17.8664C3.3426 17.9487 3.46208 17.9996 3.58843 18.0123C3.71477 18.025 3.84199 17.9989 3.95314 17.9375L17.1485 10.5469C17.2461 10.4928 17.3276 10.4136 17.3843 10.3174C17.441 10.2212 17.4709 10.1116 17.4709 9.99999C17.4709 9.88835 17.441 9.77874 17.3843 9.68257C17.3276 9.5864 17.2461 9.50717 17.1485 9.45312V9.45312Z"
      stroke="black"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.625 10H10.625"
      stroke="black"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BtnLeftAngleIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M12.5 16.25L6.25 10L12.5 3.75"
      stroke="#172B4D"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BtnRightAngleIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.25 3.75L12.5 10L6.25 16.25"
      stroke="#172B4D"
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CommentSmallIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M4.475 9L2 11V3C2 2.86739 2.05268 2.74021 2.14645 2.64645C2.24021 2.55268 2.36739 2.5 2.5 2.5H10.5C10.6326 2.5 10.7598 2.55268 10.8536 2.64645C10.9473 2.74021 11 2.86739 11 3V8.5C11 8.63261 10.9473 8.75979 10.8536 8.85355C10.7598 8.94732 10.6326 9 10.5 9H4.475Z"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 9V11.5C5 11.6326 5.05268 11.7598 5.14645 11.8536C5.24021 11.9473 5.36739 12 5.5 12H11.525L14 14V6C14 5.86739 13.9473 5.74021 13.8536 5.64645C13.7598 5.55268 13.6326 5.5 13.5 5.5H11"
      stroke="black"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const OpenFolderIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.5 16.25V5C2.5 4.83424 2.56585 4.67527 2.68306 4.55806C2.80027 4.44085 2.95924 4.375 3.125 4.375H7.28906C7.4242 4.37556 7.55562 4.41936 7.66406 4.5L9.83594 6.125C9.94438 6.20564 10.0758 6.24944 10.2109 6.25H15.625C15.7908 6.25 15.9497 6.31585 16.0669 6.43306C16.1842 6.55027 16.25 6.70924 16.25 6.875V8.75"
      stroke={strokeColor || "black"}
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 16.25L4.85938 9.17969C4.9005 9.05467 4.98004 8.94583 5.08666 8.86868C5.19327 8.79152 5.32152 8.74999 5.45313 8.75H17.8828C17.9816 8.74999 18.0791 8.77342 18.1671 8.81836C18.2551 8.86331 18.3312 8.92848 18.3892 9.00854C18.4471 9.0886 18.4852 9.18126 18.5004 9.27891C18.5156 9.37657 18.5074 9.47643 18.4766 9.57031L16.25 16.25H2.5Z"
      stroke={strokeColor || "black"}
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const UpAngleSmallIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M3 10L8 5L13 10"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DownAngleSmallIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13 6L8 11L3 6"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.4"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const NoTicketsSmallIcon = ({ width = "100", height = "100" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 100 100"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_532_11847)">
      <path
        d="M40.9863 86.7156C44.791 84.4887 83.3474 61.899 84.9456 60.4763C85.9475 59.5784 85.8981 58.7503 85.8981 58.7503C85.879 58.495 85.7952 58.2487 85.6544 58.0347C85.116 57.1709 82.9283 53.5723 79.9346 48.6431"
        fill="#A2AAB8"
      />
      <path
        d="M40.6861 87.2727C40.3574 87.2734 40.0319 87.2091 39.7281 87.0837C39.4243 86.9582 39.1482 86.7741 38.9158 86.5418L32.3917 80.0177L32.8245 79.5969C33.6869 78.7552 34.3736 77.7507 34.8449 76.6417C35.3161 75.5326 35.5626 74.341 35.5699 73.136C35.5772 71.931 35.3453 70.7365 34.8875 69.6218C34.4297 68.5071 33.7553 67.4943 32.9032 66.6423C32.0511 65.7902 31.0383 65.1157 29.9236 64.6579C28.8089 64.2001 27.6144 63.9682 26.4094 63.9755C25.2044 63.9828 24.0128 64.2293 22.9038 64.7005C21.7947 65.1718 20.7902 65.8585 19.9485 66.7209L19.5277 67.1537L13.0053 60.6314C12.7724 60.3985 12.5876 60.1219 12.4615 59.8176C12.3354 59.5133 12.2705 59.1871 12.2705 58.8577C12.2705 58.5282 12.3354 58.202 12.4615 57.8977C12.5876 57.5934 12.7724 57.3169 13.0053 57.084L57.0839 13.0071C57.3164 12.7743 57.5925 12.5896 57.8964 12.4636C58.2003 12.3376 58.526 12.2727 58.855 12.2727C59.184 12.2727 59.5098 12.3376 59.8137 12.4636C60.1176 12.5896 60.3937 12.7743 60.6262 13.0071L67.1485 19.5294L66.7175 19.9503C65.8487 20.7904 65.1558 21.795 64.6792 22.9057C64.2027 24.0163 63.952 25.2107 63.9419 26.4192C63.9317 27.6277 64.1622 28.8262 64.6199 29.9447C65.0777 31.0632 65.7535 32.0794 66.608 32.934C67.4625 33.7886 68.4786 34.4646 69.5971 34.9225C70.7155 35.3804 71.9139 35.611 73.1224 35.601C74.331 35.591 75.5254 35.3405 76.6361 34.8641C77.7468 34.3877 78.7515 33.695 79.5917 32.8263L80.0143 32.3935L86.5366 38.9158C87.0057 39.3863 87.2691 40.0235 87.2691 40.6878C87.2691 41.3521 87.0057 41.9894 86.5366 42.4598L42.4581 86.5418C42.2253 86.7741 41.9489 86.9583 41.6449 87.0837C41.3408 87.2092 41.015 87.2734 40.6861 87.2727ZM34.0819 80.016L39.7626 85.6984C40.0093 85.9437 40.3432 86.0814 40.6912 86.0814C41.0392 86.0814 41.373 85.9437 41.6198 85.6984L85.6966 41.6164C85.942 41.3696 86.0797 41.0358 86.0797 40.6878C86.0797 40.3398 85.942 40.006 85.6966 39.7592L80.0143 34.082C78.1243 35.8212 75.648 36.7837 73.0796 36.7775C70.347 36.7739 67.7273 35.6866 65.7952 33.7542C63.863 31.8217 62.7762 29.2019 62.7731 26.4692C62.766 23.8999 63.7279 21.4225 65.4668 19.5311L59.7862 13.8505C59.6645 13.7285 59.5199 13.6316 59.3607 13.5656C59.2015 13.4995 59.0308 13.4655 58.8585 13.4655C58.6861 13.4655 58.5154 13.4995 58.3562 13.5656C58.197 13.6316 58.0524 13.7285 57.9307 13.8505L13.8487 57.9359C13.6031 58.1821 13.4651 58.5158 13.4651 58.8636C13.4651 59.2115 13.6031 59.5451 13.8487 59.7914L19.5294 65.472C21.4193 63.7328 23.8956 62.7703 26.464 62.7765C29.197 62.7797 31.8171 63.8667 33.7496 65.7992C35.6821 67.7317 36.7692 70.3518 36.7723 73.0848C36.7791 75.6513 35.8185 78.1262 34.0819 80.016Z"
        fill="#172B4D"
      />
      <path
        d="M33.1308 49.1089L28.0092 43.9873C27.7763 43.7544 27.3987 43.7544 27.1658 43.9873C26.9329 44.2201 26.9329 44.5977 27.1658 44.8306L32.2874 49.9522C32.5203 50.1851 32.8979 50.1851 33.1308 49.9522C33.3637 49.7193 33.3637 49.3418 33.1308 49.1089Z"
        fill="#172B4D"
      />
      <path
        d="M55.2607 71.2385L50.1391 66.1169C49.9062 65.884 49.5286 65.884 49.2957 66.1169C49.0628 66.3498 49.0628 66.7273 49.2957 66.9602L54.4173 72.0818C54.6502 72.3147 55.0278 72.3147 55.2607 72.0818C55.4936 71.849 55.4936 71.4714 55.2607 71.2385Z"
        fill="#172B4D"
      />
      <path
        d="M44.2041 60.1822L39.0824 55.0606C38.8495 54.8277 38.472 54.8277 38.2391 55.0606C38.0062 55.2935 38.0062 55.6711 38.2391 55.904L43.3607 61.0256C43.5936 61.2585 43.9712 61.2585 44.2041 61.0256C44.4369 60.7927 44.4369 60.4151 44.2041 60.1822Z"
        fill="#172B4D"
      />
      <path
        d="M46.8229 38.1078C51.0675 33.8633 51.0675 26.9815 46.8229 22.737C42.5784 18.4924 35.6966 18.4924 31.452 22.737C27.2075 26.9815 27.2075 33.8633 31.452 38.1078C35.6966 42.3524 42.5784 42.3524 46.8229 38.1078Z"
        fill="#E6E8FE"
      />
      <path
        d="M39.1376 41.889C36.8699 41.888 34.6533 41.2146 32.7683 39.954C30.8832 38.6934 29.4143 36.9022 28.5472 34.8068C27.6801 32.7114 27.4537 30.4059 27.8967 28.1819C28.3398 25.9579 29.4323 23.9152 31.0362 22.312C32.64 20.7088 34.6833 19.6172 36.9075 19.1752C39.1317 18.7332 41.4371 18.9605 43.5321 19.8286C45.6271 20.6966 47.4176 22.1664 48.6774 24.052C49.9372 25.9376 50.6096 28.1544 50.6096 30.4221C50.606 33.4631 49.396 36.3785 47.2452 38.5283C45.0944 40.6781 42.1786 41.8868 39.1376 41.889ZM39.1376 20.1496C37.1057 20.1506 35.1197 20.7541 33.4307 21.8838C31.7418 23.0134 30.4257 24.6185 29.6489 26.496C28.8721 28.3736 28.6695 30.4393 29.0666 32.432C29.4638 34.4247 30.4429 36.2549 31.8801 37.6912C33.3174 39.1275 35.1482 40.1054 37.1412 40.5012C39.1342 40.897 41.1998 40.693 43.0768 39.915C44.9538 39.1369 46.558 37.8198 47.6866 36.1301C48.8151 34.4404 49.4172 32.454 49.4169 30.4221C49.4137 27.6975 48.3295 25.0854 46.4022 23.1595C44.475 21.2335 41.8622 20.151 39.1376 20.1496Z"
        fill="#172B4D"
      />
      <path
        d="M43.6268 33.6468L36.4043 26.8597C36.1479 26.6187 35.7447 26.6312 35.5037 26.8876C35.2627 27.1441 35.2753 27.5473 35.5317 27.7883L42.7542 34.5754C43.0106 34.8164 43.4139 34.8038 43.6548 34.5474C43.8958 34.291 43.8833 33.8877 43.6268 33.6468Z"
        fill="#0517F8"
      />
      <path
        d="M42.6514 26.4972L35.662 33.4282C35.3989 33.6892 35.3971 34.1141 35.6581 34.3772C35.919 34.6404 36.3439 34.6422 36.6071 34.3812L43.5964 27.4502C43.8596 27.1892 43.8614 26.7643 43.6004 26.5012C43.3394 26.238 42.9145 26.2362 42.6514 26.4972Z"
        fill="#0517F8"
      />
    </g>
    <defs>
      <clipPath id="clip0_532_11847">
        <rect
          width="75"
          height="75"
          fill="white"
          transform="translate(12.2725 12.2727)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const NoTicketsLargeIcon = ({ width = "165", height = "165" }) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 165 165"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1710_1651)">
      <path
        d="M63.168 163.774C71.5383 158.875 156.362 109.178 159.878 106.048C162.083 104.072 161.974 102.251 161.974 102.251C161.932 101.689 161.747 101.147 161.438 100.676C160.253 98.7757 155.44 90.859 148.854 80.0146"
        fill="#A2AAB8"
      />
      <path
        d="M62.5084 165C61.7853 165.001 61.0691 164.86 60.4008 164.584C59.7325 164.308 59.1252 163.903 58.6137 163.392L44.2608 149.039L45.2129 148.113C47.1102 146.262 48.621 144.052 49.6578 141.612C50.6945 139.172 51.2367 136.55 51.2528 133.899C51.2689 131.248 50.7586 128.62 49.7515 126.168C48.7444 123.716 47.2606 121.488 45.386 119.613C43.5114 117.738 41.2833 116.255 38.831 115.247C36.3786 114.24 33.7508 113.73 31.0997 113.746C28.4487 113.762 25.8272 114.304 23.3873 115.341C20.9473 116.378 18.7375 117.889 16.8858 119.786L15.9599 120.738L1.61077 106.389C1.09824 105.877 0.691679 105.268 0.414295 104.599C0.13691 103.929 -0.00585937 103.212 -0.00585938 102.487C-0.00585938 101.762 0.13691 101.045 0.414295 100.375C0.691679 99.7055 1.09824 99.0971 1.61077 98.5847L98.5836 1.61567C99.095 1.10349 99.7024 0.697161 100.371 0.419929C101.04 0.142697 101.756 0 102.48 0C103.204 0 103.921 0.142697 104.589 0.419929C105.258 0.697161 105.865 1.10349 106.377 1.61567L120.726 15.9648L119.777 16.8907C117.866 18.7389 116.342 20.9491 115.293 23.3925C114.245 25.8359 113.694 28.4636 113.671 31.1223C113.649 33.781 114.156 36.4176 115.163 38.8783C116.17 41.339 117.657 43.5747 119.537 45.4548C121.417 47.335 123.652 48.8222 126.113 49.8295C128.573 50.8369 131.21 51.3443 133.868 51.3223C136.527 51.3002 139.155 50.7491 141.598 49.701C144.042 48.653 146.252 47.129 148.101 45.2178L149.03 44.2657L163.38 58.6148C164.411 59.6498 164.991 61.0517 164.991 62.5133C164.991 63.9748 164.411 65.3767 163.38 66.4117L66.4068 163.392C65.8946 163.903 65.2867 164.308 64.6177 164.584C63.9488 164.86 63.232 165.002 62.5084 165ZM47.9793 149.035L60.4767 161.536C61.0196 162.076 61.754 162.379 62.5196 162.379C63.2852 162.379 64.0196 162.076 64.5625 161.536L161.532 64.5562C162.071 64.0133 162.374 63.2788 162.374 62.5133C162.374 61.7477 162.071 61.0132 161.532 60.4703L149.03 47.9804C144.873 51.8067 139.425 53.9243 133.774 53.9105C127.762 53.9026 121.999 51.5106 117.748 47.2592C113.498 43.0079 111.107 37.2442 111.1 31.2323C111.084 25.5799 113.2 20.1295 117.026 15.9685L104.529 3.47116C104.261 3.20266 103.943 2.98963 103.593 2.84428C103.242 2.69893 102.867 2.62411 102.488 2.62411C102.108 2.62411 101.733 2.69893 101.383 2.84428C101.032 2.98963 100.714 3.20266 100.447 3.47116L3.46626 100.459C2.92582 101.001 2.62232 101.735 2.62232 102.5C2.62232 103.265 2.92582 103.999 3.46626 104.541L15.9637 117.038C20.1216 113.212 25.5694 111.095 31.2199 111.108C37.2324 111.115 42.9967 113.507 47.2482 117.758C51.4997 122.01 53.8912 127.774 53.8981 133.787C53.913 139.433 51.7997 144.878 47.9793 149.035Z"
        fill="#172B4D"
      />
      <path
        d="M45.8866 81.0396L34.619 69.772C34.1067 69.2597 33.276 69.2597 32.7636 69.772C32.2513 70.2844 32.2513 71.1151 32.7636 71.6274L44.0312 82.895C44.5435 83.4073 45.3742 83.4073 45.8866 82.895C46.3989 82.3826 46.3989 81.5519 45.8866 81.0396Z"
        fill="#172B4D"
      />
      <path
        d="M94.5721 129.725L83.3046 118.457C82.7922 117.945 81.9615 117.945 81.4492 118.457C80.9368 118.969 80.9368 119.8 81.4492 120.312L92.7167 131.58C93.2291 132.092 94.0598 132.092 94.5721 131.58C95.0845 131.068 95.0845 130.237 94.5721 129.725Z"
        fill="#172B4D"
      />
      <path
        d="M70.2479 105.401L58.9804 94.1333C58.468 93.621 57.6373 93.621 57.125 94.1333C56.6126 94.6457 56.6126 95.4764 57.125 95.9887L68.3925 107.256C68.9049 107.769 69.7356 107.769 70.2479 107.256C70.7603 106.744 70.7603 105.913 70.2479 105.401Z"
        fill="#172B4D"
      />
      <path
        d="M76.009 56.8372C85.3471 47.4992 85.3471 32.3592 76.009 23.0212C66.671 13.6832 51.5311 13.6832 42.1931 23.0212C32.8551 32.3592 32.8551 47.4992 42.1931 56.8372C51.5311 66.1752 66.671 66.1752 76.009 56.8372Z"
        fill="#E6E8FE"
      />
      <path
        d="M59.1007 65.156C54.1117 65.1538 49.2354 63.6724 45.0883 60.899C40.9412 58.1257 37.7095 54.1849 35.8019 49.5751C33.8942 44.9652 33.3962 39.8932 34.3709 35.0004C35.3455 30.1075 37.7491 25.6135 41.2776 22.0866C44.8062 18.5596 49.3012 16.1581 54.1945 15.1856C59.0878 14.2131 64.1596 14.7133 68.7686 16.6231C73.3776 18.5328 77.3169 21.7662 80.0884 25.9145C82.8599 30.0629 84.3391 34.9399 84.3391 39.9288C84.3312 46.619 81.6692 53.0327 76.9375 57.7624C72.2058 62.492 65.7909 65.1511 59.1007 65.156ZM59.1007 17.3293C54.6305 17.3316 50.2614 18.6592 46.5457 21.1444C42.83 23.6296 39.9346 27.1607 38.2257 31.2914C36.5167 35.422 36.0709 39.9666 36.9447 44.3505C37.8184 48.7345 39.9724 52.761 43.1343 55.9208C46.2963 59.0807 50.3242 61.232 54.7087 62.1028C59.0933 62.9736 63.6375 62.5248 67.767 60.8131C71.8965 59.1014 75.4257 56.2037 77.9085 52.4864C80.3912 48.7691 81.7159 44.399 81.7152 39.9288C81.7082 33.9346 79.3229 28.1881 75.083 23.951C70.843 19.7138 65.0949 17.3323 59.1007 17.3293Z"
        fill="#172B4D"
      />
      <path
        d="M68.9773 47.0229L53.0878 32.0913C52.5236 31.5611 51.6365 31.5887 51.1064 32.1528C50.5763 32.717 50.6038 33.6041 51.168 34.1342L67.0575 49.0659C67.6216 49.596 68.5087 49.5684 69.0389 49.0043C69.569 48.4401 69.5414 47.553 68.9773 47.0229Z"
        fill="#0517F8"
      />
      <path
        d="M66.831 31.2938L51.4546 46.542C50.8756 47.1161 50.8717 48.0509 51.4458 48.6298C52.0199 49.2088 52.9547 49.2127 53.5337 48.6386L68.9102 33.3904C69.4891 32.8163 69.493 31.8815 68.9189 31.3025C68.3448 30.7236 67.41 30.7197 66.831 31.2938Z"
        fill="#0517F8"
      />
    </g>
    <defs>
      <clipPath id="clip0_1710_1651">
        <rect width="165" height="165" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const QuitDoorImage = () => (
  <svg
    width="130"
    height="130"
    viewBox="0 0 130 130"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1710_25119)">
      <path
        d="M114.248 75.2414C114.105 75.2415 113.966 75.1973 113.849 75.1149C113.733 75.0324 113.645 74.9159 113.597 74.7813L111.651 69.2134C111.59 69.0407 111.6 68.8508 111.679 68.6856C111.758 68.5203 111.899 68.3932 112.072 68.3322C112.244 68.2712 112.434 68.2812 112.599 68.3602C112.765 68.4392 112.892 68.5805 112.953 68.7532L114.902 74.3188C114.932 74.4046 114.945 74.4955 114.94 74.5863C114.934 74.6771 114.911 74.766 114.872 74.8479C114.832 74.9298 114.777 75.003 114.709 75.0634C114.641 75.1239 114.562 75.1703 114.476 75.2C114.403 75.2262 114.326 75.2402 114.248 75.2414Z"
        fill="#172B4D"
      />
      <path
        d="M121.454 75.2414C121.376 75.2412 121.298 75.228 121.224 75.2023C121.138 75.1724 121.059 75.1258 120.992 75.0653C120.924 75.0048 120.869 74.9315 120.83 74.8496C120.79 74.7677 120.767 74.6789 120.762 74.5882C120.758 74.4975 120.771 74.4068 120.801 74.3211L122.747 68.7555C122.808 68.5829 122.935 68.4415 123.101 68.3625C123.266 68.2836 123.456 68.2735 123.628 68.3345C123.801 68.3955 123.943 68.5226 124.021 68.6879C124.1 68.8532 124.111 69.043 124.05 69.2157L122.098 74.779C122.052 74.9129 121.965 75.0292 121.849 75.112C121.734 75.1947 121.596 75.2399 121.454 75.2414Z"
        fill="#172B4D"
      />
      <path
        d="M118.07 73.0236C117.887 73.0236 117.711 72.9508 117.582 72.8214C117.452 72.692 117.38 72.5164 117.38 72.3333L117.403 66.4364C117.403 66.2534 117.475 66.0778 117.605 65.9484C117.734 65.8189 117.91 65.7462 118.093 65.7462C118.276 65.7462 118.451 65.8189 118.581 65.9484C118.71 66.0778 118.783 66.2534 118.783 66.4364L118.76 72.3356C118.759 72.5183 118.686 72.6933 118.557 72.8222C118.428 72.9512 118.252 73.0236 118.07 73.0236Z"
        fill="#172B4D"
      />
      <path
        d="M112.879 92.4812C112.696 92.4934 112.516 92.4324 112.378 92.3116C112.24 92.1908 112.155 92.0201 112.143 91.837C112.131 91.654 112.192 91.4735 112.313 91.3355C112.433 91.1974 112.604 91.113 112.787 91.1008L115.925 90.6728C116.107 90.6481 116.291 90.6965 116.437 90.8074C116.583 90.9183 116.678 91.0826 116.703 91.2641C116.728 91.4457 116.679 91.6296 116.569 91.7754C116.458 91.9212 116.293 92.0171 116.112 92.0418L112.974 92.4812C112.942 92.4835 112.911 92.4835 112.879 92.4812Z"
        fill="#172B4D"
      />
      <path
        d="M112.382 89.8491C112.231 89.8494 112.083 89.7997 111.962 89.7076C111.842 89.6156 111.755 89.4864 111.715 89.3399C111.675 89.1935 111.685 89.038 111.742 88.8976C111.799 88.7571 111.902 88.6395 112.033 88.5629L114.77 86.9524C114.849 86.8995 114.937 86.8633 115.03 86.8461C115.123 86.8288 115.219 86.8308 115.311 86.8521C115.403 86.8733 115.49 86.9133 115.566 86.9695C115.642 87.0257 115.706 87.0969 115.754 87.1788C115.801 87.2607 115.831 87.3514 115.842 87.4454C115.853 87.5394 115.844 87.6346 115.817 87.7251C115.789 87.8156 115.744 87.8996 115.682 87.9717C115.621 88.0438 115.546 88.1025 115.461 88.1442L112.723 89.7547C112.619 89.8152 112.502 89.8477 112.382 89.8491Z"
        fill="#172B4D"
      />
      <path
        d="M76.7501 121.894C76.624 121.894 76.4981 121.887 76.3727 121.874L26.9819 116.338V10.5873L75.9149 8.2727C76.3703 8.24806 76.826 8.31701 77.2538 8.47529C77.6815 8.63358 78.0723 8.87784 78.402 9.19301C78.734 9.50586 78.9988 9.88302 79.1804 10.3015C79.362 10.72 79.4564 11.1711 79.4581 11.6272L80.1345 118.48C80.1369 118.926 80.0512 119.368 79.8822 119.781C79.7133 120.194 79.4644 120.569 79.1499 120.886C78.8354 121.202 78.4615 121.453 78.0497 121.625C77.6378 121.796 77.1962 121.885 76.7501 121.885V121.894ZM28.3624 115.105L76.5269 120.502C76.8083 120.534 77.0931 120.505 77.3626 120.418C77.6322 120.331 77.8803 120.188 78.0906 119.999C78.301 119.809 78.4689 119.578 78.5832 119.319C78.6975 119.06 78.7558 118.779 78.754 118.496L78.0776 11.6456C78.076 11.3754 78.0199 11.1083 77.9125 10.8603C77.8052 10.6123 77.6488 10.3885 77.4529 10.2025C77.2569 10.0164 77.0254 9.87181 76.7721 9.77742C76.5189 9.68303 76.2493 9.64076 75.9793 9.65316L28.3532 11.901L28.3624 115.105Z"
        fill="#172B4D"
      />
      <path
        d="M78.7817 13.6565L88.1458 14.1166C88.5027 14.1341 88.8396 14.2863 89.0886 14.5425C89.3376 14.7987 89.48 15.1399 89.4872 15.4971C90.0072 47.5424 90.5279 79.5892 91.0494 111.637C91.0523 111.828 91.0165 112.018 90.9442 112.194C90.8719 112.371 90.7645 112.531 90.6286 112.665C90.4927 112.799 90.3311 112.904 90.1535 112.974C89.9759 113.043 89.7861 113.076 89.5953 113.071L79.4098 112.763"
        fill="#172B4D"
      />
      <path
        d="M89.6367 114.007H89.5677L79.3822 113.697L79.4374 111.856L89.6229 112.164C89.6893 112.166 89.7554 112.155 89.8172 112.13C89.879 112.106 89.9353 112.069 89.9825 112.023C90.0298 111.976 90.0672 111.92 90.0924 111.859C90.1176 111.797 90.1301 111.732 90.1291 111.665L88.5669 15.5271C88.5643 15.4037 88.5157 15.2857 88.4305 15.1963C88.3454 15.1069 88.2299 15.0525 88.1067 15.0439L78.7426 14.5838L78.8346 12.7432L88.1988 13.2033C88.7909 13.2347 89.3489 13.4898 89.7602 13.9168C90.1715 14.3439 90.4053 14.9112 90.4144 15.5041L91.9766 111.644C91.9815 111.954 91.9248 112.261 91.8097 112.549C91.6947 112.836 91.5236 113.097 91.3065 113.318C91.0893 113.539 90.8305 113.714 90.545 113.833C90.2595 113.953 89.9531 114.014 89.6436 114.014L89.6367 114.007Z"
        fill="#172B4D"
      />
      <path
        d="M70.3447 69.6712C68.6191 69.6712 67.2087 68.04 67.2087 66.0452C67.2087 64.0504 68.6191 62.4192 70.3447 62.4192C72.0703 62.4192 73.4807 64.0504 73.4807 66.0452C73.4807 68.04 72.0703 69.6712 70.3447 69.6712Z"
        fill="#172B4D"
      />
      <path
        d="M80.5671 72.2942C82.2332 74.2109 84.2009 75.8429 86.3926 77.1258C87.8674 77.9886 90.7112 79.6268 94.657 80.0248C96.8428 80.2549 98.619 80.0386 101.099 79.7487C102.687 79.5638 104.264 79.2981 105.825 78.9526C105.534 82.5419 105.244 86.1311 104.958 89.7203C103.658 90.4317 102.291 91.0151 100.878 91.462C98.8399 92.118 96.7174 92.476 94.5765 92.5249C92.098 92.5355 89.6377 92.1007 87.313 91.2411C86.3288 90.8516 85.3658 90.4108 84.4278 89.9205C82.8333 89.1313 81.5403 88.3904 80.6269 87.8498C80.6085 82.6546 80.5886 77.4694 80.5671 72.2942Z"
        fill="#0517F8"
      />
      <path
        d="M94.4247 93.0955C91.9289 93.0847 89.4542 92.6369 87.1128 91.7726C86.1937 91.4135 85.2943 91.0057 84.4186 90.5509L84.1724 90.4289C82.8633 89.7801 81.5702 89.0715 80.3301 88.3237L80.054 88.1557L79.9942 70.748L81.0088 71.9168C82.6329 73.7892 84.5517 75.3839 86.6895 76.638C88.0377 77.4272 90.847 79.07 94.7215 79.4588C96.8451 79.6728 98.6006 79.4588 101.03 79.185C102.599 79.0018 104.157 78.7392 105.699 78.3981L106.458 78.2279L105.512 90.07L105.241 90.2195C103.907 90.9475 102.506 91.5454 101.058 92.0049C98.9647 92.6766 96.7857 93.0431 94.588 93.0932L94.4247 93.0955ZM81.2021 87.5046C82.3295 88.1764 83.5029 88.8115 84.6832 89.3982L84.9317 89.5224C85.7715 89.9574 86.6332 90.349 87.5131 90.6958C89.7728 91.5294 92.1634 91.9518 94.5719 91.9428C96.6549 91.8943 98.7201 91.5456 100.704 90.9075C101.982 90.5025 103.222 89.9835 104.408 89.3568L105.181 79.682C103.853 79.9535 102.505 80.1697 101.157 80.3285C98.6558 80.6184 96.8474 80.8301 94.5995 80.6046C90.4743 80.1881 87.5201 78.4603 86.1005 77.6297C84.2854 76.5671 82.6189 75.269 81.1446 73.769L81.2021 87.5046Z"
        fill="#172B4D"
      />
      <path
        d="M105.62 81.4167L109.847 82.107C110.451 82.2188 111.001 82.5291 111.409 82.9886C111.817 83.4481 112.06 84.0306 112.099 84.6438C112.139 85.2571 111.973 85.866 111.627 86.3742C111.282 86.8823 110.777 87.2607 110.192 87.4494L105.073 88.3559C105.252 86.0444 105.435 83.7313 105.62 81.4167Z"
        fill="#00A5BC"
      />
      <path
        d="M104.433 89.0507L105.096 80.7472L109.978 81.5479C110.698 81.6939 111.35 82.0732 111.833 82.6272C112.316 83.1811 112.603 83.8787 112.65 84.6121C112.696 85.3455 112.5 86.0739 112.091 86.6845C111.682 87.2951 111.083 87.754 110.387 87.99L110.291 88.0154L104.433 89.0507ZM106.143 82.0862L105.698 87.661L110.04 86.8972C110.495 86.738 110.884 86.4336 111.149 86.0309C111.413 85.6282 111.537 85.1496 111.503 84.6691C111.468 84.1886 111.276 83.733 110.956 83.3726C110.636 83.0122 110.207 82.7671 109.734 82.6752L106.143 82.0862Z"
        fill="#172B4D"
      />
      <path
        d="M117.117 97.773C115.932 97.773 114.968 97.5084 114.244 96.9815C112.905 96.006 112.594 94.3379 112.125 91.8117C112.076 91.5402 112.035 91.2871 111.996 91.0455C111.832 90.0401 111.727 89.3752 111.167 88.7724C110.838 88.425 110.434 88.1581 109.985 87.9924L110.399 86.9202C111.011 87.1491 111.561 87.5151 112.01 87.9901C112.803 88.8483 112.955 89.7824 113.128 90.8638C113.167 91.0939 113.206 91.34 113.254 91.6C113.687 93.9376 113.944 95.3457 114.918 96.052C115.608 96.5467 116.636 96.7216 117.998 96.572C118.398 96.5282 118.791 96.4418 119.172 96.3143L116.358 80.0961H115.79C114.874 80.0695 113.959 80.1273 113.054 80.2687C112.378 80.4159 111.356 80.9106 110.321 82.4383L109.368 81.7918C110.615 79.9512 111.929 79.3346 112.819 79.1436C113.804 78.9842 114.802 78.9179 115.799 78.9457C116.259 78.9457 116.618 78.9457 116.866 78.9572H117.327L120.463 97.0322L120.039 97.2093C119.427 97.4682 118.781 97.6364 118.12 97.7086C117.787 97.7485 117.453 97.77 117.117 97.773Z"
        fill="#172B4D"
      />
      <path
        d="M109.191 93.4016C108.675 93.4018 108.177 93.2154 107.788 92.877C107.44 92.5664 107.042 91.982 107.109 90.9558C107.203 89.4971 108.512 88.2386 109.693 87.6864C110.447 87.3321 111.062 87.2999 111.469 87.5967C112.212 88.1374 111.869 89.2533 111.301 91.1031C110.806 92.7136 110.328 93.091 109.92 93.2612C109.689 93.355 109.441 93.4027 109.191 93.4016ZM110.746 88.5469C110.309 88.6334 109.9 88.8245 109.552 89.1037C108.782 89.6674 108.298 90.3852 108.257 91.0295C108.227 91.4896 108.328 91.8163 108.554 92.0188C108.678 92.127 108.828 92.2005 108.99 92.232C109.151 92.2635 109.318 92.2519 109.474 92.1982C109.628 92.1338 109.897 91.7243 110.194 90.7649C110.454 89.9297 110.765 88.9196 110.746 88.5377V88.5469Z"
        fill="#172B4D"
      />
      <path
        d="M121.031 98.1756C120.702 98.1682 120.379 98.0868 120.086 97.9375C119.793 97.7881 119.537 97.5746 119.338 97.3128L120.244 96.6042C120.566 97.016 121.063 97.115 121.323 96.9493C121.4 96.8865 121.463 96.8089 121.509 96.7211C121.556 96.6334 121.584 96.5373 121.592 96.4386L118.629 78.7755C118.402 78.7365 118.17 78.7555 117.952 78.8307C117.76 78.9091 117.586 79.0277 117.442 79.1784C117.299 79.3291 117.189 79.5086 117.12 79.705L116.029 79.3415C116.153 78.98 116.354 78.6502 116.62 78.3751C116.885 78.1 117.207 77.8864 117.564 77.7493C118.145 77.5474 118.78 77.5621 119.351 77.7907L119.65 77.9127L122.747 96.3925V96.4593C122.733 96.7475 122.654 97.0287 122.514 97.2815C122.375 97.5343 122.18 97.752 121.944 97.918C121.671 98.0893 121.354 98.1787 121.031 98.1756Z"
        fill="#172B4D"
      />
      <path
        d="M68.5708 67.948C66.9994 67.948 65.7133 66.4617 65.7133 64.6441C65.7133 62.8265 66.9994 61.3402 68.5708 61.3402C70.1422 61.3402 71.4284 62.8265 71.4284 64.6441C71.4284 66.4617 70.1422 67.948 68.5708 67.948Z"
        fill="#0517F8"
      />
      <path
        d="M68.5708 68.5232C66.6773 68.5232 65.138 66.7839 65.138 64.6441C65.138 62.5044 66.6773 60.765 68.5708 60.765C70.4643 60.765 72.0036 62.5044 72.0036 64.6441C72.0036 66.7839 70.462 68.5232 68.5708 68.5232ZM68.5708 61.9154C67.3123 61.9154 66.2884 63.1394 66.2884 64.6441C66.2884 66.1488 67.3123 67.3729 68.5708 67.3729C69.8293 67.3729 70.8532 66.1488 70.8532 64.6441C70.8532 63.1394 69.8293 61.9154 68.5708 61.9154Z"
        fill="#172B4D"
      />
      <path
        d="M79.6857 113.203L79.671 114.583L114.489 114.954L114.504 113.573L79.6857 113.203Z"
        fill="#172B4D"
      />
      <path
        d="M5.92379 112.421L5.90909 113.801L27.2959 114.029L27.3106 112.648L5.92379 112.421Z"
        fill="#172B4D"
      />
    </g>
    <defs>
      <clipPath id="clip0_1710_25119">
        <rect
          width="118.182"
          height="113.622"
          fill="white"
          transform="translate(5.90909 8.27271)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const DeleteTrashImage = () => (
  <svg
    width="130"
    height="130"
    viewBox="0 0 130 130"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M85.2571 43.5971L81.2275 20.9417L64.5577 9.91301L46.9833 13.0247C46.6113 13.0904 46.2559 13.2288 45.9375 13.432C45.6191 13.6352 45.3438 13.8991 45.1275 14.2088C44.9111 14.5184 44.758 14.8677 44.6767 15.2366C44.5955 15.6055 44.5877 15.9868 44.654 16.3587L49.4149 43.2392L47.8879 43.5193L43.1137 16.6387C43.0122 16.0656 43.0247 15.4781 43.1503 14.9098C43.2759 14.3415 43.5122 13.8035 43.8458 13.3266C44.1793 12.8496 44.6035 12.443 45.0942 12.13C45.5848 11.8169 46.1323 11.6036 46.7055 11.5022L64.8911 8.27271L82.6455 20.0216L86.7841 43.3237L85.2571 43.5971Z"
      fill="#172B4D"
    />
    <rect
      x="33.0909"
      y="43.7273"
      width="67.9545"
      height="9.45454"
      fill="#CDD1FE"
    />
    <rect
      x="30.1364"
      y="60.8636"
      width="11.8182"
      height="5.31818"
      fill="#CDD1FE"
    />
    <rect
      x="91.5909"
      y="60.8636"
      width="11.8182"
      height="5.31818"
      fill="#CDD1FE"
    />
    <path
      d="M81.3831 20.7906C76.6777 21.6796 72.9882 22.1464 71.0278 22.2464C70.361 22.2797 69.2008 22.302 68.2406 21.6307C67.7479 21.287 67.3453 20.8296 67.0671 20.2971C66.9287 20.0309 66.8235 19.7488 66.7537 19.457C66.7292 19.3614 66.7159 19.2858 66.7092 19.2347V19.2125C66.6959 19.1391 66.0157 16.3631 64.3688 9.65515"
      fill="#0517F8"
    />
    <path
      d="M70.5588 23.0377C69.5802 23.0782 68.6134 22.8117 67.7939 22.2753C67.2002 21.8588 66.7147 21.3063 66.378 20.6639C66.2099 20.3415 66.0823 19.9996 65.998 19.6459C65.9757 19.5548 65.958 19.477 65.9491 19.4237V19.3792C65.9068 19.1947 65.4623 17.3655 63.6175 9.85299L65.1289 9.48181C67.0648 17.3677 67.3982 18.7435 67.4627 19.0169L67.4805 19.1014C67.4805 19.1414 67.5005 19.2036 67.5183 19.2792C67.6868 19.9765 68.1051 20.5877 68.694 20.9973C69.3919 21.4863 70.2499 21.5129 70.9967 21.4729C73.2193 21.3596 77.0578 20.8195 81.2475 20.0305L81.5342 21.5596C77.2801 22.362 73.3705 22.911 71.0878 23.0288C70.9056 23.031 70.7366 23.0377 70.5588 23.0377ZM65.958 19.4437C65.9573 19.4526 65.9573 19.4615 65.958 19.4703V19.4437Z"
      fill="#172B4D"
    />
    <path
      d="M99.2575 54.3213H34.4654C33.713 54.3201 32.9917 54.0207 32.4597 53.4886C31.9277 52.9566 31.6283 52.2354 31.6271 51.483V45.2596C31.6283 44.5072 31.9277 43.7859 32.4597 43.2539C32.9917 42.7219 33.713 42.4224 34.4654 42.4213H99.2575C100.01 42.4224 100.731 42.7219 101.263 43.2539C101.795 43.7859 102.095 44.5072 102.096 45.2596V51.483C102.095 52.2354 101.795 52.9566 101.263 53.4886C100.731 54.0207 100.01 54.3201 99.2575 54.3213ZM34.4654 43.986C34.1255 43.9866 33.7996 44.1219 33.5592 44.3623C33.3188 44.6027 33.1835 44.9285 33.1829 45.2685V51.4918C33.1835 51.8318 33.3188 52.1577 33.5592 52.398C33.7996 52.6384 34.1255 52.7737 34.4654 52.7743H99.2575C99.5975 52.7737 99.9233 52.6384 100.164 52.398C100.404 52.1577 100.539 51.8318 100.54 51.4918V45.2685C100.539 44.9285 100.404 44.6027 100.164 44.3623C99.9233 44.1219 99.5975 43.9866 99.2575 43.986H34.4654Z"
      fill="#172B4D"
    />
    <path
      d="M94.55 121.727H38.9084L36.608 66.3325L38.1616 66.268L40.4021 120.171H93.063L95.459 66.1102L97.0126 66.1791L94.55 121.727Z"
      fill="#172B4D"
    />
    <path
      d="M36.3813 60.887L36.0435 52.7655H97.6061L97.2638 60.5069L95.7102 60.438L95.9813 54.3213H37.666L37.9349 60.8226L36.3813 60.887Z"
      fill="#172B4D"
    />
    <path
      d="M66.8626 114.117C65.7647 114.116 64.7122 113.679 63.9359 112.903C63.1596 112.127 62.723 111.074 62.7218 109.976V65.8257C62.7469 64.7444 63.194 63.7159 63.9676 62.96C64.7412 62.2041 65.7799 61.7809 66.8615 61.7809C67.943 61.7809 68.9817 62.2041 69.7553 62.96C70.5289 63.7159 70.9761 64.7444 71.0011 65.8257V109.976C70.9999 111.074 70.5636 112.126 69.7878 112.902C69.012 113.678 67.96 114.115 66.8626 114.117ZM66.8626 63.243C66.1776 63.2436 65.5208 63.5158 65.0362 64C64.5516 64.4842 64.2788 65.1407 64.2776 65.8257V109.976C64.2675 110.322 64.3268 110.666 64.4521 110.988C64.5774 111.311 64.7661 111.605 65.007 111.853C65.248 112.101 65.5362 112.298 65.8548 112.433C66.1733 112.568 66.5156 112.637 66.8615 112.637C67.2073 112.637 67.5496 112.568 67.8681 112.433C68.1867 112.298 68.475 112.101 68.7159 111.853C68.9568 111.605 69.1455 111.311 69.2708 110.988C69.3961 110.666 69.4554 110.322 69.4453 109.976V65.8257C69.4441 65.1411 69.1716 64.4849 68.6875 64.0008C68.2034 63.5167 67.5472 63.2442 66.8626 63.243Z"
      fill="#172B4D"
    />
    <path
      d="M81.8942 114.117C80.7968 114.115 79.7448 113.678 78.969 112.902C78.1932 112.126 77.7569 111.074 77.7557 109.976V65.8257C77.7557 64.7281 78.1917 63.6754 78.9679 62.8993C79.744 62.1232 80.7966 61.6871 81.8942 61.6871C82.9919 61.6871 84.0445 62.1232 84.8206 62.8993C85.5968 63.6754 86.0328 64.7281 86.0328 65.8257V109.976C86.0316 111.074 85.5953 112.126 84.8195 112.902C84.0437 113.678 82.9917 114.115 81.8942 114.117ZM81.8942 63.243C81.2096 63.2442 80.5534 63.5166 80.0693 64.0007C79.5852 64.4848 79.3127 65.1411 79.3116 65.8257V109.976C79.3116 110.661 79.5837 111.318 80.068 111.802C80.5524 112.287 81.2093 112.559 81.8942 112.559C82.5792 112.559 83.2361 112.287 83.7205 111.802C84.2048 111.318 84.4769 110.661 84.4769 109.976V65.8257C84.4758 65.1411 84.2033 64.4848 83.7192 64.0007C83.2351 63.5166 82.5789 63.2442 81.8942 63.243Z"
      fill="#172B4D"
    />
    <path
      d="M51.3975 114.117C50.3 114.115 49.248 113.678 48.4722 112.902C47.6964 112.126 47.2601 111.074 47.2589 109.976V65.8257C47.2589 64.7281 47.6949 63.6754 48.4711 62.8993C49.2472 62.1232 50.2999 61.6871 51.3975 61.6871C52.4951 61.6871 53.5477 62.1232 54.3239 62.8993C55.1 63.6754 55.536 64.7281 55.536 65.8257V109.976C55.5348 111.074 55.0985 112.126 54.3227 112.902C53.5469 113.678 52.4949 114.115 51.3975 114.117ZM51.3975 63.243C50.7128 63.2442 50.0566 63.5166 49.5725 64.0007C49.0884 64.4848 48.8159 65.1411 48.8148 65.8257V109.976C48.8148 110.661 49.0869 111.318 49.5712 111.802C50.0556 112.287 50.7125 112.559 51.3975 112.559C52.0824 112.559 52.7394 112.287 53.2237 111.802C53.7081 111.318 53.9802 110.661 53.9802 109.976V65.8257C53.979 65.1411 53.7065 64.4848 53.2224 64.0007C52.7383 63.5166 52.0821 63.2442 51.3975 63.243Z"
      fill="#172B4D"
    />
    <path
      d="M43.087 67.2238H32.3695C31.8836 67.2358 31.4003 67.1505 30.948 66.9728C30.4957 66.7952 30.0834 66.5289 29.7356 66.1895C29.3877 65.8502 29.1113 65.4446 28.9226 64.9968C28.7338 64.549 28.6366 64.0679 28.6366 63.582C28.6366 63.096 28.7338 62.615 28.9226 62.1672C29.1113 61.7193 29.3877 61.3138 29.7356 60.9744C30.0834 60.6351 30.4957 60.3688 30.948 60.1911C31.4003 60.0135 31.8836 59.9282 32.3695 59.9402H43.087V67.2238ZM32.3695 61.496C32.0898 61.4869 31.8112 61.5341 31.5501 61.6348C31.2891 61.7355 31.0509 61.8877 30.8499 62.0823C30.6488 62.2769 30.4889 62.51 30.3798 62.7676C30.2706 63.0252 30.2143 63.3022 30.2143 63.582C30.2143 63.8618 30.2706 64.1387 30.3798 64.3964C30.4889 64.654 30.6488 64.887 30.8499 65.0816C31.0509 65.2762 31.2891 65.4284 31.5501 65.5291C31.8112 65.6299 32.0898 65.6771 32.3695 65.6679H41.5312V61.496H32.3695Z"
      fill="#172B4D"
    />
    <path
      d="M101.411 67.2238H90.6915V59.9402H101.411C102.362 59.9637 103.265 60.3577 103.929 61.0381C104.593 61.7185 104.964 62.6314 104.964 63.582C104.964 64.5326 104.593 65.4455 103.929 66.1259C103.265 66.8063 102.362 67.2003 101.411 67.2238ZM92.2473 65.6679H101.411C101.953 65.6502 102.466 65.4227 102.843 65.0335C103.22 64.6442 103.43 64.1237 103.43 63.582C103.43 63.0402 103.22 62.5197 102.843 62.1305C102.466 61.7412 101.953 61.5137 101.411 61.496H92.2473V65.6679Z"
      fill="#172B4D"
    />
    <path
      d="M101.82 37.5004C101.751 37.5009 101.682 37.4904 101.616 37.4693C101.448 37.4152 101.308 37.2966 101.227 37.1395C101.146 36.9824 101.131 36.7996 101.185 36.6313L102.925 31.2059C102.979 31.0373 103.098 30.897 103.255 30.8159C103.412 30.7349 103.595 30.7196 103.764 30.7736C103.933 30.8275 104.073 30.9462 104.154 31.1036C104.235 31.2609 104.25 31.444 104.196 31.6126L102.454 37.0381C102.41 37.1722 102.326 37.2891 102.212 37.3722C102.098 37.4552 101.961 37.5001 101.82 37.5004Z"
      fill="#172B4D"
    />
    <path
      d="M107.372 41.6967C107.229 41.6968 107.09 41.6507 106.975 41.5653C106.86 41.4799 106.775 41.3598 106.734 41.2226C106.692 41.0854 106.696 40.9386 106.745 40.8038C106.793 40.669 106.884 40.5535 107.003 40.4743L111.747 37.3181C111.819 37.2697 111.901 37.2361 111.987 37.2192C112.073 37.2024 112.162 37.2026 112.247 37.22C112.333 37.2373 112.415 37.2714 112.488 37.3203C112.56 37.3691 112.623 37.4319 112.671 37.5048C112.72 37.5778 112.753 37.6596 112.77 37.7456C112.787 37.8315 112.787 37.9199 112.769 38.0058C112.752 38.0917 112.718 38.1733 112.669 38.246C112.62 38.3186 112.557 38.381 112.484 38.4294L107.741 41.5856C107.632 41.6581 107.504 41.6968 107.372 41.6967Z"
      fill="#172B4D"
    />
    <path
      d="M106.054 38.0183C105.93 38.0181 105.808 37.9832 105.703 37.9176C105.598 37.8519 105.513 37.7581 105.458 37.6467C105.403 37.5354 105.38 37.4108 105.392 37.2872C105.404 37.1636 105.45 37.0457 105.525 36.947L108.977 32.415C109.084 32.2742 109.243 32.1817 109.419 32.1579C109.594 32.1341 109.772 32.1811 109.913 32.2883C110.054 32.3956 110.146 32.5545 110.17 32.73C110.194 32.9055 110.147 33.0832 110.04 33.2241L106.586 37.756C106.523 37.8377 106.443 37.9039 106.351 37.9494C106.259 37.9949 106.157 38.0185 106.054 38.0183Z"
      fill="#172B4D"
    />
    <path
      d="M47.8038 100.758C53.3407 95.2208 53.3407 86.2436 47.8038 80.7067C42.2669 75.1697 33.2897 75.1697 27.7528 80.7067C22.2159 86.2436 22.2159 95.2208 27.7528 100.758C33.2897 106.295 42.2669 106.295 47.8038 100.758Z"
      fill="#FDE5F0"
    />
    <path
      d="M37.7772 105.689C34.8193 105.688 31.9279 104.811 29.4686 103.167C27.0094 101.523 25.0928 99.1873 23.9611 96.4544C22.8295 93.7215 22.5336 90.7144 23.1109 87.8133C23.6883 84.9123 25.1129 82.2476 27.2046 80.1561C29.2964 78.0647 31.9613 76.6405 34.8624 76.0636C37.7636 75.4867 40.7707 75.783 43.5034 76.9151C46.2362 78.0471 48.5718 79.9641 50.2151 82.4236C51.8585 84.883 52.7356 87.7746 52.7356 90.7325C52.7314 94.6982 51.1541 98.5003 48.3497 101.304C45.5453 104.108 41.7429 105.685 37.7772 105.689ZM37.7772 77.3322C35.127 77.3327 32.5364 78.1189 30.333 79.5917C28.1296 81.0644 26.4124 83.1574 25.3985 85.606C24.3846 88.0546 24.1196 90.7489 24.6369 93.3481C25.1542 95.9474 26.4306 98.3349 28.3048 100.209C30.1789 102.083 32.5666 103.359 35.166 103.875C37.7653 104.392 40.4596 104.127 42.908 103.113C45.3565 102.098 47.4492 100.381 48.9215 98.1771C50.3939 95.9734 51.1797 93.3827 51.1797 90.7325C51.1756 87.1794 49.7622 83.773 47.2495 81.2608C44.7369 78.7486 41.3303 77.3357 37.7772 77.3322Z"
      fill="#172B4D"
    />
    <path
      d="M44.7047 95.3349L33.8012 85.0886C33.2711 84.5905 32.4375 84.6164 31.9394 85.1465C31.4412 85.6766 31.4671 86.5101 31.9972 87.0083L42.9008 97.2546C43.4309 97.7527 44.2644 97.7268 44.7626 97.1967C45.2607 96.6666 45.2348 95.833 44.7047 95.3349Z"
      fill="#EE0064"
    />
    <path
      d="M42.8261 84.5597L32.2263 95.0711C31.7031 95.59 31.6995 96.4348 32.2184 96.9581C32.7373 97.4813 33.5821 97.4849 34.1054 96.966L44.7052 86.4546C45.2285 85.9357 45.232 85.0909 44.7131 84.5676C44.1942 84.0443 43.3494 84.0408 42.8261 84.5597Z"
      fill="#EE0064"
    />
  </svg>
);

export const RemoveGhostImage = () => (
  <svg
    width="130"
    height="130"
    viewBox="0 0 130 130"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_1710_24481)">
      <path
        d="M29.3449 56.5944H29.2831L23.6462 56.0598C23.4705 56.0434 23.3085 55.9579 23.1958 55.822C23.0831 55.6862 23.0291 55.5111 23.0455 55.3354C23.0619 55.1596 23.1474 54.9976 23.2833 54.8849C23.4191 54.7722 23.5942 54.7182 23.7699 54.7346L29.4068 55.2691C29.5825 55.2773 29.7478 55.355 29.8663 55.485C29.9847 55.6151 30.0467 55.7869 30.0385 55.9627C30.0303 56.1384 29.9526 56.3037 29.8225 56.4221C29.6925 56.5406 29.5207 56.6026 29.3449 56.5944V56.5944Z"
        fill="#172B4D"
      />
      <path
        d="M32.234 50.3104C32.0619 50.3108 31.8963 50.2443 31.7724 50.1248L27.6972 46.1954C27.5706 46.0732 27.4978 45.9058 27.4947 45.73C27.4916 45.5542 27.5585 45.3843 27.6806 45.2578C27.8027 45.1312 27.9701 45.0584 28.146 45.0553C28.3218 45.0522 28.4917 45.119 28.6182 45.2412L32.6934 49.1706C32.7885 49.2623 32.8541 49.3802 32.8816 49.5094C32.9092 49.6385 32.8975 49.773 32.8482 49.8955C32.7988 50.0179 32.714 50.1229 32.6045 50.1968C32.4951 50.2707 32.3661 50.3103 32.234 50.3104V50.3104Z"
        fill="#172B4D"
      />
      <path
        d="M28.9429 52.3732C28.8468 52.373 28.7518 52.3519 28.6646 52.3114L23.5292 49.9281C23.3695 49.854 23.2459 49.7195 23.1854 49.5542C23.1249 49.3889 23.1326 49.2064 23.2067 49.0468C23.2808 48.8871 23.4153 48.7635 23.5806 48.703C23.7458 48.6425 23.9284 48.6502 24.088 48.7243L29.2301 51.1098C29.3669 51.1735 29.478 51.282 29.5448 51.4173C29.6116 51.5527 29.6301 51.7068 29.5974 51.8542C29.5647 52.0016 29.4826 52.1334 29.3648 52.2277C29.247 52.3221 29.1005 52.3734 28.9496 52.3732H28.9429Z"
        fill="#172B4D"
      />
      <path
        d="M29.4509 70.6047C28.8482 70.6039 28.2502 70.4977 27.6839 70.2911C27.0638 70.0727 26.4956 69.7286 26.0147 69.2803C25.5338 68.8321 25.1507 68.2893 24.8895 67.686C24.6282 67.0828 24.4943 66.4321 24.4963 65.7747C24.4982 65.1173 24.636 64.4674 24.9008 63.8657C25.4402 62.6251 26.437 61.6402 27.6839 61.1158L40.274 55.6821L40.8859 57.1002L28.2958 62.536C27.4158 62.9065 26.7125 63.6018 26.3321 64.4775C26.1504 64.8852 26.0552 65.3261 26.0525 65.7724C26.0499 66.2186 26.1398 66.6606 26.3167 67.0704C26.4935 67.4802 26.7535 67.8488 27.08 68.153C27.4066 68.4572 27.7927 68.6903 28.214 68.8377C29.1123 69.1623 30.1012 69.1283 30.975 68.7427L42.4674 63.7796L43.0792 65.1976L31.5758 70.163C30.9049 70.453 30.1819 70.6033 29.4509 70.6047Z"
        fill="#172B4D"
      />
      <path
        d="M54.8741 71.7577C54.2713 71.7568 53.6733 71.6507 53.1071 71.444C52.4876 71.2251 51.92 70.8805 51.44 70.4318C50.96 69.9832 50.5778 69.4401 50.3175 68.8368C50.0573 68.2335 49.9245 67.583 49.9275 66.9259C49.9305 66.2689 50.0693 65.6195 50.3351 65.0186C50.8655 63.7835 51.8499 62.7992 53.085 62.2687L65.6751 56.8329C65.8634 56.7517 66.0763 56.7487 66.2668 56.8245C66.4574 56.9003 66.61 57.0487 66.6912 57.2371C66.7723 57.4254 66.7753 57.6383 66.6995 57.8288C66.6237 58.0194 66.4753 58.172 66.2869 58.2531L53.6968 63.6867C52.8173 64.0578 52.1142 64.753 51.7332 65.6283C51.5508 66.036 51.455 66.4771 51.452 66.9238C51.449 67.3704 51.5388 67.8128 51.7157 68.2229C51.8926 68.6331 52.1528 69.002 52.4798 69.3062C52.8067 69.6105 53.1934 69.8436 53.6151 69.9906C54.5128 70.3142 55.5006 70.2802 56.3739 69.8957L67.8839 64.9303C68.0722 64.8492 68.2851 64.8462 68.4756 64.922C68.6662 64.9978 68.8188 65.1462 68.8999 65.3345C68.9811 65.5228 68.9841 65.7357 68.9083 65.9262C68.8325 66.1168 68.6841 66.2694 68.4957 66.3505L56.9946 71.3137C56.3254 71.6047 55.6038 71.7558 54.8741 71.7577V71.7577Z"
        fill="#172B4D"
      />
      <path
        d="M80.4409 29.9188H78.8947C78.8947 24.5879 76.777 19.4754 73.0076 15.7059C69.2381 11.9364 64.1256 9.81877 58.7947 9.81877C53.4639 9.81877 48.3514 11.9364 44.5819 15.7059C40.8124 19.4754 38.6947 24.5879 38.6947 29.9188H37.1486C37.1486 24.1764 39.4297 18.6692 43.4902 14.6087C47.5507 10.5482 53.0579 8.26709 58.8002 8.26709C64.5426 8.26709 70.0498 10.5482 74.1103 14.6087C78.1708 18.6692 80.4519 24.1764 80.4519 29.9188H80.4409Z"
        fill="#172B4D"
      />
      <path
        d="M71.418 99.3699C69.9977 99.3699 68.1666 98.6388 66.6448 97.879C62.7728 95.9441 56.8576 91.7761 50.6796 82.9078C48.4871 79.7867 46.5581 76.4887 44.9125 73.0477C38.9885 59.9584 36.3711 45.4356 37.1397 29.8857L38.6859 29.963C37.9261 45.2633 40.4949 59.5432 46.3283 72.4094C46.4078 72.5839 54.4434 90.0532 67.3427 96.4963C70.0684 97.8569 71.7139 98.0159 72.0099 97.6934C72.434 97.2318 71.2346 94.8839 70.5897 93.6204C69.3505 91.1908 68.1821 88.9113 69.3527 87.5706C70.7597 85.9603 74.1657 87.2635 78.4706 89.1322C81.517 90.4763 84.4464 92.0714 87.2285 93.9009C88.4654 94.6851 90.0071 95.668 90.3495 95.5288C90.3495 95.5288 90.5858 95.2947 90.3495 94.0026C90.0545 92.7124 89.6857 91.4403 89.2451 90.1924C88.0722 86.6031 86.9656 83.2148 88.7194 81.8807C89.371 81.3859 90.0822 81.1916 96.205 83.3849C98.1929 84.0983 99.6198 84.6594 100.662 85.0702C101.4 85.3595 102.065 85.6224 102.467 85.7439C102.122 84.6108 100.07 81.651 98.0759 78.7796C96.4568 76.4427 94.4402 73.5381 92.3551 70.2801C91.8272 69.454 91.3015 68.6478 90.7868 67.8505C87.4118 62.6443 84.2245 57.7276 82.3537 51.8058C80.133 44.7264 78.9697 37.3579 78.9013 29.9388L80.4475 29.9211C80.5142 37.1902 81.6529 44.4097 83.8269 51.3464C85.6359 57.0693 88.6244 61.6791 92.0834 67.0155C92.6032 67.8136 93.1274 68.6235 93.6561 69.4452C95.7279 72.6767 97.7335 75.5702 99.3481 77.896C103.244 83.5108 104.857 85.8344 103.691 87.0073C103.028 87.6699 102.295 87.3718 100.097 86.5059C99.0632 86.0995 97.6496 85.5429 95.6793 84.8383C90.9812 83.1552 89.8923 83.0712 89.6427 83.1176C88.7835 83.8156 89.9431 87.3607 90.7117 89.7109C91.8161 93.0925 92.6997 95.763 91.2441 96.779C90.1397 97.5653 88.7305 96.673 86.4024 95.1953C83.6884 93.4087 80.8298 91.852 77.8566 90.5414C76.4319 89.9207 74.6583 89.152 73.1607 88.7213C71.0579 88.1139 70.5919 88.5005 70.5101 88.5778C70.0286 89.1277 71.2368 91.4934 71.9591 92.907C73.1607 95.2549 74.2938 97.4748 73.143 98.7272C72.7432 99.1888 72.1314 99.3699 71.418 99.3699Z"
        fill="#172B4D"
      />
      <path
        d="M45.1466 43.5338C44.4061 43.5332 43.6961 43.2389 43.1723 42.7155C42.6485 42.1921 42.3537 41.4823 42.3525 40.7419V34.2767C42.342 33.9033 42.4065 33.5316 42.5421 33.1836C42.6777 32.8355 42.8818 32.5182 43.1422 32.2504C43.4026 31.9826 43.714 31.7697 44.0581 31.6243C44.4022 31.479 44.772 31.4041 45.1455 31.4041C45.519 31.4041 45.8888 31.479 46.2329 31.6243C46.577 31.7697 46.8884 31.9826 47.1488 32.2504C47.4092 32.5182 47.6133 32.8355 47.7489 33.1836C47.8845 33.5316 47.949 33.9033 47.9385 34.2767V40.7419C47.9374 41.482 47.6428 42.1914 47.1195 42.7147C46.5962 43.2381 45.8867 43.5326 45.1466 43.5338V43.5338ZM45.1466 33.0288C44.8158 33.0293 44.4987 33.161 44.2648 33.3949C44.0309 33.6288 43.8992 33.9459 43.8986 34.2767V40.7419C43.8916 40.91 43.9186 41.0779 43.978 41.2353C44.0375 41.3928 44.1282 41.5365 44.2447 41.658C44.3612 41.7795 44.501 41.8762 44.6558 41.9422C44.8106 42.0083 44.9772 42.0423 45.1455 42.0423C45.3138 42.0423 45.4804 42.0083 45.6352 41.9422C45.79 41.8762 45.9298 41.7795 46.0463 41.658C46.1628 41.5365 46.2535 41.3928 46.313 41.2353C46.3724 41.0779 46.3994 40.91 46.3924 40.7419V34.2767C46.3924 33.9461 46.2612 33.629 46.0276 33.3951C45.7941 33.1611 45.4772 33.0293 45.1466 33.0288Z"
        fill="#172B4D"
      />
      <path
        d="M54.1077 43.5338C53.368 43.532 52.6591 43.2372 52.1362 42.714C51.6134 42.1907 51.3191 41.4816 51.318 40.7419V34.2767C51.3075 33.9033 51.3719 33.5316 51.5076 33.1836C51.6432 32.8355 51.8473 32.5182 52.1077 32.2504C52.3681 31.9826 52.6795 31.7697 53.0236 31.6243C53.3677 31.479 53.7374 31.4041 54.111 31.4041C54.4845 31.4041 54.8543 31.479 55.1984 31.6243C55.5425 31.7697 55.8539 31.9826 56.1143 32.2504C56.3747 32.5182 56.5787 32.8355 56.7144 33.1836C56.85 33.5316 56.9145 33.9033 56.904 34.2767V40.7419C56.9028 41.4827 56.6077 42.1928 56.0834 42.7163C55.5591 43.2398 54.8485 43.5338 54.1077 43.5338ZM54.1077 33.0288C53.7769 33.0293 53.4598 33.161 53.2259 33.3949C52.992 33.6288 52.8603 33.9459 52.8597 34.2767V40.7419C52.8526 40.91 52.8796 41.0779 52.9391 41.2353C52.9986 41.3928 53.0893 41.5365 53.2058 41.658C53.3222 41.7795 53.4621 41.8762 53.6169 41.9422C53.7717 42.0083 53.9383 42.0423 54.1066 42.0423C54.2749 42.0423 54.4414 42.0083 54.5962 41.9422C54.7511 41.8762 54.8909 41.7795 55.0074 41.658C55.1239 41.5365 55.2146 41.3928 55.274 41.2353C55.3335 41.0779 55.3605 40.91 55.3534 40.7419V34.2767C55.3534 33.9461 55.2223 33.629 54.9887 33.3951C54.7551 33.1611 54.4383 33.0293 54.1077 33.0288V33.0288Z"
        fill="#172B4D"
      />
      <path
        d="M72.5179 120.954C90.9711 120.954 105.93 117.792 105.93 113.89C105.93 109.989 90.9711 106.827 72.5179 106.827C54.0648 106.827 39.1056 109.989 39.1056 113.89C39.1056 117.792 54.0648 120.954 72.5179 120.954Z"
        fill="#CDD1FE"
      />
      <path
        d="M72.5224 121.727C63.5458 121.727 55.0994 120.985 48.7381 119.64C41.8379 118.182 38.3391 116.247 38.3391 113.897C38.3391 111.547 41.8379 109.603 48.7381 108.154C55.0994 106.809 63.537 106.069 72.5224 106.069C81.5077 106.069 89.9475 106.809 96.3088 108.154C103.209 109.612 106.708 111.547 106.708 113.897C106.708 116.247 103.209 118.189 96.3088 119.64C89.9431 120.985 81.4945 121.727 72.5224 121.727ZM72.5224 107.591C52.3009 107.591 39.8853 111.253 39.8853 113.882C39.8853 116.51 52.2965 120.181 72.5224 120.181C92.7483 120.181 105.162 116.517 105.162 113.89C105.162 111.264 92.7416 107.6 72.5224 107.6V107.591Z"
        fill="#172B4D"
      />
      <path
        d="M96.8492 54.8947C102.386 49.3577 102.386 40.3806 96.8492 34.8436C91.3123 29.3067 82.3352 29.3067 76.7982 34.8436C71.2613 40.3806 71.2613 49.3577 76.7982 54.8947C82.3352 60.4316 91.3123 60.4316 96.8492 54.8947Z"
        fill="#FDE5F0"
      />
      <path
        d="M86.8227 59.8256C83.8647 59.8252 80.9733 58.9476 78.5141 57.304C76.0549 55.6603 74.1382 53.3243 73.0066 50.5914C71.8749 47.8585 71.5791 44.8514 72.1564 41.9503C72.7337 39.0492 74.1583 36.3845 76.2501 34.2931C78.3418 32.2017 81.0068 30.7775 83.9079 30.2006C86.8091 29.6236 89.8161 29.92 92.5489 31.052C95.2816 32.1841 97.6173 34.101 99.2606 36.5605C100.904 39.02 101.781 41.9115 101.781 44.8695C101.777 48.8352 100.2 52.6373 97.3951 55.4413C94.5907 58.2453 90.7884 59.8221 86.8227 59.8256V59.8256ZM86.8227 31.4692C84.1724 31.4696 81.5818 32.2559 79.3785 33.7286C77.1751 35.2013 75.4579 37.2943 74.444 39.7429C73.4301 42.1916 73.165 44.8858 73.6824 47.4851C74.1997 50.0843 75.4761 52.4718 77.3502 54.3457C79.2244 56.2195 81.6121 57.4956 84.2114 58.0124C86.8108 58.5293 89.505 58.2638 91.9535 57.2495C94.4019 56.2352 96.4946 54.5177 97.967 52.314C99.4393 50.1104 100.225 47.5197 100.225 44.8695C100.221 41.3164 98.8076 37.91 96.295 35.3978C93.7824 32.8856 90.3758 31.4727 86.8227 31.4692Z"
        fill="#172B4D"
      />
      <path
        d="M93.1461 43.5747L81.8441 43.7112C81.1072 43.7201 80.5171 44.3246 80.526 45.0615C80.5349 45.7984 81.1394 46.3885 81.8763 46.3796L93.1784 46.2431C93.9152 46.2342 94.5054 45.6296 94.4965 44.8928C94.4876 44.1559 93.883 43.5658 93.1461 43.5747Z"
        fill="#EE0064"
      />
    </g>
    <defs>
      <clipPath id="clip0_1710_24481">
        <rect
          width="83.658"
          height="113.455"
          fill="white"
          transform="translate(23.0455 8.27271)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const NoUserAssignedIcon = () => (
  <svg
    width="170"
    height="79"
    viewBox="0 0 170 79"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    className="flex-shrink-0"
  >
    <g clipPath="url(#clip0_2161_2959)">
      <path
        d="M135.813 66.3763C147.771 66.3763 157.464 56.6828 157.464 44.7253C157.464 32.7677 147.771 23.0742 135.813 23.0742C123.856 23.0742 114.162 32.7677 114.162 44.7253C114.162 56.6828 123.856 66.3763 135.813 66.3763Z"
        fill="#D1D5DB"
      />
      <path
        d="M135.812 67.5086C131.306 67.5086 126.901 66.1724 123.155 63.669C119.408 61.1655 116.488 57.6073 114.764 53.4442C113.039 49.2811 112.588 44.7002 113.467 40.2807C114.346 35.8612 116.516 31.8017 119.702 28.6154C122.889 25.4291 126.948 23.2593 131.368 22.3802C135.787 21.5011 140.368 21.9523 144.531 23.6767C148.694 25.4011 152.252 28.3212 154.756 32.0679C157.259 35.8145 158.596 40.2194 158.596 44.7255C158.589 50.7658 156.186 56.5568 151.915 60.828C147.644 65.0992 141.853 67.5018 135.812 67.5086ZM135.812 24.2065C131.754 24.2065 127.787 25.4099 124.413 27.6646C121.038 29.9192 118.408 33.1238 116.855 36.8732C115.302 40.6226 114.896 44.7483 115.688 48.7286C116.479 52.7089 118.434 56.365 121.303 59.2346C124.173 62.1043 127.829 64.0585 131.809 64.8503C135.79 65.642 139.915 65.2356 143.665 63.6826C147.414 62.1296 150.619 59.4996 152.873 56.1253C155.128 52.7509 156.331 48.7838 156.331 44.7255C156.325 39.2854 154.162 34.0697 150.315 30.223C146.468 26.3762 141.253 24.2125 135.812 24.2065Z"
        fill="#172B4D"
      />
      <path
        d="M34.1876 78.9484C27.4259 78.9484 20.8161 76.9434 15.194 73.1868C9.57188 69.4302 5.18997 64.0908 2.60239 57.8439C0.014813 51.5969 -0.662216 44.7229 0.65692 38.0912C1.97606 31.4594 5.23211 25.3678 10.0133 20.5866C14.7946 15.8054 20.8862 12.5493 27.5179 11.2302C34.1497 9.91103 41.0237 10.5881 47.2706 13.1756C53.5176 15.7632 58.857 20.1451 62.6136 25.7672C66.3701 31.3894 68.3752 37.9992 68.3752 44.7608C68.3649 53.8248 64.7597 62.5146 58.3505 68.9238C51.9414 75.333 43.2516 78.9382 34.1876 78.9484ZM34.1876 12.8373C27.8737 12.8373 21.7017 14.7096 16.4519 18.2174C11.2021 21.7252 7.11034 26.711 4.69413 32.5443C2.27791 38.3775 1.64572 44.7963 2.8775 50.9888C4.10927 57.1814 7.14969 62.8696 11.6143 67.3342C16.0789 71.7988 21.7671 74.8392 27.9596 76.071C34.1522 77.3027 40.571 76.6705 46.4042 74.2543C52.2375 71.8381 57.2232 67.7464 60.731 62.4966C64.2388 57.2468 66.1111 51.0747 66.1111 44.7608C66.1017 36.2971 62.7353 28.1827 56.7505 22.1979C50.7658 16.2131 42.6514 12.8468 34.1876 12.8373Z"
        fill="#172B4D"
      />
      <path
        d="M135.813 78.9484C129.051 78.9484 122.441 76.9434 116.819 73.1868C111.197 69.4302 106.815 64.0908 104.227 57.8439C101.64 51.5969 100.963 44.7229 102.282 38.0912C103.601 31.4594 106.857 25.3678 111.638 20.5866C116.42 15.8054 122.511 12.5493 129.143 11.2302C135.775 9.91103 142.649 10.5881 148.896 13.1756C155.143 15.7632 160.482 20.1451 164.239 25.7672C167.995 31.3894 170 37.9992 170 44.7608C169.99 53.8248 166.385 62.5146 159.976 68.9238C153.566 75.333 144.877 78.9382 135.813 78.9484ZM135.813 12.8373C129.499 12.8373 123.327 14.7096 118.077 18.2174C112.827 21.7252 108.735 26.711 106.319 32.5443C103.903 38.3775 103.271 44.7963 104.502 50.9888C105.734 57.1814 108.775 62.8696 113.239 67.3342C117.704 71.7988 123.392 74.8392 129.585 76.071C135.777 77.3027 142.196 76.6705 148.029 74.2543C153.862 71.8381 158.848 67.7464 162.356 62.4966C165.864 57.2468 167.736 51.0747 167.736 44.7608C167.727 36.2971 164.36 28.1827 158.376 22.1979C152.391 16.2131 144.276 12.8468 135.813 12.8373Z"
        fill="#172B4D"
      />
      <path
        d="M34.1882 66.3763C46.1457 66.3763 55.8392 56.6828 55.8392 44.7253C55.8392 32.7677 46.1457 23.0742 34.1882 23.0742C22.2306 23.0742 12.5371 32.7677 12.5371 44.7253C12.5371 56.6828 22.2306 66.3763 34.1882 66.3763Z"
        fill="#D1D5DB"
      />
      <path
        d="M34.1874 67.5086C29.6813 67.5086 25.2765 66.1724 21.5298 63.669C17.7831 61.1655 14.863 57.6073 13.1386 53.4442C11.4142 49.2811 10.963 44.7002 11.8421 40.2807C12.7212 35.8612 14.8911 31.8017 18.0773 28.6154C21.2636 25.4291 25.3232 23.2593 29.7427 22.3802C34.1621 21.5011 38.7431 21.9523 42.9061 23.6767C47.0692 25.4011 50.6274 28.3212 53.1309 32.0679C55.6343 35.8145 56.9705 40.2194 56.9705 44.7255C56.9637 50.7658 54.5611 56.5568 50.2899 60.828C46.0188 65.0992 40.2278 67.5018 34.1874 67.5086ZM34.1874 24.2065C30.1291 24.2065 26.162 25.4099 22.7877 27.6646C19.4133 29.9192 16.7833 33.1238 15.2303 36.8732C13.6773 40.6226 13.2709 44.7483 14.0627 48.7286C14.8544 52.7089 16.8086 56.365 19.6783 59.2346C22.5479 62.1043 26.204 64.0585 30.1844 64.8503C34.1647 65.642 38.2903 65.2356 42.0397 63.6826C45.7891 62.1296 48.9937 59.4996 51.2484 56.1253C53.503 52.7509 54.7064 48.7838 54.7064 44.7255C54.7004 39.2854 52.5367 34.0697 48.6899 30.223C44.8432 26.3762 39.6276 24.2125 34.1874 24.2065Z"
        fill="#172B4D"
      />
      <path
        d="M26.732 52.2198C30.8512 52.2198 34.1905 48.8805 34.1905 44.7613C34.1905 40.642 30.8512 37.3027 26.732 37.3027C22.6127 37.3027 19.2734 40.642 19.2734 44.7613C19.2734 48.8805 22.6127 52.2198 26.732 52.2198Z"
        fill="#0517F8"
      />
      <path
        d="M128.617 52.2198C132.736 52.2198 136.075 48.8805 136.075 44.7613C136.075 40.642 132.736 37.3027 128.617 37.3027C124.497 37.3027 121.158 40.642 121.158 44.7613C121.158 48.8805 124.497 52.2198 128.617 52.2198Z"
        fill="#0517F8"
      />
      <path
        d="M84.9221 34.3041C82.607 34.3041 80.3439 33.6176 78.419 32.3314C76.4941 31.0453 74.9938 29.2171 74.1078 27.0783C73.2219 24.9394 72.9901 22.5859 73.4417 20.3153C73.8934 18.0447 75.0082 15.959 76.6452 14.322C78.2822 12.6849 80.3679 11.5701 82.6385 11.1185C84.9091 10.6668 87.2626 10.8986 89.4015 11.7846C91.5404 12.6705 93.3685 14.1708 94.6547 16.0957C95.9409 18.0207 96.6274 20.2838 96.6274 22.5989C96.6248 25.7025 95.3908 28.6783 93.1961 30.8729C91.0015 33.0675 88.0257 34.3016 84.9221 34.3041ZM84.9221 13.1609C83.0547 13.1609 81.2292 13.7147 79.6765 14.7522C78.1238 15.7898 76.9137 17.2645 76.1992 18.9898C75.4848 20.7152 75.298 22.6137 75.6625 24.4452C76.0271 26.2767 76.9266 27.9589 78.2473 29.2792C79.568 30.5994 81.2506 31.4984 83.0822 31.8623C84.9138 32.2262 86.8122 32.0388 88.5373 31.3237C90.2624 30.6086 91.7367 29.398 92.7737 27.845C93.8108 26.292 94.3639 24.4663 94.3633 22.5989C94.3607 20.096 93.3651 17.6964 91.595 15.9269C89.8249 14.1574 87.425 13.1626 84.9221 13.1609Z"
        fill="#172B4D"
      />
      <path
        d="M32.5566 12.0293C32.5566 9.2251 33.6706 6.53576 35.6535 4.55289C37.6363 2.57002 40.3257 1.45605 43.1299 1.45605C45.9341 1.45605 48.6234 2.57002 50.6063 4.55289C52.5892 6.53576 53.7031 9.2251 53.7031 12.0293"
        fill="#CDD1FE"
      />
      <path
        d="M54.8364 12.0285H52.5723C52.5723 9.52457 51.5776 7.12316 49.807 5.35259C48.0364 3.58202 45.635 2.58732 43.1311 2.58732C40.6271 2.58732 38.2257 3.58202 36.4551 5.35259C34.6846 7.12316 33.6899 9.52457 33.6899 12.0285H31.4258C31.4258 8.9241 32.659 5.94681 34.8542 3.75164C37.0493 1.55647 40.0266 0.323242 43.1311 0.323242C46.2355 0.323242 49.2128 1.55647 51.408 3.75164C53.6031 5.94681 54.8364 8.9241 54.8364 12.0285Z"
        fill="#172B4D"
      />
      <path
        d="M115.82 11.7054C115.795 10.3016 116.051 8.90691 116.571 7.60279C117.091 6.29867 117.866 5.11123 118.85 4.10977C119.834 3.1083 121.008 2.31287 122.303 1.76989C123.598 1.22691 124.988 0.947266 126.392 0.947266C127.796 0.947266 129.186 1.22691 130.481 1.76989C131.775 2.31287 132.949 3.1083 133.933 4.10977C134.917 5.11123 135.692 6.29867 136.212 7.60279C136.733 8.90691 136.988 10.3016 136.963 11.7054"
        fill="#CDD1FE"
      />
      <path
        d="M138.108 11.7055H135.844C135.867 10.4512 135.64 9.20481 135.176 8.03922C134.712 6.87362 134.021 5.81217 133.142 4.91691C132.263 4.02164 131.215 3.3105 130.058 2.82505C128.901 2.3396 127.659 2.08957 126.404 2.08957C125.15 2.08957 123.908 2.3396 122.751 2.82505C121.594 3.3105 120.546 4.02164 119.667 4.91691C118.788 5.81217 118.097 6.87362 117.633 8.03922C117.169 9.20481 116.942 10.4512 116.965 11.7055H114.701C114.675 10.1522 114.959 8.60928 115.535 7.1667C116.112 5.72412 116.97 4.41075 118.059 3.30314C119.148 2.19553 120.447 1.31586 121.88 0.715385C123.313 0.114913 124.851 -0.194336 126.404 -0.194336C127.958 -0.194336 129.496 0.114913 130.929 0.715385C132.362 1.31586 133.661 2.19553 134.75 3.30314C135.839 4.41075 136.697 5.72412 137.274 7.1667C137.85 8.60928 138.134 10.1522 138.108 11.7055Z"
        fill="#172B4D"
      />
      <path
        d="M136.964 10.5732H33.541V12.8373H136.964V10.5732Z"
        fill="#172B4D"
      />
      <path
        d="M68.1745 43.9528L66.25 42.7658C68.1437 39.6469 70.8716 37.1195 74.1258 35.469C78.007 33.5283 81.5261 33.5283 84.9157 33.5283C88.121 33.5283 92.0993 33.5283 96.4334 35.6921C99.3169 37.1482 101.819 39.2595 103.74 41.8569L101.929 43.2153C100.218 40.8992 97.9877 39.0161 95.4178 37.7169C91.5656 35.7762 88.0369 35.7762 84.9222 35.7762C81.6457 35.7762 78.5537 35.7762 75.1349 37.4872C72.2574 38.9514 69.8465 41.1909 68.1745 43.9528Z"
        fill="#172B4D"
      />
    </g>
    <defs>
      <clipPath id="clip0_2161_2959">
        <rect width="170" height="78.9484" fill="white" />
      </clipPath>
    </defs>
  </svg>
);
export const CompanyLogoIcon = () => (
  <svg
    width="158"
    height="65"
    viewBox="0 0 158 65"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M0 39.7646L25.2353 23.4513L50.4707 39.7646L25.2353 56.078L0 39.7646Z"
      fill="#48D3FE"
    />
    <path
      d="M0 25.2354L25.2353 8.92205L50.4707 25.2354L25.2353 41.5487L0 25.2354Z"
      fill="#816AFE"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M39.2348 32.4217L25.2373 41.4704L11.2397 32.4217L25.2373 23.373L39.2348 32.4217Z"
      fill="#303BE5"
    />
    <path
      d="M72.9137 39.255C71.6897 39.255 70.6329 38.9887 69.7432 38.456C68.8592 37.9233 68.1764 37.1782 67.6947 36.2205C67.2187 35.2628 66.9807 34.1493 66.9807 32.88C66.9807 31.6107 67.2187 30.4972 67.6947 29.5395C68.1764 28.5818 68.8592 27.8367 69.7432 27.304C70.6329 26.7713 71.6897 26.505 72.9137 26.505C74.319 26.505 75.4977 26.8535 76.4497 27.5505C77.4074 28.2475 78.0817 29.191 78.4727 30.381L76.1437 31.027C75.917 30.2847 75.5345 29.7095 74.9962 29.3015C74.4579 28.8878 73.7637 28.681 72.9137 28.681C72.1374 28.681 71.4885 28.8538 70.9672 29.1995C70.4515 29.5452 70.0634 30.0325 69.8027 30.6615C69.542 31.2905 69.4117 32.03 69.4117 32.88C69.4117 33.73 69.542 34.4695 69.8027 35.0985C70.0634 35.7275 70.4515 36.2148 70.9672 36.5605C71.4885 36.9062 72.1374 37.079 72.9137 37.079C73.7637 37.079 74.4579 36.8722 74.9962 36.4585C75.5345 36.0448 75.917 35.4697 76.1437 34.733L78.4727 35.379C78.0817 36.569 77.4074 37.5125 76.4497 38.2095C75.4977 38.9065 74.319 39.255 72.9137 39.255ZM84.4753 39.255C83.5516 39.255 82.7413 39.0482 82.0443 38.6345C81.3473 38.2208 80.8033 37.6513 80.4123 36.926C80.027 36.195 79.8343 35.3563 79.8343 34.41C79.8343 33.4523 80.0326 32.6108 80.4293 31.8855C80.826 31.1545 81.3728 30.585 82.0698 30.177C82.7668 29.769 83.5686 29.565 84.4753 29.565C85.399 29.565 86.2093 29.7718 86.9063 30.1855C87.609 30.5992 88.1558 31.1715 88.5468 31.9025C88.9378 32.6278 89.1333 33.4637 89.1333 34.41C89.1333 35.362 88.935 36.2035 88.5383 36.9345C88.1473 37.6598 87.6005 38.2293 86.8978 38.643C86.2008 39.051 85.3933 39.255 84.4753 39.255ZM84.4753 37.096C85.2176 37.096 85.7701 36.8467 86.1328 36.348C86.5011 35.8437 86.6853 35.1977 86.6853 34.41C86.6853 33.594 86.4983 32.9423 86.1243 32.455C85.756 31.9677 85.2063 31.724 84.4753 31.724C83.971 31.724 83.5573 31.8373 83.2343 32.064C82.9113 32.2907 82.6705 32.6052 82.5118 33.0075C82.3588 33.4098 82.2823 33.8773 82.2823 34.41C82.2823 35.2317 82.4665 35.8862 82.8348 36.3735C83.2088 36.8552 83.7556 37.096 84.4753 37.096ZM102.053 39V33.577C102.053 32.9707 101.908 32.5003 101.619 32.166C101.336 31.826 100.942 31.656 100.438 31.656C100.115 31.656 99.834 31.7325 99.596 31.8855C99.358 32.0328 99.171 32.2425 99.035 32.5145C98.9047 32.7808 98.8395 33.0897 98.8395 33.441L97.8705 32.795C97.8705 32.166 98.0178 31.6107 98.3125 31.129C98.6128 30.6473 99.0123 30.2733 99.511 30.007C100.015 29.735 100.574 29.599 101.186 29.599C102.234 29.599 103.024 29.9107 103.557 30.534C104.095 31.1517 104.365 31.962 104.365 32.965V39H102.053ZM90.9855 39V29.82H93.0255V32.863H93.3145V39H90.9855ZM96.5275 39V33.577C96.5275 32.9707 96.383 32.5003 96.094 32.166C95.8107 31.826 95.4168 31.656 94.9125 31.656C94.4308 31.656 94.0427 31.8232 93.748 32.1575C93.459 32.4862 93.3145 32.914 93.3145 33.441L92.3455 32.761C92.3455 32.1603 92.4957 31.622 92.796 31.146C93.0963 30.67 93.4987 30.2932 94.003 30.0155C94.513 29.7378 95.0825 29.599 95.7115 29.599C96.4312 29.599 97.0205 29.752 97.4795 30.058C97.9442 30.364 98.287 30.772 98.508 31.282C98.729 31.792 98.8395 32.353 98.8395 32.965V39H96.5275ZM111.08 39.255C110.167 39.255 109.405 39.0425 108.793 38.6175C108.181 38.1925 107.719 37.6145 107.408 36.8835C107.102 36.1525 106.949 35.328 106.949 34.41C106.949 33.492 107.102 32.6675 107.408 31.9365C107.714 31.2055 108.164 30.6275 108.759 30.2025C109.354 29.7775 110.088 29.565 110.961 29.565C111.839 29.565 112.604 29.7747 113.256 30.194C113.907 30.6133 114.412 31.1885 114.769 31.9195C115.131 32.6448 115.313 33.475 115.313 34.41C115.313 35.328 115.134 36.1525 114.777 36.8835C114.426 37.6145 113.933 38.1925 113.298 38.6175C112.664 39.0425 111.924 39.255 111.08 39.255ZM106.558 43.08V29.82H108.598V36.11H108.887V43.08H106.558ZM110.706 37.198C111.204 37.198 111.612 37.0733 111.93 36.824C112.247 36.5747 112.482 36.2403 112.635 35.821C112.788 35.396 112.865 34.9257 112.865 34.41C112.865 33.9 112.785 33.4353 112.627 33.016C112.468 32.591 112.222 32.2538 111.887 32.0045C111.559 31.7495 111.136 31.622 110.621 31.622C110.139 31.622 109.748 31.7382 109.448 31.9705C109.153 32.2028 108.938 32.5287 108.802 32.948C108.666 33.3673 108.598 33.8547 108.598 34.41C108.598 34.9653 108.666 35.4527 108.802 35.872C108.938 36.2913 109.159 36.6172 109.465 36.8495C109.776 37.0818 110.19 37.198 110.706 37.198ZM117.353 39V26.76H119.665V36.841H124.935V39H117.353ZM130.511 39.255C129.588 39.255 128.777 39.0482 128.08 38.6345C127.383 38.2208 126.839 37.6513 126.448 36.926C126.063 36.195 125.87 35.3563 125.87 34.41C125.87 33.4523 126.069 32.6108 126.465 31.8855C126.862 31.1545 127.409 30.585 128.106 30.177C128.803 29.769 129.605 29.565 130.511 29.565C131.435 29.565 132.245 29.7718 132.942 30.1855C133.645 30.5992 134.192 31.1715 134.583 31.9025C134.974 32.6278 135.169 33.4637 135.169 34.41C135.169 35.362 134.971 36.2035 134.574 36.9345C134.183 37.6598 133.637 38.2293 132.934 38.643C132.237 39.051 131.429 39.255 130.511 39.255ZM130.511 37.096C131.254 37.096 131.806 36.8467 132.169 36.348C132.537 35.8437 132.721 35.1977 132.721 34.41C132.721 33.594 132.534 32.9423 132.16 32.455C131.792 31.9677 131.242 31.724 130.511 31.724C130.007 31.724 129.593 31.8373 129.27 32.064C128.947 32.2907 128.707 32.6052 128.548 33.0075C128.395 33.4098 128.318 33.8773 128.318 34.41C128.318 35.2317 128.503 35.8862 128.871 36.3735C129.245 36.8552 129.792 37.096 130.511 37.096ZM140.966 43.335C140.444 43.335 139.948 43.25 139.478 43.08C139.008 42.9157 138.586 42.6833 138.212 42.383C137.843 42.0883 137.543 41.7427 137.311 41.346L139.453 40.309C139.6 40.581 139.81 40.7878 140.082 40.9295C140.359 41.0712 140.66 41.142 140.983 41.142C141.328 41.142 141.654 41.0825 141.96 40.9635C142.266 40.8502 142.51 40.6773 142.691 40.445C142.878 40.2183 142.966 39.935 142.955 39.595V36.926H143.244V29.82H145.267V39.629C145.267 39.8557 145.255 40.0682 145.233 40.2665C145.216 40.4705 145.182 40.6717 145.131 40.87C144.983 41.431 144.708 41.8928 144.306 42.2555C143.909 42.6182 143.422 42.8873 142.844 43.063C142.266 43.2443 141.64 43.335 140.966 43.335ZM140.762 39.255C139.917 39.255 139.178 39.0425 138.543 38.6175C137.908 38.1925 137.413 37.6145 137.056 36.8835C136.704 36.1525 136.529 35.328 136.529 34.41C136.529 33.475 136.707 32.6448 137.064 31.9195C137.427 31.1885 137.934 30.6133 138.586 30.194C139.237 29.7747 140.002 29.565 140.881 29.565C141.753 29.565 142.487 29.7775 143.082 30.2025C143.677 30.6275 144.128 31.2055 144.434 31.9365C144.74 32.6675 144.893 33.492 144.893 34.41C144.893 35.328 144.737 36.1525 144.425 36.8835C144.119 37.6145 143.66 38.1925 143.048 38.6175C142.436 39.0425 141.674 39.255 140.762 39.255ZM141.136 37.198C141.651 37.198 142.062 37.0818 142.368 36.8495C142.68 36.6172 142.904 36.2913 143.04 35.872C143.176 35.4527 143.244 34.9653 143.244 34.41C143.244 33.8547 143.176 33.3673 143.04 32.948C142.904 32.5287 142.685 32.2028 142.385 31.9705C142.09 31.7382 141.702 31.622 141.221 31.622C140.705 31.622 140.28 31.7495 139.946 32.0045C139.617 32.2538 139.373 32.591 139.215 33.016C139.056 33.4353 138.977 33.9 138.977 34.41C138.977 34.9257 139.053 35.396 139.206 35.821C139.359 36.2403 139.594 36.5747 139.912 36.824C140.229 37.0733 140.637 37.198 141.136 37.198ZM151.778 39.255C150.854 39.255 150.044 39.0482 149.347 38.6345C148.65 38.2208 148.106 37.6513 147.715 36.926C147.33 36.195 147.137 35.3563 147.137 34.41C147.137 33.4523 147.335 32.6108 147.732 31.8855C148.129 31.1545 148.676 30.585 149.373 30.177C150.07 29.769 150.871 29.565 151.778 29.565C152.702 29.565 153.512 29.7718 154.209 30.1855C154.912 30.5992 155.459 31.1715 155.85 31.9025C156.241 32.6278 156.436 33.4637 156.436 34.41C156.436 35.362 156.238 36.2035 155.841 36.9345C155.45 37.6598 154.903 38.2293 154.201 38.643C153.504 39.051 152.696 39.255 151.778 39.255ZM151.778 37.096C152.52 37.096 153.073 36.8467 153.436 36.348C153.804 35.8437 153.988 35.1977 153.988 34.41C153.988 33.594 153.801 32.9423 153.427 32.455C153.059 31.9677 152.509 31.724 151.778 31.724C151.274 31.724 150.86 31.8373 150.537 32.064C150.214 32.2907 149.973 32.6052 149.815 33.0075C149.662 33.4098 149.585 33.8773 149.585 34.41C149.585 35.2317 149.769 35.8862 150.138 36.3735C150.512 36.8552 151.058 37.096 151.778 37.096Z"
      fill="black"
    />
  </svg>
);
export const UserIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8 10C10.2091 10 12 8.20914 12 6C12 3.79086 10.2091 2 8 2C5.79086 2 4 3.79086 4 6C4 8.20914 5.79086 10 8 10Z"
      stroke="black"
      strokeMiterlimit="10"
    />
    <path
      d="M1.9375 13.5006C2.55184 12.4363 3.43552 11.5525 4.49972 10.938C5.56392 10.3235 6.77113 10 8 10C9.22887 10 10.4361 10.3235 11.5003 10.938C12.5645 11.5525 13.4482 12.4363 14.0625 13.5006"
      stroke="black"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const BinIcon = () => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M16.875 4.875H3.125"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.125 8.625V13.625"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.875 8.625V13.625"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.625 4.875V16.75C15.625 16.9158 15.5592 17.0747 15.4419 17.1919C15.3247 17.3092 15.1658 17.375 15 17.375H5C4.83424 17.375 4.67527 17.3092 4.55806 17.1919C4.44085 17.0747 4.375 16.9158 4.375 16.75V4.875"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.125 4.875V3.625C13.125 3.29348 12.9933 2.97554 12.7589 2.74112C12.5245 2.5067 12.2065 2.375 11.875 2.375H8.125C7.79348 2.375 7.47554 2.5067 7.24112 2.74112C7.0067 2.97554 6.875 3.29348 6.875 3.625V4.875"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const TickIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M13.5 4.5L6.5 11.5L3 8"
      stroke="#0517F8"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const ExclamationIcon = ({
  strokeColor = "",
  width = 16,
  height = 16,
  ...props
}) => (
  <svg
    width={width}
    height={height}
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8 14C11.3137 14 14 11.3137 14 8C14 4.68629 11.3137 2 8 2C4.68629 2 2 4.68629 2 8C2 11.3137 4.68629 14 8 14Z"
      stroke="#EE0064"
      class={`exclamation-icon ${strokeColor}`}
      strokeWidth="1.2"
      strokeMiterlimit="10"
    />
    <path
      d="M8 5V8.5"
      stroke="#EE0064"
      class={`exclamation-icon ${strokeColor}`}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.375 10.75C8.375 10.9571 8.20711 11.125 8 11.125C7.79289 11.125 7.625 10.9571 7.625 10.75C7.625 10.5429 7.79289 10.375 8 10.375C8.20711 10.375 8.375 10.5429 8.375 10.75Z"
      fill="black"
      stroke="#EE0064"
      class={`exclamation-icon ${strokeColor}`}
      strokeWidth="0.75"
    />
  </svg>
);
export const ChevronDownIcon = () => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <circle cx="12" cy="12" r="11.5" fill="white" stroke="#E8EAED" />
    <path
      d="M8.25 13.5L12 9.75L15.75 13.5"
      stroke="#172B4D"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DocumentsIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_11372_931)">
      <path
        d="M13.125 17.5H4.375C4.20924 17.5 4.05027 17.4342 3.93306 17.3169C3.81585 17.1997 3.75 17.0408 3.75 16.875V5.625C3.75 5.45924 3.81585 5.30027 3.93306 5.18306C4.05027 5.06585 4.20924 5 4.375 5H10.625L13.75 8.125V16.875C13.75 17.0408 13.6842 17.1997 13.5669 17.3169C13.4497 17.4342 13.2908 17.5 13.125 17.5Z"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.25 5V3.125C6.25 2.95924 6.31585 2.80027 6.43306 2.68306C6.55027 2.56585 6.70924 2.5 6.875 2.5H13.125L16.25 5.625V14.375C16.25 14.5408 16.1842 14.6997 16.0669 14.8169C15.9497 14.9342 15.7908 15 15.625 15H13.75"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.875 11.875H10.625"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.875 14.375H10.625"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11372_931">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const DocumentsFilledIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_10992_154858)">
      <path
        d="M20.0344 6.21564L16.2844 2.46564C16.1415 2.32635 15.9495 2.24889 15.75 2.25001H8.25C7.85218 2.25001 7.47064 2.40805 7.18934 2.68935C6.90804 2.97066 6.75 3.35219 6.75 3.75001V5.25001H5.25C4.85218 5.25001 4.47064 5.40805 4.18934 5.68935C3.90804 5.97066 3.75 6.35219 3.75 6.75001V20.25C3.75 20.6478 3.90804 21.0294 4.18934 21.3107C4.47064 21.592 4.85218 21.75 5.25 21.75H15.75C16.1478 21.75 16.5294 21.592 16.8107 21.3107C17.092 21.0294 17.25 20.6478 17.25 20.25V18.75H18.75C19.1478 18.75 19.5294 18.592 19.8107 18.3107C20.092 18.0294 20.25 17.6478 20.25 17.25V6.75001C20.2511 6.55047 20.1737 6.35851 20.0344 6.21564ZM12.75 18H8.25C8.05109 18 7.86032 17.921 7.71967 17.7803C7.57902 17.6397 7.5 17.4489 7.5 17.25C7.5 17.0511 7.57902 16.8603 7.71967 16.7197C7.86032 16.579 8.05109 16.5 8.25 16.5H12.75C12.9489 16.5 13.1397 16.579 13.2803 16.7197C13.421 16.8603 13.5 17.0511 13.5 17.25C13.5 17.4489 13.421 17.6397 13.2803 17.7803C13.1397 17.921 12.9489 18 12.75 18ZM12.75 15H8.25C8.05109 15 7.86032 14.921 7.71967 14.7803C7.57902 14.6397 7.5 14.4489 7.5 14.25C7.5 14.0511 7.57902 13.8603 7.71967 13.7197C7.86032 13.579 8.05109 13.5 8.25 13.5H12.75C12.9489 13.5 13.1397 13.579 13.2803 13.7197C13.421 13.8603 13.5 14.0511 13.5 14.25C13.5 14.4489 13.421 14.6397 13.2803 14.7803C13.1397 14.921 12.9489 15 12.75 15ZM18.75 17.25H17.25V9.75001C17.2511 9.55047 17.1737 9.35851 17.0344 9.21564L13.2844 5.46564C13.1415 5.32634 12.9495 5.24889 12.75 5.25001H8.25V3.75001H15.4406L18.75 7.05939V17.25Z"
        fill="#0517F8"
      />
    </g>
    <defs>
      <clipPath id="clip0_10992_154858">
        <rect width="24" height="24" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const AllTemplatesIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.5 4.375H17.5V15C17.5 15.1658 17.4342 15.3247 17.3169 15.4419C17.1997 15.5592 17.0408 15.625 16.875 15.625H3.125C2.95924 15.625 2.80027 15.5592 2.68306 15.4419C2.56585 15.3247 2.5 15.1658 2.5 15V4.375Z"
      stroke="#748094"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 8.125H17.5"
      stroke="#748094"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 11.875H17.5"
      stroke="#748094"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.875 8.125V15.625"
      stroke="#748094"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PdfDocIcon = () => (
  <svg
    width="40"
    height="40"
    viewBox="0 0 40 40"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M8.74998 5H24.6809L33.1249 13.4113V33.125C33.1249 34.1609 32.285 35 31.25 35H8.74998C7.71495 35 6.875 34.1609 6.875 33.125V6.87498C6.875 5.83908 7.71505 5 8.74998 5Z"
      fill="#EE0047"
    />
    <path
      d="M33.0978 13.4373H26.5625C25.5275 13.4373 24.6875 12.5973 24.6875 11.5623V5.01855L33.0978 13.4373Z"
      fill="white"
      fillOpacity="0.7"
    />
    <path
      d="M26.0923 21.0791C26.4064 21.0791 26.5601 20.8053 26.5601 20.54C26.5601 20.2653 26.3998 20 26.0923 20H24.3035C23.9538 20 23.7588 20.2897 23.7588 20.6094V25.0053C23.7588 25.3972 23.9819 25.6147 24.2838 25.6147C24.5839 25.6147 24.8079 25.3972 24.8079 25.0053V23.7988H25.8898C26.2254 23.7988 26.3933 23.524 26.3933 23.2513C26.3933 22.9841 26.2254 22.7187 25.8898 22.7187H24.8079V21.0791H26.0923ZM20.0463 20H18.7375C18.3822 20 18.13 20.2438 18.13 20.6056V25.0091C18.13 25.4581 18.4928 25.5988 18.7525 25.5988H20.126C21.7515 25.5988 22.825 24.5291 22.825 22.8781C22.8241 21.1325 21.8135 20 20.0463 20ZM20.1092 24.5132H19.3113V21.0857H20.0304C21.1189 21.0857 21.5923 21.816 21.5923 22.8238C21.5923 23.7669 21.1272 24.5132 20.1092 24.5132ZM15.3148 20H14.0182C13.6517 20 13.4473 20.2418 13.4473 20.6094V25.0053C13.4473 25.3972 13.6816 25.6147 13.9966 25.6147C14.3116 25.6147 14.5459 25.3972 14.5459 25.0053V23.7218H15.3587C16.3618 23.7218 17.1897 23.0112 17.1897 21.8684C17.1898 20.75 16.391 20 15.3148 20ZM15.2932 22.6907H14.546V21.0322H15.2932C15.7545 21.0322 16.0479 21.3922 16.0479 21.8619C16.047 22.3307 15.7545 22.6907 15.2932 22.6907Z"
      fill="white"
    />
  </svg>
);

export const CalendarWithDotsIcon = ({ color, className }) => (
  <svg
    className={className || ""}
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M16 2.5H15.25V1.75C15.25 1.33579 14.9142 1 14.5 1C14.0858 1 13.75 1.33579 13.75 1.75V2.5H10.75V1.75C10.75 1.33579 10.4142 1 10 1C9.58579 1 9.25 1.33579 9.25 1.75V2.5H6.25V1.75C6.25 1.33579 5.91421 1 5.5 1C5.08579 1 4.75 1.33579 4.75 1.75V2.5H4C2.34315 2.5 1 3.84315 1 5.5V16C1 17.6569 2.34315 19 4 19H16C17.6569 19 19 17.6569 19 16V5.5C19 3.84315 17.6569 2.5 16 2.5ZM4 4H4.75V4.75C4.75 5.16421 5.08579 5.5 5.5 5.5C5.91421 5.5 6.25 5.16421 6.25 4.75V4H9.25V4.75C9.25 5.16421 9.58579 5.5 10 5.5C10.4142 5.5 10.75 5.16421 10.75 4.75V4H13.75V4.75C13.75 5.16421 14.0858 5.5 14.5 5.5C14.9142 5.5 15.25 5.16421 15.25 4.75V4H16C16.8284 4 17.5 4.67157 17.5 5.5V7H2.5V5.5C2.5 4.67157 3.17157 4 4 4ZM2.5 16C2.5 16.8284 3.17157 17.5 4 17.5H16C16.8284 17.5 17.5 16.8284 17.5 16V8.5H2.5V16Z"
      fill={color || "white"}
    />
    <circle cx="5.5" cy="11.5" r="0.75" fill={color || "white"} />
    <circle cx="5.5" cy="14.5" r="0.75" fill={color || "white"} />
    <circle cx="8.5" cy="11.5" r="0.75" fill={color || "white"} />
    <circle cx="8.5" cy="14.5" r="0.75" fill={color || "white"} />
    <circle cx="11.5" cy="11.5" r="0.75" fill={color || "white"} />
    <circle cx="11.5" cy="14.5" r="0.75" fill={color || "white"} />
    <circle cx="14.5" cy="11.5" r="0.75" fill={color || "white"} />
    <circle cx="14.5" cy="14.5" r="0.75" fill={color || "white"} />
  </svg>
);

export const WaitingJarIcon = () => (
  <svg
    width="120"
    height="90"
    viewBox="0 0 120 90"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M71.4512 49.6913C70.4146 49.1537 67.0553 47.4755 67.0553 44.0587C67.0553 40.8852 70.2315 38.9487 71.2291 38.4041C75.0106 36.3407 85.0925 29.576 84.4802 16.2315H61.3323V16.1875H38.0688C36.8467 29.5111 47.1477 36.3332 51.0978 38.3816C52.1343 38.9192 55.4936 40.5974 55.4936 44.0147C55.4936 47.1882 52.3174 49.1247 51.3204 49.6693C47.5384 51.7327 37.457 58.4974 38.0688 71.8414H61.2167V71.8854H84.4802C85.7023 58.5618 75.4013 51.7397 71.4512 49.6913Z"
      fill="white"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M37.9768 17.6309C37.9718 17.7983 37.9708 17.9637 37.969 18.129C37.9673 18.5484 37.9743 18.9633 37.9949 19.3694H60.3813C69.0229 19.3694 76.0284 26.2093 76.0284 34.6468V34.7535C80.2349 31.278 84.9797 25.3105 84.5537 16.2301H66.3774H61.366V16.1875H38.0615C38.0295 16.5329 38.0094 16.872 37.9929 17.2087C37.9864 17.3501 37.9803 17.491 37.9768 17.6309Z"
      fill="#D1D5DB"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M80.2353 24.2344C77.686 30.239 72.476 33.659 69.7872 35.0904C68.0991 35.9895 63.7475 38.5006 63.4552 42.8003V53.9399C63.6345 57.1352 66.1093 59.7361 69.8846 61.6736L69.9387 61.6756C72.0891 62.7816 80.2353 65.6955 80.2353 72.5059H42.3246C42.3246 65.6955 49.9588 63.1071 52.6042 61.6756C54.2651 60.7766 58.1957 58.2397 58.4833 53.9399V42.8003C58.301 39.605 56.0606 37.0042 52.2235 35.0666L52.1685 35.0388C49.9829 33.9329 44.2483 30.5218 41.7031 24.2344H80.2353Z"
      fill="#6974FB"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M72.1484 24.2344C73.8094 26.1586 74.9417 28.3491 75.5397 30.9098C77.3016 29.1421 79.0747 27.0261 80.2278 24.2344H72.1484Z"
      fill="#0517F8"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M37.2781 16.1887C35.6013 16.1887 34.2422 14.8029 34.2422 13.0943C34.2422 11.3852 35.6013 10 37.2781 10H84.6541C86.331 10 87.69 11.3852 87.69 13.0943C87.69 14.8029 86.331 16.1887 84.6541 16.1887H37.2781Z"
      fill="#D1D5DB"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M70.834 49.6913C69.7975 49.1537 66.4382 47.4755 66.4382 44.0587C66.4382 40.8852 69.6144 38.9487 70.6119 38.4041C74.3934 36.3407 84.4753 29.576 83.863 16.2315H60.7151V16.1875H37.4516C36.2295 29.5111 46.5305 36.3332 50.4806 38.3816C51.5172 38.9192 54.8765 40.5974 54.8765 44.0147C54.8765 47.1882 51.7003 49.1247 50.7032 49.6693C46.9212 51.7327 36.8398 58.4974 37.4516 71.8414H60.5995V71.8854H83.863C85.0851 58.5618 74.7841 51.7397 70.834 49.6913Z"
      stroke="#172B4D"
      strokeWidth="1.41892"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M80.2353 24.2344C79.8121 25.2589 79.3054 26.1925 78.7464 27.0512C76.0031 31.3342 72.0233 33.9 69.7872 35.0904C68.1413 35.967 63.9634 38.376 63.4846 42.4812L63.4552 42.8003V53.9399C63.6292 57.0412 65.9658 59.5826 69.5549 61.501L69.8846 61.6736L69.9387 61.6756C72.0526 62.7628 79.9609 65.5973 80.2283 72.163L80.2353 72.5059H42.3246C42.3246 65.6955 49.9588 63.1071 52.6042 61.6756C54.2225 60.7996 57.9956 58.3687 58.4541 54.2672L58.4833 53.9399V42.8003C58.3063 39.699 56.1906 37.1577 52.5579 35.2393L52.2235 35.0666L52.1685 35.0388C50.0314 33.9575 44.5014 30.6722 41.8775 24.6493L41.7031 24.2344H80.2353Z"
      stroke="#6974FB"
      strokeWidth="1.41892"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M84.6619 76.9465H37.2859C35.6091 76.9465 34.25 75.5607 34.25 73.8521C34.25 72.143 35.6091 70.7578 37.2859 70.7578H84.6619C86.3388 70.7578 87.6978 72.143 87.6978 73.8521C87.6978 75.5607 86.3388 76.9465 84.6619 76.9465Z"
      fill="#D1D5DB"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M83.951 76.9465H36.575C34.8981 76.9465 33.5391 75.5607 33.5391 73.8521C33.5391 72.143 34.8981 70.7578 36.575 70.7578H83.951C85.6278 70.7578 86.9869 72.143 86.9869 73.8521C86.9869 75.5607 85.6278 76.9465 83.951 76.9465Z"
      stroke="#172B4D"
      strokeWidth="1.41892"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M84.6541 16.1887H37.2781C35.6013 16.1887 34.2422 14.8029 34.2422 13.0943C34.2422 11.3852 35.6013 10 37.2781 10H84.6541C86.331 10 87.69 11.3852 87.69 13.0943C87.69 14.8029 86.331 16.1887 84.6541 16.1887Z"
      stroke="#172B4D"
      strokeWidth="1.41892"
    />
    <path
      d="M8 77L113 77.1144"
      stroke="#172B4D"
      strokeWidth="1.41892"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M26 81H94"
      stroke="#172B4D"
      strokeWidth="1.41892"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const HourglassIcon = ({ fillColor, ...props }) => (
  <svg
    width="15"
    height="14"
    viewBox="0 0 15 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.27507 13.125C2.15861 13.125 2.04691 13.0789 1.96456 12.9969C1.8822 12.9148 1.83594 12.8035 1.83594 12.6875C1.83594 12.5715 1.8822 12.4602 1.96456 12.3781C2.04691 12.2961 2.15861 12.25 2.27507 12.25H3.15334V11.375C3.15316 10.6321 3.36394 9.9043 3.76133 9.27568C4.15871 8.64706 4.7265 8.14327 5.39908 7.8225C5.65378 7.70087 5.78816 7.49263 5.78816 7.30625V6.69375C5.78816 6.50738 5.6529 6.29912 5.39908 6.1775C4.7265 5.85673 4.15871 5.35294 3.76133 4.72432C3.36394 4.0957 3.15316 3.3679 3.15334 2.625V1.75H2.27507C2.15861 1.75 2.04691 1.70391 1.96456 1.62186C1.8822 1.53981 1.83594 1.42853 1.83594 1.3125C1.83594 1.19647 1.8822 1.08519 1.96456 1.00314C2.04691 0.921094 2.15861 0.875 2.27507 0.875H11.9361C12.0525 0.875 12.1642 0.921094 12.2466 1.00314C12.3289 1.08519 12.3752 1.19647 12.3752 1.3125C12.3752 1.42853 12.3289 1.53981 12.2466 1.62186C12.1642 1.70391 12.0525 1.75 11.9361 1.75H11.0578V2.625C11.058 3.3679 10.8472 4.0957 10.4498 4.72432C10.0524 5.35294 9.48463 5.85673 8.81204 6.1775C8.55735 6.29912 8.42297 6.50738 8.42297 6.69375V7.30625C8.42297 7.49263 8.55822 7.70087 8.81204 7.8225C9.48463 8.14327 10.0524 8.64706 10.4498 9.27568C10.8472 9.9043 11.058 10.6321 11.0578 11.375V12.25H11.9361C12.0525 12.25 12.1642 12.2961 12.2466 12.3781C12.3289 12.4602 12.3752 12.5715 12.3752 12.6875C12.3752 12.8035 12.3289 12.9148 12.2466 12.9969C12.1642 13.0789 12.0525 13.125 11.9361 13.125H2.27507ZM4.03162 1.75V2.625C4.03162 3.09488 4.13701 3.53938 4.32759 3.9375H9.88354C10.0732 3.53938 10.1795 3.09488 10.1795 2.625V1.75H4.03162ZM6.66643 7.30625C6.66643 7.91963 6.24661 8.38775 5.7785 8.61175C5.25531 8.86122 4.81364 9.25308 4.50452 9.74205C4.1954 10.231 4.03146 10.7971 4.03162 11.375C4.03162 11.375 4.7922 10.2384 6.66643 10.08V7.30625ZM7.5447 7.30625V10.08C9.41893 10.2384 10.1795 11.375 10.1795 11.375C10.1797 10.7971 10.0157 10.231 9.70661 9.74205C9.39749 9.25308 8.95582 8.86122 8.43263 8.61175C7.96451 8.38775 7.5447 7.9205 7.5447 7.30713V7.30625Z"
      fill={fillColor || "#00A5BC"}
    />
  </svg>
);

export const HourglassIconSmall = ({ fillColor, ...props }) => {
  return (
    <svg
      width="8"
      height="10"
      viewBox="0 0 8 10"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <path
        d="M0.5625 9.375C0.47962 9.375 0.400134 9.34208 0.341529 9.28347C0.282924 9.22487 0.25 9.14538 0.25 9.0625C0.25 8.97962 0.282924 8.90013 0.341529 8.84153C0.400134 8.78292 0.47962 8.75 0.5625 8.75H1.1875V8.125C1.18737 7.59436 1.33737 7.0745 1.62016 6.62549C1.90295 6.17647 2.30699 5.81662 2.78562 5.5875C2.96688 5.50062 3.0625 5.35188 3.0625 5.21875V4.78125C3.0625 4.64813 2.96625 4.49937 2.78562 4.4125C2.30699 4.18338 1.90295 3.82353 1.62016 3.37451C1.33737 2.9255 1.18737 2.40564 1.1875 1.875V1.25H0.5625C0.47962 1.25 0.400134 1.21708 0.341529 1.15847C0.282924 1.09987 0.25 1.02038 0.25 0.9375C0.25 0.85462 0.282924 0.775134 0.341529 0.716529C0.400134 0.657924 0.47962 0.625 0.5625 0.625H7.4375C7.52038 0.625 7.59987 0.657924 7.65847 0.716529C7.71708 0.775134 7.75 0.85462 7.75 0.9375C7.75 1.02038 7.71708 1.09987 7.65847 1.15847C7.59987 1.21708 7.52038 1.25 7.4375 1.25H6.8125V1.875C6.81263 2.40564 6.66263 2.9255 6.37984 3.37451C6.09705 3.82353 5.69301 4.18338 5.21438 4.4125C5.03312 4.49937 4.9375 4.64813 4.9375 4.78125V5.21875C4.9375 5.35188 5.03375 5.50062 5.21438 5.5875C5.69301 5.81662 6.09705 6.17647 6.37984 6.62549C6.66263 7.0745 6.81263 7.59436 6.8125 8.125V8.75H7.4375C7.52038 8.75 7.59987 8.78292 7.65847 8.84153C7.71708 8.90013 7.75 8.97962 7.75 9.0625C7.75 9.14538 7.71708 9.22487 7.65847 9.28347C7.59987 9.34208 7.52038 9.375 7.4375 9.375H0.5625ZM1.8125 1.25V1.875C1.8125 2.21063 1.8875 2.52813 2.02312 2.8125H5.97688C6.11188 2.52813 6.1875 2.21063 6.1875 1.875V1.25H1.8125ZM3.6875 5.21875C3.6875 5.65688 3.38875 5.99125 3.05562 6.15125C2.68331 6.32945 2.36901 6.60934 2.14903 6.9586C1.92906 7.30787 1.81239 7.71224 1.8125 8.125C1.8125 8.125 2.35375 7.31313 3.6875 7.2V5.21875ZM4.3125 5.21875V7.2C5.64625 7.31313 6.1875 8.125 6.1875 8.125C6.18761 7.71224 6.07094 7.30787 5.85097 6.9586C5.63099 6.60934 5.31669 6.32945 4.94438 6.15125C4.61125 5.99125 4.3125 5.6575 4.3125 5.21937V5.21875Z"
        fill={fillColor || "#00A5BC"}
      />
    </svg>
  );
};

export const MakulaInterfaceTopIcon = () => (
  <svg
    width="146"
    height="70"
    viewBox="0 0 146 70"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M146 70L141.405 59.4068L134.528 68.6832L146 70ZM0.157364 65.2593L8.14359 66.9583L9.84264 58.9721L1.85641 57.2731L0.157364 65.2593ZM88.4425 27.335L89.038 26.5316L88.4425 27.335ZM59.9429 26.4497L60.4874 27.2884L59.9429 26.4497ZM139.365 63.8372L89.038 26.5316L87.847 28.1383L138.174 65.4439L139.365 63.8372ZM59.3985 25.6109L4.45552 61.2769L5.54448 62.9545L60.4874 27.2884L59.3985 25.6109ZM89.038 26.5316C80.3187 20.0684 68.502 19.7013 59.3985 25.6109L60.4874 27.2884C68.8907 21.8335 79.7984 22.1723 87.847 28.1383L89.038 26.5316Z"
      fill="#E8EAED"
    />
    <circle cx="72" cy="26" r="26" fill="white" />
    <path
      d="M74.625 18.625V14.5C74.625 14.4668 74.6118 14.4351 74.5884 14.4116C74.5649 14.3882 74.5332 14.375 74.5 14.375H67.4125H67.4122C67.1991 14.3748 66.9882 14.4168 66.7915 14.4986C66.5948 14.5805 66.4162 14.7004 66.2662 14.8517L66.2652 14.8527L60.8527 20.2652L60.8517 20.2662C60.7004 20.4162 60.5805 20.5948 60.4986 20.7915C60.4168 20.9882 60.3748 21.1991 60.375 21.4122L60.375 21.4125L60.375 36C60.375 36.431 60.5462 36.8443 60.851 37.149C61.1557 37.4538 61.569 37.625 62 37.625H82C82.431 37.625 82.8443 37.4538 83.149 37.149C83.4538 36.8443 83.625 36.431 83.625 36V16C83.625 15.569 83.4538 15.1557 83.149 14.851C82.8443 14.5462 82.431 14.375 82 14.375H77.5C77.4668 14.375 77.4351 14.3882 77.4116 14.4116C77.3882 14.4351 77.375 14.4668 77.375 14.5V20C77.375 20.3647 77.2301 20.7144 76.9723 20.9723C76.7144 21.2301 76.3647 21.375 76 21.375H68.0402C67.6869 21.3798 67.3449 21.2508 67.0826 21.0139C66.8196 20.7762 66.6566 20.4476 66.6264 20.0945L66.6256 20.0859L66.6257 20.0859C66.614 19.8983 66.6408 19.7102 66.7047 19.5333C66.7685 19.3564 66.8679 19.1945 66.9968 19.0576C67.1256 18.9207 67.2812 18.8117 67.4539 18.7372C67.6264 18.6629 67.8123 18.6247 68.0002 18.625M74.625 18.625L68.0007 18.625C68.0006 18.625 68.0004 18.625 68.0002 18.625M74.625 18.625H68.0002M74.625 18.625H68.0002M72 32.375L72.0013 32.375C73.0276 32.3714 74.0107 31.9621 74.7364 31.2364C75.4621 30.5107 75.8714 29.5276 75.875 28.5013V28.5C75.875 27.7336 75.6477 26.9844 75.2219 26.3472C74.7962 25.7099 74.191 25.2133 73.4829 24.92C72.7748 24.6267 71.9957 24.5499 71.244 24.6995C70.4923 24.849 69.8019 25.218 69.26 25.76C68.718 26.3019 68.349 26.9924 68.1995 27.744C68.0499 28.4957 68.1267 29.2748 68.42 29.9829C68.7133 30.691 69.2099 31.2962 69.8472 31.7219C70.4844 32.1477 71.2336 32.375 72 32.375Z"
      fill="#CCEFEB"
      stroke="#172B4D"
      strokeWidth="0.75"
    />
  </svg>
);

export const DigitalizePlaneIcon = () => (
  <svg
    width="43"
    height="46"
    viewBox="0 0 43 46"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M22.9325 23.4595C20.4257 23.4491 17.9188 23.4388 15.4119 23.4284C16.9269 24.9967 18.4632 26.565 19.9782 28.1333"
      fill="white"
    />
    <path
      d="M22.9325 23.4595C20.4257 23.4491 17.9188 23.4388 15.4119 23.4284C16.9269 24.9967 18.4632 26.565 19.9782 28.1333"
      stroke="#172B4D"
      strokeMiterlimit="10"
    />
    <path
      d="M41.1822 43.9591L28.0669 36.9808L14.9516 29.9815L22.3162 23.3992L29.6807 16.7959L35.4314 30.3775L41.1822 43.9591Z"
      fill="#E5F7F5"
      stroke="#172B4D"
      strokeMiterlimit="10"
    />
    <path
      d="M22.3165 23.3992C28.4202 30.0713 34.5452 36.7434 40.649 43.4155L22.3165 23.3992Z"
      fill="white"
    />
    <path
      d="M22.3165 23.3992C28.4202 30.0713 34.5452 36.7434 40.649 43.4155"
      stroke="#172B4D"
      strokeMiterlimit="10"
    />
    <path
      d="M18.0956 20.0154C14.7877 16.418 11.4798 12.8205 8.15075 9.24417L18.0956 20.0154Z"
      fill="white"
    />
    <path
      d="M18.0956 20.0154C14.7877 16.418 11.4798 12.8205 8.15075 9.24417"
      stroke="#172B4D"
      strokeMiterlimit="10"
      strokeLinecap="round"
    />
    <path
      d="M10.9667 17.0007C7.65878 13.4033 4.35089 9.80587 1.02185 6.22952L10.9667 17.0007Z"
      fill="white"
    />
    <path
      d="M10.9667 17.0007C7.65878 13.4033 4.35089 9.80587 1.02185 6.22952"
      stroke="#172B4D"
      strokeMiterlimit="10"
      strokeLinecap="round"
    />
    <path
      d="M5.97295 6.56606C4.30829 4.76738 2.66488 2.96861 1.00023 1.16994L5.97295 6.56606Z"
      fill="white"
    />
    <path
      d="M5.97295 6.56606C4.30829 4.76738 2.66488 2.96861 1.00023 1.16994"
      stroke="black"
      strokeMiterlimit="10"
      strokeLinecap="round"
    />
  </svg>
);
export const DigitalizationIcon = () => (
  <svg
    width="143"
    height="119"
    viewBox="0 0 143 119"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M39.2693 102.458L42.3853 104.251C43.4516 104.865 44.1088 106.001 44.1088 107.231V112.993C44.1088 114.223 43.4516 115.359 42.3853 115.973L39.2693 117.766C38.2076 118.377 36.9012 118.377 35.8395 117.766L32.7235 115.973C31.6572 115.359 31 114.223 31 112.993V107.231C31 106.001 31.6572 104.865 32.7235 104.251L35.8395 102.458C36.9012 101.847 38.2076 101.847 39.2693 102.458ZM35.9018 105.587L35.8695 105.606L33.1508 107.175C32.1187 107.771 31.479 108.867 31.4663 110.057L31.4661 110.093V110.131C31.4661 111.322 32.0955 112.424 33.1196 113.03L33.1508 113.049L35.8695 114.618C36.9014 115.214 38.1702 115.22 39.207 114.637L39.2393 114.618L41.958 113.049C42.9901 112.453 43.6298 111.357 43.6425 110.167L43.6427 110.131V110.093C43.6427 108.902 43.0133 107.8 41.9892 107.194L41.958 107.175L39.2393 105.606C38.2075 105.01 36.9386 105.004 35.9018 105.587ZM53.9283 106.215C54.0773 106.215 54.192 106.283 54.2722 106.409L57.1501 111.078L60.0165 106.409C60.0967 106.283 60.2114 106.215 60.3604 106.215H61.5873C61.7707 106.215 61.8739 106.318 61.8739 106.5V113.99C61.8739 114.172 61.7707 114.275 61.5873 114.275H60.5783C60.3948 114.275 60.2916 114.172 60.2916 113.99V108.761L57.8036 112.734C57.7234 112.859 57.6087 112.928 57.4596 112.928H56.8176C56.6685 112.928 56.5539 112.859 56.4736 112.734L53.9856 108.772V113.99C53.9856 114.172 53.8824 114.275 53.6989 114.275H52.69C52.5065 114.275 52.4033 114.172 52.4033 113.99V106.5C52.4033 106.318 52.5065 106.215 52.69 106.215H53.9283ZM68.9268 106.215C69.0873 106.215 69.1905 106.283 69.2593 106.42L72.8939 113.944C72.9856 114.138 72.8939 114.275 72.6761 114.275H71.518C71.3575 114.275 71.2429 114.206 71.1741 114.058L70.1612 111.942H66.5345L65.4527 114.058C65.384 114.195 65.2693 114.275 65.1088 114.275H63.9508C63.7329 114.275 63.6412 114.138 63.7329 113.944L67.379 106.42C67.4478 106.283 67.5509 106.215 67.7115 106.215H68.9268ZM107.797 106.215C107.957 106.215 108.06 106.283 108.129 106.42L111.764 113.944C111.855 114.138 111.764 114.275 111.546 114.275H110.388C110.227 114.275 110.113 114.206 110.044 114.058L109.031 111.942H105.404L104.323 114.058C104.254 114.195 104.139 114.275 103.979 114.275H102.821C102.603 114.275 102.511 114.138 102.603 113.944L106.249 106.42C106.318 106.283 106.421 106.215 106.581 106.215H107.797ZM76.06 106.215C76.2434 106.215 76.3466 106.318 76.3466 106.5V109.56H77.8601L80.543 106.375C80.6348 106.26 80.7494 106.215 80.8985 106.215H82.1482C82.389 106.215 82.4693 106.375 82.3087 106.569L79.1901 110.233L82.3087 113.921C82.4693 114.115 82.389 114.275 82.1482 114.275H80.8985C80.7494 114.275 80.6348 114.229 80.543 114.115L77.8601 110.976H76.3466V113.99C76.3466 114.172 76.2434 114.275 76.06 114.275H75.0396C74.8561 114.275 74.7529 114.172 74.7529 113.99V106.5C74.7529 106.318 74.8561 106.215 75.0396 106.215H76.06ZM85.5551 106.215C85.7385 106.215 85.8417 106.318 85.8417 106.5V111.558C85.8417 112.54 86.1972 112.757 86.9654 112.757H89.5451C90.3133 112.757 90.6573 112.54 90.6573 111.558V106.5C90.6573 106.318 90.7605 106.215 90.9439 106.215H91.9758C92.1593 106.215 92.2625 106.318 92.2625 106.5V111.501C92.2625 113.202 91.4369 114.275 89.5451 114.275H86.9654C85.0621 114.275 84.248 113.202 84.248 111.501V106.5C84.248 106.318 84.3512 106.215 84.5347 106.215H85.5551ZM96.0362 106.215C96.2197 106.215 96.3229 106.318 96.3229 106.5V112.757H101.012C101.196 112.757 101.299 112.859 101.299 113.042V113.99C101.299 114.172 101.196 114.275 101.012 114.275H95.0158C94.8323 114.275 94.7291 114.172 94.7291 113.99V106.5C94.7291 106.318 94.8323 106.215 95.0158 106.215H96.0362ZM68.3306 107.87L66.977 110.651H69.7319L68.3306 107.87ZM107.2 107.87L105.847 110.651H108.602L107.2 107.87Z"
      fill="#0517F8"
    />
    <g clipPath="url(#clip0_10992_155205)">
      <path
        d="M101.868 65.5878L102.887 65.888L103.125 64.8524C104.556 58.6179 110.141 53.9648 116.812 53.9648C124.574 53.9648 130.869 60.2559 130.869 68.0201C130.869 68.542 130.832 69.0644 130.776 69.5951L130.649 70.795L131.852 70.697C132.119 70.6753 132.375 70.6615 132.627 70.6615C137.665 70.6615 141.745 74.7428 141.745 79.78C141.745 84.7926 137.703 88.8622 132.698 88.9018L132.553 88.9029H98.4742C91.8989 88.9029 86.5695 83.5701 86.5695 77.0003C86.5695 70.4251 91.8989 65.0946 98.4742 65.0946C99.6496 65.0946 100.787 65.2695 101.868 65.5878Z"
        fill="#0517F8"
        stroke="#172B4D"
        strokeWidth="2"
      />
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M112.994 77.1975L119.451 67.963C119.793 67.4736 120.468 67.353 120.958 67.6936C121.449 68.0343 121.569 68.7072 121.227 69.1966L114.11 79.3753C113.86 79.7339 113.43 79.8945 113.026 79.8207C112.787 79.8301 112.543 79.7688 112.327 79.6299L108.551 77.2006C107.992 76.841 107.832 76.0974 108.193 75.5396C108.554 74.9817 109.3 74.821 109.859 75.1806L112.994 77.1975Z"
        fill="white"
      />
      <path
        d="M137.894 58.5574C137.894 60.793 137.623 62.9672 137.153 65.0755C137.077 65.0502 137.001 65.0254 136.924 65.0013C135.463 55.2151 127.003 47.6855 116.812 47.6855C108.971 47.6855 102.06 52.0818 98.6708 58.8165C98.6053 58.8156 98.5397 58.8151 98.4741 58.8151C88.4461 58.8151 80.2898 66.9725 80.2898 77.0004C80.2898 81.5551 81.9842 85.7146 84.7598 88.9029H24.8958L24.3972 88.8991C11.458 88.7982 1 78.2813 1 65.3182C1 52.2927 11.5572 41.7357 24.5814 41.7357C25.2446 41.7357 25.9093 41.7718 26.5786 41.8265L27.7901 41.9254L27.6537 40.7176C27.5059 39.4096 27.4273 38.0844 27.4273 36.7416C27.4273 17.0991 43.3494 1.17432 62.9935 1.17432C79.8793 1.17432 94.0088 12.9501 97.6457 28.7384L97.8846 29.7755L98.9049 29.4725C101.642 28.6595 104.541 28.2175 107.549 28.2175C124.307 28.2175 137.894 41.8003 137.894 58.5574Z"
        fill="#F3F4F6"
        stroke="#172B4D"
        strokeWidth="2"
      />
    </g>
    <defs>
      <clipPath id="clip0_10992_155205">
        <rect width="142.745" height="90" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const DevicesIcon = () => (
  <svg
    width="179"
    height="124"
    viewBox="0 0 179 124"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M117.259 54.0652L117.15 54.1807L129.496 65.7278L129.604 65.6124L117.259 54.0652Z"
      fill="#0C266D"
    />
    <path
      d="M167.179 1.98828H22.985C20.8791 1.98828 19.1719 3.68986 19.1719 5.78886V90.2283C19.1719 92.3273 20.8791 94.0289 22.985 94.0289H167.179C169.285 94.0289 170.992 92.3273 170.992 90.2283V5.78886C170.992 3.68986 169.285 1.98828 167.179 1.98828Z"
      fill="#E8EAED"
    />
    <path
      d="M167.179 95.1341H22.9849C21.6789 95.1333 20.4266 94.6158 19.5031 93.6953C18.5796 92.7749 18.0604 91.5267 18.0596 90.225V5.79825C18.0604 4.49653 18.5796 3.24837 19.5031 2.32792C20.4266 1.40747 21.6789 0.889999 22.9849 0.88916H167.179C168.485 0.889999 169.737 1.40747 170.66 2.32792C171.584 3.24837 172.103 4.49653 172.104 5.79825V90.2345C172.101 91.5346 171.58 92.7803 170.657 93.6987C169.734 94.6171 168.483 95.1333 167.179 95.1341ZM22.9849 3.09667C22.2688 3.0975 21.5823 3.3814 21.0759 3.88608C20.5696 4.39076 20.2847 5.07502 20.2839 5.78874V90.225C20.2847 90.9388 20.5696 91.623 21.0759 92.1277C21.5823 92.6324 22.2688 92.9163 22.9849 92.9171H167.179C167.895 92.9163 168.581 92.6324 169.088 92.1277C169.594 91.623 169.879 90.9388 169.88 90.225V5.79825C169.879 5.08452 169.594 4.40027 169.088 3.89558C168.581 3.3909 167.895 3.107 167.179 3.10617L22.9849 3.09667Z"
      fill="#172B4D"
    />
    <path
      d="M31.5137 13.3584C32.672 13.3584 33.611 12.4225 33.611 11.2681C33.611 10.1136 32.672 9.17773 31.5137 9.17773C30.3555 9.17773 29.4165 10.1136 29.4165 11.2681C29.4165 12.4225 30.3555 13.3584 31.5137 13.3584Z"
      fill="#0517F8"
    />
    <path
      d="M38.7906 13.3584C39.9488 13.3584 40.8878 12.4225 40.8878 11.2681C40.8878 10.1136 39.9488 9.17773 38.7906 9.17773C37.6323 9.17773 36.6934 10.1136 36.6934 11.2681C36.6934 12.4225 37.6323 13.3584 38.7906 13.3584Z"
      fill="#0517F8"
    />
    <path
      d="M46.5757 13.3584C47.734 13.3584 48.673 12.4225 48.673 11.2681C48.673 10.1136 47.734 9.17773 46.5757 9.17773C45.4175 9.17773 44.4785 10.1136 44.4785 11.2681C44.4785 12.4225 45.4175 13.3584 46.5757 13.3584Z"
      fill="#0517F8"
    />
    <path
      d="M178.164 112.889H12V104.271C12.0008 102.97 12.52 101.722 13.4435 100.801C14.367 99.8806 15.6193 99.3631 16.9253 99.3623H173.239C174.545 99.3631 175.797 99.8806 176.72 100.801C177.644 101.722 178.163 102.97 178.164 104.271V112.889ZM14.2243 110.672H175.939V104.271C175.939 103.558 175.654 102.873 175.147 102.369C174.641 101.864 173.955 101.58 173.239 101.579H16.9253C16.2092 101.58 15.5227 101.864 15.0164 102.369C14.51 102.873 14.2252 103.558 14.2243 104.271V110.672Z"
      fill="#172B4D"
    />
    <path
      d="M134.098 93.7939H135.098V92.7939V27.0234C135.098 24.262 132.86 22.0234 130.098 22.0234H59.1473C56.3859 22.0234 54.1473 24.262 54.1473 27.0234V92.7939V93.7939H55.1473H134.098Z"
      fill="white"
      stroke="#172B4D"
      strokeWidth="2"
    />
    <path
      d="M55.1473 27.0234C55.1473 24.8143 56.9382 23.0234 59.1473 23.0234H130.098C132.307 23.0234 134.098 24.8143 134.098 27.0234V34.9579H55.1473V27.0234Z"
      fill="#E6E8FE"
    />
    <rect
      x="59.7378"
      y="27.6138"
      width="4.59016"
      height="4.59016"
      rx="2.29508"
      fill="#6974FB"
    />
    <rect
      x="66.1641"
      y="27.6138"
      width="21.1147"
      height="4.59016"
      rx="2.29508"
      fill="#6974FB"
    />
    <rect
      x="59.7378"
      y="42.3022"
      width="59.6721"
      height="1.83606"
      rx="0.918032"
      fill="#D1D5DB"
    />
    <rect
      x="59.7378"
      y="47.8105"
      width="59.6721"
      height="1.83606"
      rx="0.918032"
      fill="#D1D5DB"
    />
    <rect
      x="59.7378"
      y="80.8599"
      width="59.6721"
      height="1.83606"
      rx="0.918032"
      fill="#D1D5DB"
    />
    <rect
      x="59.7378"
      y="53.3188"
      width="27.541"
      height="1.83606"
      rx="0.918032"
      fill="#D1D5DB"
    />
    <rect
      x="1.16406"
      y="45.2969"
      width="34.9144"
      height="77.703"
      rx="4"
      fill="white"
      stroke="#172B4D"
      strokeWidth="2"
    />
    <rect x="5" y="62" width="25" height="1.6" rx="0.8" fill="#D1D5DB" />
    <rect x="5" y="101" width="25" height="1.6" rx="0.8" fill="#D1D5DB" />
    <rect x="5" y="86" width="25" height="1.6" rx="0.8" fill="#D1D5DB" />
    <rect x="5" y="65.6001" width="25" height="1.6" rx="0.8" fill="#D1D5DB" />
    <rect x="5" y="104.6" width="25" height="1.6" rx="0.8" fill="#D1D5DB" />
    <rect x="5" y="69.2002" width="12" height="1.6" rx="0.8" fill="#D1D5DB" />
    <rect x="5" y="108.2" width="12" height="1.6" rx="0.8" fill="#D1D5DB" />
    <path
      d="M2.16406 49.2969C2.16406 47.64 3.50721 46.2969 5.16406 46.2969H32.0784C33.7353 46.2969 35.0784 47.64 35.0784 49.2969V56.1712H2.16406V49.2969Z"
      fill="#E6E8FE"
    />
    <rect x="5" y="52" width="2" height="2" rx="1" fill="#6974FB" />
    <rect x="8" y="52" width="18" height="2" rx="1" fill="#6974FB" />
    <path
      d="M10.3926 46.2969H26.8498V46.7655C26.8498 47.87 25.9543 48.7655 24.8498 48.7655H12.3926C11.288 48.7655 10.3926 47.87 10.3926 46.7655V46.2969Z"
      fill="#172B4D"
    />
  </svg>
);
export const MakulaInterfaceIcon = () => (
  <svg
    width="167"
    height="113"
    viewBox="0 0 167 113"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M105.259 54.176L105.15 54.2915L117.496 65.8387L117.604 65.7232L105.259 54.176Z"
      fill="#0C266D"
    />
    <path
      d="M155.179 2.09912H10.9855C8.87956 2.09912 7.17236 3.8007 7.17236 5.8997V90.3391C7.17236 92.4381 8.87956 94.1397 10.9855 94.1397H155.179C157.285 94.1397 158.992 92.4381 158.992 90.3391V5.8997C158.992 3.8007 157.285 2.09912 155.179 2.09912Z"
      fill="#E8EAED"
    />
    <rect
      x="7.26172"
      y="1.93799"
      width="151.639"
      height="91.9672"
      rx="4"
      fill="white"
      stroke="#172B4D"
      strokeWidth="2"
    />
    <rect
      x="50.4917"
      y="18.5444"
      width="102.82"
      height="0.918032"
      rx="0.459016"
      fill="#E8EAED"
    />
    <circle cx="53.2458" cy="13.0363" r="2.7541" fill="#E6E8FE" />
    <rect
      x="58.7544"
      y="12.1182"
      width="35.8033"
      height="1.83606"
      rx="0.918032"
      fill="#CDD1FE"
    />
    <rect
      x="50.4917"
      y="31.397"
      width="102.82"
      height="0.918032"
      rx="0.459016"
      fill="#E8EAED"
    />
    <circle cx="53.2458" cy="25.8889" r="2.7541" fill="#E6E8FE" />
    <rect
      x="58.7544"
      y="24.9707"
      width="35.8033"
      height="1.83606"
      rx="0.918032"
      fill="#CDD1FE"
    />
    <rect
      x="50.4917"
      y="44.2495"
      width="102.82"
      height="0.918032"
      rx="0.459016"
      fill="#E8EAED"
    />
    <circle cx="53.2458" cy="38.7414" r="2.7541" fill="#E6E8FE" />
    <rect
      x="58.7544"
      y="37.8232"
      width="35.8033"
      height="1.83606"
      rx="0.918032"
      fill="#CDD1FE"
    />
    <rect
      x="8.26172"
      y="2.93799"
      width="34.8852"
      height="89.9672"
      fill="#F3F4F6"
    />
    <path
      d="M155.179 95.245H10.9854C9.67935 95.2441 8.42707 94.7266 7.50358 93.8062C6.58009 92.8857 6.0609 91.6376 6.06006 90.3359V5.90909C6.0609 4.60737 6.58009 3.35922 7.50358 2.43876C8.42707 1.51831 9.67935 1.00084 10.9854 1H155.179C156.485 1.00084 157.737 1.51831 158.661 2.43876C159.584 3.35922 160.104 4.60737 160.104 5.90909V90.3454C160.101 91.6454 159.581 92.8912 158.658 93.8095C157.734 94.728 156.484 95.2441 155.179 95.245ZM10.9854 3.20751C10.2693 3.20834 9.58276 3.49224 9.07641 3.99692C8.57007 4.5016 8.28523 5.18586 8.28439 5.89959V90.3359C8.28523 91.0496 8.57007 91.7338 9.07641 92.2385C9.58276 92.7432 10.2693 93.0271 10.9854 93.0279H155.179C155.895 93.0271 156.582 92.7432 157.088 92.2385C157.594 91.7338 157.879 91.0496 157.88 90.3359V5.90909C157.879 5.19536 157.594 4.51111 157.088 4.00642C156.582 3.50174 155.895 3.21784 155.179 3.21701L10.9854 3.20751Z"
      fill="#172B4D"
    />
    <path
      d="M19.5142 13.4692C20.6725 13.4692 21.6114 12.5334 21.6114 11.3789C21.6114 10.2244 20.6725 9.28857 19.5142 9.28857C18.356 9.28857 17.417 10.2244 17.417 11.3789C17.417 12.5334 18.356 13.4692 19.5142 13.4692Z"
      fill="#0517F8"
    />
    <path
      d="M26.7911 13.4692C27.9493 13.4692 28.8883 12.5334 28.8883 11.3789C28.8883 10.2244 27.9493 9.28857 26.7911 9.28857C25.6328 9.28857 24.6938 10.2244 24.6938 11.3789C24.6938 12.5334 25.6328 13.4692 26.7911 13.4692Z"
      fill="#0517F8"
    />
    <path
      d="M34.5757 13.4692C35.734 13.4692 36.673 12.5334 36.673 11.3789C36.673 10.2244 35.734 9.28857 34.5757 9.28857C33.4175 9.28857 32.4785 10.2244 32.4785 11.3789C32.4785 12.5334 33.4175 13.4692 34.5757 13.4692Z"
      fill="#0517F8"
    />
    <path
      d="M166.164 113H0V104.382C0.000841775 103.081 0.520026 101.832 1.44352 100.912C2.36701 99.9915 3.61929 99.474 4.9253 99.4731H161.239C162.545 99.474 163.797 99.9915 164.72 100.912C165.644 101.832 166.163 103.081 166.164 104.382V113ZM2.22433 110.783H163.939V104.382C163.939 103.669 163.654 102.984 163.147 102.48C162.641 101.975 161.955 101.691 161.239 101.69H4.9253C4.20922 101.691 3.5227 101.975 3.01636 102.48C2.51001 102.984 2.22517 103.669 2.22433 104.382V110.783Z"
      fill="#172B4D"
    />
  </svg>
);

export const PaperIcon = () => (
  <svg
    width="173"
    height="133"
    viewBox="0 0 173 133"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M39.8184 130.083C39.9045 130.801 39.9883 131.519 40.0745 132.237C26.823 106.578 13.5716 80.9206 0.320154 55.265C0.147065 54.9298 0.0417081 54.5638 0.0100995 54.1879C-0.0215091 53.812 0.0212503 53.4335 0.135935 53.0741C0.25062 52.7147 0.434984 52.3814 0.678496 52.0933C0.922007 51.8052 1.2199 51.5679 1.55515 51.3949L15.164 44.3774C23.3813 72.945 31.5995 101.513 39.8184 130.083Z"
      fill="#A2AAB8"
    />
    <path
      d="M42.5779 133C41.7734 132.998 40.9911 132.735 40.3486 132.251C39.706 131.767 39.2378 131.087 39.0141 130.315L11.7868 35.6653C11.5162 34.7198 11.6316 33.7056 12.1076 32.8451C12.5837 31.9846 13.3815 31.3479 14.3262 31.0747L76.6647 13.1434C77.6101 12.8728 78.6243 12.9882 79.4849 13.4642C80.3454 13.9402 80.982 14.7381 81.2552 15.6828L108.483 110.33C108.753 111.276 108.638 112.29 108.162 113.151C107.686 114.012 106.888 114.649 105.943 114.923L43.6047 132.854C43.271 132.951 42.9253 133 42.5779 133ZM77.6867 14.6728C77.4972 14.673 77.3087 14.6996 77.1266 14.7518L14.7882 32.6855C14.2731 32.8394 13.8392 33.1897 13.5804 33.6609C13.3215 34.132 13.2584 34.6861 13.4048 35.2034L40.6225 129.853C40.7738 130.37 41.1233 130.806 41.595 131.067C42.0666 131.327 42.6221 131.391 43.1404 131.243L105.479 113.312C105.996 113.161 106.433 112.812 106.694 112.34C106.955 111.868 107.019 111.313 106.872 110.794L79.6445 16.1447C79.5709 15.8882 79.4471 15.6488 79.2803 15.4405C79.1135 15.2322 78.907 15.0591 78.6727 14.9313C78.3711 14.7632 78.0319 14.6743 77.6867 14.6728Z"
      fill="#172B4D"
    />
    <path d="M91.8603 87.8701L43.911 102.949L91.8603 87.8701Z" fill="white" />
    <path
      d="M43.9114 103.789C43.7111 103.789 43.5173 103.717 43.3652 103.587C43.2132 103.457 43.1128 103.276 43.0825 103.078C43.0521 102.88 43.0936 102.678 43.1996 102.508C43.3056 102.338 43.4689 102.212 43.6601 102.152L91.6094 87.0732C91.8214 87.0066 92.0512 87.0269 92.2482 87.1297C92.4453 87.2325 92.5934 87.4093 92.6601 87.6213C92.7267 87.8333 92.7064 88.0632 92.6036 88.2602C92.5009 88.4572 92.324 88.6054 92.112 88.672L44.1627 103.75C44.0814 103.776 43.9966 103.789 43.9114 103.789Z"
      fill="#D1D5DB"
    />
    <path d="M74.2811 31.2925L26.3293 46.3733L74.2811 31.2925Z" fill="white" />
    <path
      d="M26.3291 47.2228C26.1298 47.2202 25.9379 47.1467 25.788 47.0154C25.638 46.8841 25.5398 46.7036 25.511 46.5064C25.4822 46.3092 25.5246 46.1081 25.6307 45.9394C25.7368 45.7706 25.8995 45.6453 26.0897 45.5857L74.0414 30.5073C74.2534 30.4407 74.4832 30.461 74.6803 30.5638C74.8773 30.6665 75.0255 30.8434 75.0921 31.0554C75.1588 31.2674 75.1385 31.4972 75.0357 31.6943C74.9329 31.8913 74.7561 32.0395 74.544 32.1061L26.5923 47.1845C26.5072 47.2113 26.4183 47.2242 26.3291 47.2228Z"
      fill="#D1D5DB"
    />
    <path d="M77.0691 40.8257L29.1198 55.9041L77.0691 40.8257Z" fill="white" />
    <path
      d="M29.1106 56.7438C28.9113 56.7412 28.7194 56.6677 28.5695 56.5364C28.4195 56.4051 28.3213 56.2246 28.2925 56.0274C28.2637 55.8302 28.3061 55.6291 28.4122 55.4604C28.5182 55.2916 28.681 55.1662 28.8712 55.1067L76.8205 40.0283C76.9255 39.9953 77.036 39.9833 77.1456 39.993C77.2552 40.0027 77.3618 40.0339 77.4594 40.0848C77.557 40.1356 77.6436 40.2053 77.7142 40.2896C77.7849 40.374 77.8382 40.4714 77.8712 40.5764C77.9042 40.6814 77.9162 40.7918 77.9066 40.9014C77.8969 41.0111 77.8657 41.1177 77.8148 41.2153C77.7639 41.3128 77.6943 41.3994 77.6099 41.4701C77.5256 41.5407 77.4281 41.5941 77.3232 41.6271L29.3738 56.7055C29.2886 56.7321 29.1998 56.745 29.1106 56.7438Z"
      fill="#D1D5DB"
    />
    <path d="M61.062 57.5337L31.7452 66.7172L61.062 57.5337Z" fill="white" />
    <path
      d="M31.7455 67.5669C31.5462 67.5643 31.3543 67.4908 31.2044 67.3595C31.0544 67.2282 30.9562 67.0477 30.9274 66.8505C30.8986 66.6533 30.941 66.4522 31.0471 66.2835C31.1531 66.1147 31.3159 65.9893 31.5061 65.9298L60.8204 56.7464C60.9259 56.7118 61.0372 56.6984 61.1478 56.7072C61.2584 56.716 61.3662 56.7466 61.4649 56.7974C61.5636 56.8482 61.6512 56.9181 61.7227 57.003C61.7941 57.0879 61.848 57.1862 61.8811 57.2921C61.9143 57.3981 61.926 57.5095 61.9157 57.62C61.9054 57.7305 61.8733 57.8379 61.8211 57.9359C61.769 58.0338 61.6979 58.1205 61.612 58.1907C61.526 58.261 61.427 58.3135 61.3206 58.3452L31.9968 67.5166C31.9161 67.5463 31.8313 67.5632 31.7455 67.5669Z"
      fill="#D1D5DB"
    />
    <path
      d="M90.7456 100.481L85.0301 111.553C84.0919 107.578 83.1545 103.604 82.2179 99.6314C84.9145 72.5063 87.6134 45.3811 90.3148 18.2559C90.5566 17.2369 91.1597 16.3402 92.0123 15.7321C92.8649 15.1239 93.9092 14.8456 94.9514 14.9488C95.9936 15.052 96.963 15.5296 97.6798 16.2931C98.3966 17.0566 98.8123 18.0542 98.8496 19.1008L90.7456 100.481Z"
      fill="#CDD1FE"
    />
    <path
      d="M84.728 113.949L81.3772 99.6887L89.5148 18.0428C89.8108 16.8348 90.5328 15.7743 91.5481 15.056C92.5634 14.3377 93.8037 14.0099 95.0413 14.1327C96.2789 14.2555 97.4306 14.8208 98.2849 15.7246C99.1392 16.6285 99.6386 17.8102 99.6915 19.0528V19.1174L91.5707 100.732L84.728 113.949ZM83.0526 99.5763L85.3119 109.15L89.9168 100.234L98.0018 19.0839C97.959 18.2506 97.6189 17.4603 97.0432 16.8564C96.4675 16.2525 95.6944 15.875 94.8641 15.7925C94.0338 15.7099 93.2015 15.9278 92.5182 16.4066C91.8349 16.8853 91.3458 17.5932 91.1399 18.4018L83.0526 99.5763Z"
      fill="#172B4D"
    />
    <path
      d="M85.0301 111.553C84.0919 107.578 83.1545 103.604 82.2179 99.6313C82.6368 98.8044 83.296 98.1236 84.1089 97.6782C84.9218 97.2327 85.8504 97.0435 86.7727 97.1354C87.6951 97.2273 88.5681 97.596 89.2771 98.1931C89.9861 98.7902 90.498 99.5877 90.7456 100.481L85.0301 111.553Z"
      fill="white"
    />
    <path
      d="M84.7276 113.948L81.329 99.5258L81.4702 99.2506C81.9655 98.2734 82.7447 97.4688 83.7055 96.9423C84.6663 96.4158 85.7638 96.1922 86.854 96.3006C87.9442 96.4091 88.9762 96.8446 89.8144 97.55C90.6527 98.2554 91.2581 99.1978 91.5512 100.253L91.6421 100.572L84.7276 113.948ZM83.1001 99.7556L85.3164 109.154L89.8327 100.407C89.5891 99.7432 89.1631 99.162 88.6038 98.73C88.0445 98.298 87.3745 98.0327 86.671 97.9647C85.9676 97.8967 85.2592 98.0287 84.6275 98.3455C83.9957 98.6624 83.4663 99.1511 83.1001 99.7556Z"
      fill="#172B4D"
    />
    <path
      d="M97.0232 29.4591C96.8329 28.7209 96.4211 28.0585 95.8432 27.5612C95.2653 27.0639 94.549 26.7554 93.7906 26.6772C93.0322 26.599 92.268 26.7549 91.6008 27.1238C90.9336 27.4928 90.3953 28.0572 90.0584 28.7411L88.5577 27.9944C89.0474 27.0062 89.8272 26.1911 90.7929 25.6584C91.7586 25.1258 92.864 24.9008 93.9611 25.0138C95.0582 25.1268 96.0945 25.5723 96.9314 26.2906C97.7682 27.009 98.3656 27.9658 98.6435 29.0331L97.0232 29.4591Z"
      fill="#172B4D"
    />
    <path
      d="M96.4845 35.8423C96.2935 35.1042 95.8813 34.4422 95.3033 33.9451C94.7252 33.4481 94.0088 33.1398 93.2505 33.0616C92.4921 32.9834 91.7279 33.1392 91.0606 33.5079C90.3933 33.8766 89.8548 34.4406 89.5173 35.1243L88.019 34.3775C88.5085 33.3893 89.2882 32.5742 90.2537 32.0413C91.2192 31.5084 92.3245 31.2833 93.4215 31.396C94.5186 31.5087 95.555 31.954 96.392 32.672C97.2289 33.3901 97.8266 34.3467 98.1048 35.4139L96.4845 35.8423Z"
      fill="#172B4D"
    />
    <path
      d="M149.502 21.0708C149.48 23.7029 149.458 26.335 149.436 28.9672C151.089 27.3834 152.743 25.7774 154.396 24.1937"
      fill="white"
    />
    <path
      d="M149.502 21.0708C149.48 23.7029 149.458 26.335 149.436 28.9672C151.089 27.3834 152.743 25.7774 154.396 24.1937"
      stroke="#172B4D"
      strokeMiterlimit="10"
    />
    <path
      d="M171.107 2L163.721 15.7397L156.314 29.4794L149.435 21.7174L142.535 13.9553L156.821 7.97766L171.107 2Z"
      fill="#E5F7F5"
      stroke="#172B4D"
      strokeMiterlimit="10"
    />
    <path
      d="M149.436 21.7178C156.469 15.3387 163.501 8.93725 170.534 2.55811L149.436 21.7178Z"
      fill="white"
    />
    <path
      d="M149.436 21.7178C156.469 15.3387 163.501 8.93725 170.534 2.55811"
      stroke="#172B4D"
      strokeMiterlimit="10"
    />
    <path
      d="M145.863 26.1338C142.071 29.591 138.279 33.0482 134.509 36.5277L145.863 26.1338Z"
      fill="white"
    />
    <path
      d="M145.863 26.1338C142.071 29.591 138.279 33.0482 134.509 36.5277"
      stroke="#172B4D"
      strokeMiterlimit="10"
      strokeLinecap="round"
    />
    <path
      d="M142.668 33.606C138.876 37.0632 135.084 40.5204 131.314 43.9999L142.668 33.606Z"
      fill="white"
    />
    <path
      d="M142.668 33.606C138.876 37.0632 135.084 40.5204 131.314 43.9999"
      stroke="#172B4D"
      strokeMiterlimit="10"
      strokeLinecap="round"
    />
    <path
      d="M131.688 38.8027C129.792 40.5426 127.896 42.2601 126 44L131.688 38.8027Z"
      fill="white"
    />
    <path
      d="M131.688 38.8027C129.792 40.5426 127.896 42.2601 126 44"
      stroke="black"
      strokeMiterlimit="10"
      strokeLinecap="round"
    />
  </svg>
);
export const PDFIcon = ({ fillColor, ...props }) => (
  <svg
    width="28"
    height="30"
    viewBox="0 0 28 30"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.74998 0H18.6809L27.1249 8.41128V28.125C27.1249 29.1609 26.285 30 25.25 30H2.74998C1.71495 30 0.875 29.1609 0.875 28.125V1.87498C0.875 0.839081 1.71505 0 2.74998 0Z"
      fill={fillColor || "#EE0047"}
    />
    <path
      d="M27.0978 8.43437H20.5625C19.5275 8.43437 18.6875 7.59442 18.6875 6.55939V0.015625L27.0978 8.43437Z"
      fill={
        ["#fff", "#ffffff"].includes(fillColor.toLowerCase())
          ? "#174b4d"
          : "white"
      }
      fillOpacity="0.7"
    />
    <path
      d="M20.0903 16.0791C20.4044 16.0791 20.5582 15.8053 20.5582 15.54C20.5582 15.2653 20.3978 15 20.0903 15H18.3016C17.9519 15 17.7569 15.2897 17.7569 15.6094V20.0053C17.7569 20.3972 17.98 20.6147 18.2819 20.6147C18.5819 20.6147 18.806 20.3972 18.806 20.0053V18.7988H19.8879C20.2235 18.7988 20.3913 18.524 20.3913 18.2513C20.3913 17.9841 20.2235 17.7187 19.8879 17.7187H18.806V16.0791H20.0903ZM14.0444 15H12.7356C12.3803 15 12.1281 15.2438 12.1281 15.6056V20.0091C12.1281 20.4581 12.4909 20.5988 12.7505 20.5988H14.124C15.7496 20.5988 16.823 19.5291 16.823 17.8781C16.8222 16.1325 15.8116 15 14.0444 15ZM14.1072 19.5132H13.3094V16.0857H14.0285C15.1169 16.0857 15.5903 16.816 15.5903 17.8238C15.5903 18.7669 15.1253 19.5132 14.1072 19.5132ZM9.31283 15H8.01627C7.6497 15 7.44531 15.2418 7.44531 15.6094V20.0053C7.44531 20.3972 7.67967 20.6147 7.99464 20.6147C8.30961 20.6147 8.54397 20.3972 8.54397 20.0053V18.7218H9.35677C10.3599 18.7218 11.1877 18.0112 11.1877 16.8684C11.1878 15.75 10.3891 15 9.31283 15ZM9.29129 17.6907H8.54407V16.0322H9.29129C9.75254 16.0322 10.046 16.3922 10.046 16.8619C10.045 17.3307 9.75254 17.6907 9.29129 17.6907Z"
      fill={
        ["#fff", "#ffffff"].includes(fillColor.toLowerCase())
          ? "#174b4d"
          : "white"
      }
    />
  </svg>
);

export const NoTicketsMediumIcon = () => (
  <svg
    width="130"
    height="130"
    viewBox="0 0 130 130"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_9470_95240)">
      <path
        d="M53.2812 112.729C58.2274 109.834 108.351 80.4671 110.428 78.6176C111.731 77.4503 111.667 76.3738 111.667 76.3738C111.642 76.0418 111.533 75.7216 111.35 75.4435C110.65 74.3205 107.806 69.6424 103.914 63.2344"
        fill="#A2AAB8"
      />
      <path
        d="M52.8933 113.453C52.4661 113.454 52.0429 113.37 51.648 113.207C51.253 113.044 50.8942 112.805 50.592 112.503L42.1107 104.022L42.6733 103.475C43.7944 102.38 44.6872 101.075 45.2998 99.6328C45.9125 98.191 46.2328 96.6419 46.2423 95.0754C46.2518 93.5089 45.9503 91.9561 45.3552 90.507C44.7601 89.0578 43.8833 87.7413 42.7756 86.6335C41.6679 85.5258 40.3513 84.649 38.9022 84.0539C37.4531 83.4588 35.9002 83.1573 34.3337 83.1668C32.7672 83.1763 31.2181 83.4967 29.7763 84.1093C28.3346 84.722 27.0287 85.6147 25.9345 86.7358L25.3874 87.2984L16.9084 78.8194C16.6056 78.5166 16.3653 78.1571 16.2014 77.7615C16.0375 77.3659 15.9531 76.9418 15.9531 76.5136C15.9531 76.0853 16.0375 75.6613 16.2014 75.2656C16.3653 74.87 16.6056 74.5105 16.9084 74.2077L74.2105 16.9078C74.5128 16.6052 74.8717 16.3651 75.2667 16.2013C75.6618 16.0374 76.0853 15.9531 76.513 15.9531C76.9407 15.9531 77.3642 16.0374 77.7593 16.2013C78.1544 16.3651 78.5133 16.6052 78.8155 16.9078L87.2946 25.3869L86.7342 25.934C85.6047 27.0261 84.704 28.3322 84.0845 29.776C83.465 31.2198 83.1391 32.7725 83.1259 34.3436C83.1126 35.9146 83.4123 37.4726 84.0074 38.9267C84.6024 40.3807 85.481 41.7018 86.5919 42.8128C87.7028 43.9238 89.0237 44.8026 90.4777 45.3978C91.9317 45.9931 93.4896 46.293 95.0607 46.2799C96.6317 46.2669 98.1845 45.9412 99.6284 45.3219C101.072 44.7026 102.378 43.802 103.471 42.6727L104.02 42.1101L112.499 50.5892C113.109 51.2008 113.451 52.0292 113.451 52.8928C113.451 53.7564 113.109 54.5848 112.499 55.1964L55.197 112.503C54.8943 112.805 54.5351 113.044 54.1398 113.207C53.7445 113.371 53.3209 113.454 52.8933 113.453ZM44.308 104.019L51.6928 111.407C52.0136 111.725 52.4476 111.904 52.9 111.904C53.3524 111.904 53.7864 111.725 54.1072 111.407L111.407 54.1C111.726 53.7791 111.905 53.3452 111.905 52.8928C111.905 52.4404 111.726 52.0064 111.407 51.6856L104.02 44.3052C101.563 46.5662 98.3439 47.8175 95.005 47.8093C91.4525 47.8047 88.0469 46.3912 85.5352 43.879C83.0234 41.3669 81.6105 37.961 81.6064 34.4086C81.5973 31.0685 82.8477 27.8478 85.1084 25.3891L77.7235 18.0043C77.5653 17.8456 77.3773 17.7197 77.1704 17.6338C76.9634 17.5479 76.7415 17.5037 76.5175 17.5037C76.2934 17.5037 76.0715 17.5479 75.8646 17.6338C75.6576 17.7197 75.4696 17.8456 75.3114 18.0043L18.0048 75.3152C17.6855 75.6354 17.5061 76.0691 17.5061 76.5213C17.5061 76.9735 17.6855 77.4072 18.0048 77.7274L25.3897 85.1122C27.8466 82.8512 31.0658 81.6 34.4047 81.6081C37.9576 81.6122 41.3637 83.0254 43.876 85.5376C46.3882 88.0498 47.8014 91.456 47.8055 95.0088C47.8143 98.3453 46.5655 101.563 44.308 104.019Z"
        fill="#172B4D"
      />
      <path
        d="M43.0683 63.8365L36.4102 57.1784C36.1074 56.8756 35.6166 56.8756 35.3138 57.1784C35.0111 57.4811 35.0111 57.972 35.3138 58.2747L41.9719 64.9328C42.2747 65.2356 42.7655 65.2356 43.0683 64.9328C43.371 64.6301 43.371 64.1392 43.0683 63.8365Z"
        fill="#172B4D"
      />
      <path
        d="M71.8417 92.6099L65.1836 85.9518C64.8809 85.6491 64.39 85.6491 64.0872 85.9518C63.7845 86.2546 63.7845 86.7454 64.0872 87.0482L70.7453 93.7063C71.0481 94.009 71.539 94.009 71.8417 93.7063C72.1445 93.4035 72.1445 92.9127 71.8417 92.6099Z"
        fill="#172B4D"
      />
      <path
        d="M57.4667 78.2349L50.8086 71.5768C50.5059 71.2741 50.015 71.2741 49.7122 71.5768C49.4095 71.8796 49.4095 72.3704 49.7122 72.6732L56.3704 79.3313C56.6731 79.634 57.164 79.634 57.4667 79.3313C57.7695 79.0285 57.7695 78.5377 57.4667 78.2349Z"
        fill="#172B4D"
      />
      <path
        d="M60.8716 49.5379C66.3896 44.02 66.3896 35.0737 60.8716 29.5558C55.3537 24.0379 46.4074 24.0379 40.8895 29.5558C35.3716 35.0737 35.3716 44.02 40.8895 49.5379C46.4074 55.0559 55.3537 55.0559 60.8716 49.5379Z"
        fill="#E6E8FE"
      />
      <path
        d="M50.8769 54.4546C47.9289 54.4533 45.0474 53.5779 42.5968 51.9391C40.1463 50.3003 38.2366 47.9717 37.1094 45.2476C35.9821 42.5236 35.6879 39.5266 36.2638 36.6353C36.8398 33.7441 38.26 31.0885 40.3451 29.0044C42.4301 26.9203 45.0863 25.5012 47.9778 24.9266C50.8693 24.3519 53.8662 24.6475 56.5897 25.776C59.3132 26.9045 61.641 28.8151 63.2787 31.2664C64.9164 33.7177 65.7905 36.5996 65.7905 39.5476C65.7858 43.5009 64.2128 47.2908 61.4168 50.0856C58.6208 52.8804 54.8302 54.4516 50.8769 54.4546ZM50.8769 26.1933C48.2354 26.1947 45.6537 26.9792 43.458 28.4477C41.2624 29.9162 39.5515 32.0028 38.5417 34.4436C37.5318 36.8845 37.2684 39.5699 37.7847 42.1604C38.301 44.7509 39.5738 47.1302 41.4422 48.9974C43.3106 50.8646 45.6908 52.1358 48.2816 52.6504C50.8725 53.165 53.5578 52.8998 55.9979 51.8883C58.438 50.8769 60.5235 49.1646 61.9906 46.968C63.4576 44.7714 64.2404 42.1891 64.24 39.5476C64.2359 36.0056 62.8264 32.6099 60.321 30.1061C57.8155 27.6023 54.4189 26.1951 50.8769 26.1933V26.1933Z"
        fill="#172B4D"
      />
      <path
        d="M56.7132 43.7379L47.324 34.9147C46.9906 34.6014 46.4664 34.6177 46.1532 34.9511C45.8399 35.2844 45.8562 35.8086 46.1895 36.1219L55.5788 44.9452C55.9121 45.2584 56.4363 45.2421 56.7496 44.9088C57.0629 44.5754 57.0466 44.0512 56.7132 43.7379Z"
        fill="#0517F8"
      />
      <path
        d="M55.4477 34.4457L46.3616 43.456C46.0195 43.7953 46.0172 44.3476 46.3565 44.6898C46.6957 45.0319 47.2481 45.0342 47.5902 44.6949L56.6763 35.6846C57.0184 35.3454 57.0207 34.793 56.6815 34.4509C56.3422 34.1088 55.7899 34.1065 55.4477 34.4457Z"
        fill="#0517F8"
      />
    </g>
    <defs>
      <clipPath id="clip0_9470_95240">
        <rect
          width="97.5"
          height="97.5"
          fill="white"
          transform="translate(15.9531 15.9531)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SidebarAssetsFilledIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.77803 8.03596C3.86277 8.03596 4.74213 8.91529 4.74213 10C4.74213 11.0847 3.86277 11.964 2.77803 11.964C1.69328 11.964 0.813925 11.0847 0.813926 10C0.813926 8.91529 1.69328 8.03596 2.77803 8.03596Z"
      fill="#0517F8"
    />
    <path
      d="M2.77803 8.03596C3.86277 8.03596 4.74213 8.91529 4.74213 10C4.74213 11.0847 3.86277 11.964 2.77803 11.964C1.69328 11.964 0.813925 11.0847 0.813925 10C0.813925 8.91529 1.69328 8.03596 2.77803 8.03596"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.2219 8.03596C18.3066 8.03596 19.186 8.91529 19.186 10C19.186 11.0847 18.3066 11.964 17.2219 11.964C16.1371 11.964 15.2578 11.0847 15.2578 10C15.2578 8.91529 16.1371 8.03596 17.2219 8.03596Z"
      fill="#0517F8"
    />
    <path
      d="M17.2219 8.03596C18.3066 8.03596 19.186 8.91529 19.186 10C19.186 11.0847 18.3066 11.964 17.2219 11.964C16.1371 11.964 15.2578 11.0847 15.2578 10C15.2578 8.91529 16.1371 8.03596 17.2219 8.03596"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.55572 13.3343L6.66679 14.4453L5.55572 13.3343Z"
      fill="#0517F8"
    />
    <path
      d="M5.55572 13.3343L6.66679 14.4453"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3331 5.55549L14.4441 6.66652L13.3331 5.55549Z"
      fill="#0517F8"
    />
    <path
      d="M13.3331 5.55549L14.4441 6.66652"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.99983 15.2584C11.0846 15.2584 11.9639 16.1377 11.9639 17.2224C11.9639 18.3071 11.0846 19.1864 9.99983 19.1864C8.91508 19.1864 8.03573 18.3071 8.03573 17.2224C8.03573 16.1377 8.91509 15.2584 9.99983 15.2584Z"
      fill="#0517F8"
    />
    <path
      d="M9.99983 15.2584C11.0846 15.2584 11.9639 16.1377 11.9639 17.2224C11.9639 18.3071 11.0846 19.1864 9.99983 19.1864C8.91508 19.1864 8.03573 18.3071 8.03573 17.2224C8.03573 16.1377 8.91509 15.2584 9.99983 15.2584"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.99983 0.813551C11.0846 0.813551 11.9639 1.69288 11.9639 2.77759C11.9639 3.8623 11.0846 4.74163 9.99983 4.74162C8.91508 4.74162 8.03573 3.8623 8.03573 2.77759C8.03573 1.69288 8.91509 0.81355 9.99983 0.813551Z"
      fill="#0517F8"
    />
    <path
      d="M9.99983 0.813551C11.0846 0.813551 11.9639 1.69288 11.9639 2.77759C11.9639 3.8623 11.0846 4.74163 9.99983 4.74162C8.91508 4.74162 8.03573 3.8623 8.03573 2.77759C8.03573 1.69288 8.91509 0.81355 9.99983 0.813551"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3331 14.4445L14.4441 13.3335L13.3331 14.4445Z"
      fill="#0517F8"
    />
    <path
      d="M13.3331 14.4445L14.4441 13.3335"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.55572 6.66545L6.66679 5.55442L5.55572 6.66545Z"
      fill="#0517F8"
    />
    <path
      d="M5.55572 6.66545L6.66679 5.55442"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SidebarMachinesFilledIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_12094_753)">
      <g clipPath="url(#clip1_12094_753)">
        <path
          d="M6.87456 5.88295C6.64268 5.64762 6.5127 5.33051 6.5127 5.00014C6.5127 4.66977 6.64268 4.35266 6.87456 4.11733L9.11675 1.87514C9.23225 1.75845 9.36975 1.66582 9.52128 1.6026C9.67281 1.53939 9.83537 1.50684 9.99956 1.50684C10.1637 1.50684 10.3263 1.53939 10.4778 1.6026C10.6294 1.66582 10.7669 1.75845 10.8824 1.87514L13.1246 4.11733C13.3564 4.35266 13.4864 4.66977 13.4864 5.00014C13.4864 5.33051 13.3564 5.64762 13.1246 5.88295L10.8824 8.12514C10.7669 8.24183 10.6294 8.33446 10.4778 8.39768C10.3263 8.4609 10.1637 8.49344 9.99956 8.49344C9.83537 8.49344 9.67281 8.4609 9.52128 8.39768C9.36975 8.33446 9.23225 8.24183 9.11675 8.12514L6.87456 5.88295ZM18.1246 9.11733L15.8824 6.87514C15.7669 6.75845 15.6294 6.66582 15.4778 6.6026C15.3263 6.53939 15.1638 6.50684 14.9996 6.50684C14.8354 6.50684 14.6728 6.53939 14.5213 6.6026C14.3698 6.66582 14.2323 6.75845 14.1167 6.87514L11.8746 9.11733C11.6427 9.35266 11.5127 9.66977 11.5127 10.0001C11.5127 10.3305 11.6427 10.6476 11.8746 10.883L14.1167 13.1251C14.2323 13.2418 14.3698 13.3345 14.5213 13.3977C14.6728 13.4609 14.8354 13.4934 14.9996 13.4934C15.1638 13.4934 15.3263 13.4609 15.4778 13.3977C15.6294 13.3345 15.7669 13.2418 15.8824 13.1251L18.1246 10.883C18.3564 10.6476 18.4864 10.3305 18.4864 10.0001C18.4864 9.66977 18.3564 9.35266 18.1246 9.11733ZM8.12456 9.11733L5.88237 6.87514C5.76686 6.75845 5.62937 6.66582 5.47784 6.6026C5.3263 6.53939 5.16374 6.50684 4.99956 6.50684C4.83537 6.50684 4.67281 6.53939 4.52128 6.6026C4.36975 6.66582 4.23225 6.75845 4.11674 6.87514L1.87455 9.11733C1.64268 9.35266 1.5127 9.66977 1.5127 10.0001C1.5127 10.3305 1.64268 10.6476 1.87455 10.883L4.11674 13.1251C4.23225 13.2418 4.36975 13.3345 4.52128 13.3977C4.67281 13.4609 4.83537 13.4934 4.99956 13.4934C5.16374 13.4934 5.3263 13.4609 5.47784 13.3977C5.62937 13.3345 5.76686 13.2418 5.88237 13.1251L8.12456 10.883C8.35643 10.6476 8.48642 10.3305 8.48642 10.0001C8.48642 9.66977 8.35643 9.35266 8.12456 9.11733ZM10.8824 11.8751C10.7669 11.7585 10.6294 11.6658 10.4778 11.6026C10.3263 11.5394 10.1637 11.5068 9.99956 11.5068C9.83537 11.5068 9.67281 11.5394 9.52128 11.6026C9.36975 11.6658 9.23225 11.7585 9.11675 11.8751L6.87456 14.1173C6.64268 14.3527 6.5127 14.6698 6.5127 15.0001C6.5127 15.3305 6.64268 15.6476 6.87456 15.883L9.11675 18.1251C9.23225 18.2418 9.36975 18.3345 9.52128 18.3977C9.67281 18.4609 9.83537 18.4934 9.99956 18.4934C10.1637 18.4934 10.3263 18.4609 10.4778 18.3977C10.6294 18.3345 10.7669 18.2418 10.8824 18.1251L13.1246 15.883C13.3564 15.6476 13.4864 15.3305 13.4864 15.0001C13.4864 14.6698 13.3564 14.3527 13.1246 14.1173L10.8824 11.8751Z"
          fill="#0517F8"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_12094_753">
        <rect width="20" height="20" fill="white" />
      </clipPath>
      <clipPath id="clip1_12094_753">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const MachineTemplateIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_9745_96219)">
      <path
        d="M6.5616 11.6923L3.69233 14.5616C3.38008 14.8738 3.38008 15.3801 3.69233 15.6923L6.55948 18.5595C6.87173 18.8717 7.37798 18.8717 7.69023 18.5595L10.5595 15.6902C10.8717 15.378 10.8717 14.8717 10.5595 14.5595L7.69235 11.6923C7.3801 11.3801 6.87385 11.3801 6.5616 11.6923Z"
        stroke="#748094"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.875 2.5V6.875H16.25"
        stroke="#748094"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M12.5 17.5H15.625C15.7908 17.5 15.9497 17.4342 16.0669 17.3169C16.1842 17.1997 16.25 17.0408 16.25 16.875V6.875L11.875 2.5H4.375C4.20924 2.5 4.05027 2.56585 3.93306 2.68306C3.81585 2.80027 3.75 2.95924 3.75 3.125V10.625"
        stroke="#748094"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_9745_96219">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const MachineNoDoc = (props) => (
  <svg
    width="130"
    height="130"
    viewBox="0 0 130 130"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M73.6608 74.3446L73.5908 74.4189L81.5374 81.8515L81.6074 81.7772L73.6608 74.3446Z"
      fill="#0C266D"
    />
    <path
      d="M105.793 40.8242H12.9798C11.6243 40.8242 10.5254 41.9195 10.5254 43.2705V97.6216C10.5254 98.9727 11.6243 100.068 12.9798 100.068H105.793C107.148 100.068 108.247 98.9727 108.247 97.6216V43.2705C108.247 41.9195 107.148 40.8242 105.793 40.8242Z"
      fill="#E8EAED"
    />
    <path
      d="M105.793 100.779H12.9798C12.1392 100.779 11.3331 100.446 10.7387 99.8532C10.1443 99.2607 9.81011 98.4573 9.80957 97.6194V43.2765C9.81011 42.4387 10.1443 41.6353 10.7387 41.0428C11.3331 40.4503 12.1392 40.1172 12.9798 40.1167H105.793C106.634 40.1172 107.44 40.4503 108.034 41.0428C108.628 41.6353 108.963 42.4387 108.963 43.2765V97.6256C108.961 98.4624 108.626 99.2642 108.032 99.8553C107.438 100.446 106.633 100.779 105.793 100.779ZM12.9798 41.5376C12.5189 41.5381 12.077 41.7209 11.7511 42.0457C11.4252 42.3706 11.2418 42.811 11.2413 43.2704V97.6194C11.2418 98.0788 11.4252 98.5193 11.7511 98.8441C12.077 99.169 12.5189 99.3517 12.9798 99.3523H105.793C106.254 99.3517 106.696 99.169 107.022 98.8441C107.348 98.5193 107.531 98.0788 107.531 97.6194V43.2765C107.531 42.8171 107.348 42.3767 107.022 42.0518C106.696 41.727 106.254 41.5443 105.793 41.5437L12.9798 41.5376Z"
      fill="#172B4D"
    />
    <path
      d="M47.6507 94.3783C46.9898 94.3715 46.3514 94.1382 45.8426 93.7178C45.1789 93.1719 44.7358 92.4051 44.5949 91.5589L38.551 57.547C38.4017 56.7065 38.5538 55.8406 38.9805 55.1006C39.1881 54.7508 39.4673 54.4485 39.8 54.2135C40.1327 53.9785 40.5114 53.8161 40.9113 53.7368L53.2753 51.5474L65.5473 60.9249L70.1984 87.019C70.4519 87.8449 70.3787 88.7363 69.9939 89.5101C69.78 89.9066 69.4664 90.2408 69.0838 90.4799C68.7011 90.719 68.2627 90.8548 67.8115 90.874L48.1293 94.3396C47.9712 94.3667 47.811 94.3797 47.6507 94.3783V94.3783ZM53.0115 52.6299L41.0892 54.7418C40.8339 54.7947 40.5927 54.9007 40.3812 55.0528C40.1698 55.2049 39.9929 55.3997 39.862 55.6246C39.5634 56.1567 39.4579 56.7751 39.5634 57.3757L45.6012 91.3877C45.6995 91.9909 46.0134 92.5383 46.4848 92.9289C46.6848 93.0978 46.9189 93.2216 47.1713 93.292C47.4237 93.3624 47.6884 93.3776 47.9472 93.3366L67.7154 89.8465H67.7604C68.036 89.8351 68.3038 89.7525 68.5377 89.6066C68.7715 89.4608 68.9632 89.2568 69.0939 89.0148C69.3526 88.4749 69.3979 87.8577 69.2207 87.286L69.2023 87.2188L64.6106 61.4835L53.0115 52.6299Z"
      fill="#172B4D"
    />
    <path
      d="M18.47 48.1426C19.2156 48.1426 19.82 47.5402 19.82 46.7971C19.82 46.0541 19.2156 45.4517 18.47 45.4517C17.7245 45.4517 17.1201 46.0541 17.1201 46.7971C17.1201 47.5402 17.7245 48.1426 18.47 48.1426Z"
      fill="#0517F8"
    />
    <path
      d="M23.1536 48.1426C23.8992 48.1426 24.5036 47.5402 24.5036 46.7971C24.5036 46.0541 23.8992 45.4517 23.1536 45.4517C22.4081 45.4517 21.8037 46.0541 21.8037 46.7971C21.8037 47.5402 22.4081 48.1426 23.1536 48.1426Z"
      fill="#0517F8"
    />
    <path
      d="M28.1644 48.1426C28.9099 48.1426 29.5143 47.5402 29.5143 46.7971C29.5143 46.0541 28.9099 45.4517 28.1644 45.4517C27.4188 45.4517 26.8145 46.0541 26.8145 46.7971C26.8145 47.5402 27.4188 48.1426 28.1644 48.1426Z"
      fill="#0517F8"
    />
    <path
      d="M74.4279 91.5912L51.7922 95.5868C51.4241 95.652 51.0467 95.6441 50.6816 95.5638C50.3165 95.4835 49.9709 95.3322 49.6645 95.1186C49.3581 94.9051 49.0969 94.6334 48.8959 94.3192C48.6949 94.0049 48.558 93.6543 48.4931 93.2873L42.4389 59.2672C42.3739 58.9001 42.382 58.5239 42.4628 58.16C42.5437 57.7961 42.6957 57.4516 42.9101 57.1463C43.1245 56.8409 43.3972 56.5807 43.7126 56.3804C44.028 56.1801 44.3798 56.0436 44.7481 55.9789L58.6789 53.5122L72.0963 62.3618L76.7228 88.2907C76.8394 88.6438 76.8781 89.0178 76.8365 89.3872C76.7948 89.7566 76.6737 90.1127 76.4814 90.4312C75.7881 91.5321 74.5179 91.579 74.4279 91.5912Z"
      fill="white"
    />
    <path
      d="M51.2809 96.1314C50.4931 96.1284 49.7313 95.8501 49.1281 95.345C48.5249 94.8399 48.1185 94.14 47.9797 93.3671L41.9358 59.3571C41.7829 58.4839 41.9836 57.5858 42.4939 56.8598C43.0042 56.1337 43.7824 55.6389 44.6581 55.4838L58.7934 52.9824L72.5667 62.0664L77.2239 88.1666C77.3568 88.5871 77.3982 89.0309 77.3453 89.4686C77.2924 89.9063 77.1464 90.3277 76.9171 90.7046C76.1337 91.94 74.7715 92.0746 74.4729 92.0889L51.874 96.0764C51.6783 96.1118 51.4798 96.1302 51.2809 96.1314V96.1314ZM58.5704 54.0486L44.836 56.4807C44.5341 56.5338 44.2456 56.6458 43.9871 56.8101C43.7285 56.9744 43.505 57.1879 43.3293 57.4384C43.1536 57.6889 43.0292 57.9714 42.9631 58.2699C42.897 58.5683 42.8905 58.8768 42.9441 59.1777L48.9881 93.1877C49.0414 93.4888 49.1538 93.7766 49.3188 94.0344C49.4838 94.2922 49.6982 94.5151 49.9497 94.6902C50.2013 94.8654 50.485 94.9894 50.7846 95.0551C51.0843 95.1209 51.394 95.1271 51.6961 95.0734L74.3686 91.0716H74.4095C74.7342 91.0553 75.0507 90.9645 75.3345 90.8065C75.6182 90.6485 75.8616 90.4274 76.0458 90.1603C76.1991 89.9072 76.2954 89.6238 76.3279 89.3299C76.3604 89.0359 76.3283 88.7385 76.2339 88.4581L76.2155 88.3806L71.636 62.6658L58.5704 54.0486Z"
      fill="#172B4D"
    />
    <path
      d="M71.6667 62.5982C68.0014 63.2873 65.1216 63.6502 63.5978 63.7297C63.0742 63.7562 62.1661 63.7725 61.4257 63.2506C61.0443 62.9832 60.7335 62.6279 60.5196 62.215C60.4114 62.0083 60.3289 61.7893 60.2742 61.5626C60.2557 61.4872 60.2455 61.4281 60.2394 61.3996V61.3812C60.2292 61.3241 59.7015 59.1673 58.417 53.9546"
      fill="#CDD1FE"
    />
    <path
      d="M63.2341 64.2495C62.4897 64.2766 61.7554 64.0713 61.1336 63.6624C60.4509 63.1892 59.9662 62.4826 59.7714 61.6768C59.751 61.5892 59.7366 61.5178 59.7305 61.4831C59.708 61.3833 59.3767 60.0194 57.9102 54.0708L58.9042 53.8262C60.7143 61.1774 60.7266 61.2446 60.745 61.2854C60.7634 61.3262 60.7613 61.3751 60.7757 61.4363C60.9127 62.0006 61.2525 62.4952 61.7308 62.8266C62.3751 63.2751 63.2219 63.2343 63.5839 63.2139C65.3245 63.1242 68.3168 62.7043 71.5852 62.0907L71.7755 63.0936C68.462 63.7052 65.4206 64.1415 63.635 64.2312C63.5 64.2434 63.3691 64.2495 63.2341 64.2495Z"
      fill="#172B4D"
    />
    <path
      d="M112.864 112.208H5.90918V106.661C5.90972 105.823 6.2439 105.02 6.83833 104.427C7.43275 103.835 8.23881 103.502 9.07945 103.501H109.693C110.534 103.502 111.34 103.835 111.935 104.427C112.529 105.02 112.863 105.823 112.864 106.661V112.208ZM7.34091 110.781H111.432V106.661C111.431 106.201 111.248 105.761 110.922 105.436C110.596 105.111 110.154 104.929 109.693 104.928H9.07945C8.61852 104.929 8.17664 105.111 7.85072 105.436C7.52479 105.761 7.34145 106.201 7.34091 106.661V110.781Z"
      fill="#172B4D"
    />
    <path
      d="M60.3434 88.0891C58.3612 88.0891 56.4236 87.5033 54.7754 86.4056C53.1273 85.308 51.8427 83.7479 51.0842 81.9227C50.3256 80.0974 50.1272 78.0889 50.5139 76.1512C50.9006 74.2135 51.8551 72.4336 53.2567 71.0366C54.6583 69.6396 56.4441 68.6882 58.3882 68.3028C60.3323 67.9174 62.3474 68.1152 64.1787 68.8712C66.01 69.6273 67.5753 70.9076 68.6765 72.5503C69.7778 74.193 70.3656 76.1243 70.3656 78.1C70.3629 80.7484 69.3061 83.2876 67.4272 85.1604C65.5482 87.0331 63.0006 88.0864 60.3434 88.0891V88.0891ZM60.3434 69.1301C58.5635 69.1301 56.8235 69.6562 55.3436 70.6418C53.8636 71.6275 52.7102 73.0284 52.029 74.6674C51.3479 76.3064 51.1696 78.1099 51.5169 79.8499C51.8641 81.5899 52.7212 83.1882 53.9798 84.4426C55.2384 85.6971 56.842 86.5514 58.5877 86.8975C60.3334 87.2436 62.1429 87.0659 63.7874 86.387C65.4318 85.7081 66.8373 84.5584 67.8262 83.0834C68.8151 81.6083 69.3429 79.874 69.3429 78.1C69.3407 75.7215 68.3919 73.441 66.7047 71.759C65.0175 70.077 62.7298 69.1308 60.3434 69.1281V69.1301Z"
      fill="#172B4D"
    />
    <path
      d="M64.3423 79.544L57.6092 75.2125C57.2423 74.9764 56.7525 75.0819 56.5154 75.4482C56.2782 75.8144 56.3834 76.3027 56.7504 76.5388L63.4835 80.8703C63.8504 81.1064 64.3402 81.0008 64.5773 80.6346C64.8145 80.2684 64.7093 79.7801 64.3423 79.544Z"
      fill="#0517F8"
    />
    <path
      d="M62.3017 74.0619L57.6294 80.7448C57.4048 81.0662 57.4837 81.508 57.8057 81.7316C58.1278 81.9553 58.571 81.8761 58.7956 81.5548L63.4679 74.8718C63.6926 74.5505 63.6136 74.1087 63.2916 73.885C62.9696 73.6613 62.5264 73.7405 62.3017 74.0619Z"
      fill="#0517F8"
    />
    <path
      d="M76.3766 35.7323C76.3498 35.7342 76.3228 35.7342 76.296 35.7323L71.2982 35.0515C71.219 35.044 71.1421 35.0205 71.0722 34.9825C71.0023 34.9446 70.9408 34.8929 70.8914 34.8305C70.842 34.7682 70.8057 34.6965 70.7847 34.6198C70.7636 34.5431 70.7583 34.4629 70.7691 34.3841C70.7798 34.3052 70.8064 34.2294 70.8472 34.1611C70.888 34.0928 70.9422 34.0335 71.0065 33.9866C71.0708 33.9398 71.1439 33.9065 71.2214 33.8886C71.2989 33.8707 71.3793 33.8687 71.4576 33.8827L76.4553 34.5635C76.6119 34.5739 76.7579 34.6461 76.8612 34.7642C76.9645 34.8823 77.0167 35.0366 77.0063 35.1931C76.9958 35.3497 76.9236 35.4957 76.8055 35.599C76.6875 35.7023 76.5332 35.7545 76.3766 35.7441V35.7323Z"
      fill="#172B4D"
    />
    <path
      d="M79.1767 30.2444C79.0969 30.2443 79.0179 30.2281 78.9445 30.1966C78.8711 30.1652 78.8049 30.1192 78.7498 30.0614L75.2671 26.4134C75.2085 26.3586 75.1616 26.2923 75.1295 26.2188C75.0973 26.1452 75.0805 26.0659 75.0801 25.9856C75.0797 25.9053 75.0956 25.8258 75.127 25.7519C75.1583 25.6779 75.2044 25.6112 75.2624 25.5557C75.3205 25.5002 75.3892 25.4572 75.4644 25.4292C75.5397 25.4012 75.6198 25.3888 75.7 25.3928C75.7802 25.3969 75.8587 25.4172 75.9308 25.4526C76.0028 25.4881 76.0669 25.5378 76.1191 25.5988L79.6037 29.2468C79.6834 29.3304 79.7368 29.4356 79.7574 29.5493C79.7779 29.6629 79.7646 29.7801 79.7191 29.8863C79.6737 29.9925 79.598 30.0831 79.5016 30.1467C79.4052 30.2104 79.2923 30.2443 79.1767 30.2444Z"
      fill="#172B4D"
    />
    <path
      d="M76.172 31.9605C76.0783 31.96 75.986 31.9378 75.9024 31.8955L71.4182 29.5836C71.2837 29.5093 71.1835 29.3855 71.1388 29.2385C71.0941 29.0914 71.1085 28.9328 71.179 28.7962C71.2495 28.6596 71.3704 28.5559 71.5161 28.5071C71.6618 28.4583 71.8208 28.4683 71.9593 28.5348L76.4435 30.8448C76.5616 30.9053 76.6559 31.0038 76.7111 31.1245C76.7663 31.2451 76.7793 31.3809 76.7479 31.5098C76.7165 31.6387 76.6426 31.7533 76.5381 31.8351C76.4336 31.9168 76.3047 31.961 76.172 31.9605Z"
      fill="#172B4D"
    />
    <path
      d="M110.235 56.8213L126.525 72.2769C126.752 72.4901 126.933 72.7459 127.061 73.0296C127.188 73.3133 127.258 73.6194 127.266 73.9302C127.275 74.241 127.222 74.5504 127.111 74.8407C126.999 75.131 126.832 75.3965 126.618 75.6218L125.803 76.4817C125.59 76.708 125.334 76.8899 125.05 77.0171C124.767 77.1443 124.461 77.2142 124.15 77.2228C123.839 77.2314 123.53 77.1785 123.239 77.0672C122.949 76.9559 122.684 76.7883 122.458 76.5742L106.127 61.1559L110.235 56.8213Z"
      fill="#CDD1FE"
    />
    <path
      d="M124.086 77.8983C123.307 77.9008 122.557 77.6027 121.992 77.0659L105.167 61.1755L110.208 55.8472L127 71.7849C127.585 72.3417 127.927 73.108 127.949 73.916C127.971 74.724 127.672 75.5078 127.118 76.0959L126.303 76.9558C126.028 77.2469 125.698 77.4807 125.333 77.6435C124.967 77.8063 124.572 77.8949 124.172 77.9042L124.086 77.8983ZM107.115 61.1204L122.938 76.0743C123.261 76.376 123.69 76.5386 124.132 76.5268C124.574 76.515 124.993 76.3299 125.3 76.0113L126.112 75.1515C126.416 74.8295 126.58 74.4001 126.568 73.9575C126.556 73.5148 126.368 73.0951 126.047 72.7903L110.257 57.8089L107.115 61.1204Z"
      fill="#172B4D"
    />
    <path
      d="M110.235 56.8211L106.141 61.1498C105.927 61.376 105.671 61.5578 105.388 61.6848C105.104 61.8118 104.798 61.8815 104.487 61.8899C104.176 61.8983 103.867 61.8453 103.576 61.7338C103.286 61.6223 103.021 61.4546 102.796 61.2403V61.2403C102.569 61.027 102.388 60.7711 102.261 60.4873C102.134 60.2035 102.064 59.8974 102.056 59.5867C102.047 59.2759 102.1 58.9665 102.212 58.6762C102.323 58.386 102.491 58.1206 102.705 57.8954L106.8 53.5666C107.013 53.3405 107.269 53.1587 107.553 53.0317C107.837 52.9047 108.143 52.835 108.453 52.8265C108.764 52.8181 109.074 52.8712 109.364 52.9827C109.654 53.0941 109.919 53.2618 110.145 53.4761V53.4761C110.371 53.6895 110.553 53.9454 110.68 54.2291C110.807 54.5129 110.876 54.819 110.885 55.1298C110.893 55.4406 110.84 55.75 110.729 56.0402C110.617 56.3304 110.449 56.5958 110.235 56.8211Z"
      fill="white"
    />
    <path
      d="M104.425 62.5726C103.829 62.573 103.246 62.3986 102.748 62.071C102.25 61.7433 101.859 61.2768 101.623 60.7292C101.388 60.1816 101.318 59.5769 101.422 58.99C101.527 58.4031 101.801 57.8598 102.212 57.4272L106.306 53.0985C106.862 52.5106 107.629 52.1677 108.438 52.1452C109.247 52.1227 110.031 52.4224 110.619 52.9785C111.207 53.5345 111.55 54.3013 111.572 55.1101C111.595 55.919 111.295 56.7036 110.739 57.2915L106.645 61.6202C106.359 61.9223 106.015 62.1626 105.634 62.3264C105.252 62.4902 104.841 62.574 104.425 62.5726ZM108.522 53.5215H108.474C108.255 53.5263 108.039 53.5747 107.838 53.6639C107.637 53.7531 107.456 53.8813 107.306 54.041L103.211 58.3697C103.056 58.5284 102.934 58.7163 102.853 58.9225C102.771 59.1287 102.731 59.3491 102.735 59.5708C102.74 59.7926 102.788 60.0113 102.878 60.2142C102.967 60.4171 103.096 60.6001 103.257 60.7526C103.418 60.9052 103.608 61.0242 103.815 61.1027C104.023 61.1812 104.244 61.2176 104.465 61.2099C104.687 61.2022 104.905 61.1505 105.107 61.0578C105.308 60.9651 105.489 60.8332 105.639 60.6699L109.736 56.3411C109.961 56.1043 110.112 55.8064 110.17 55.4844C110.228 55.1625 110.19 54.8306 110.06 54.5302C109.931 54.2298 109.716 53.974 109.443 53.7946C109.169 53.6153 108.849 53.5203 108.522 53.5215V53.5215Z"
      fill="#172B4D"
    />
    <path
      d="M106.11 57.6551C112.699 51.0661 112.699 40.3831 106.11 33.7941C99.5209 27.205 88.8379 27.205 82.2489 33.7941C75.6598 40.3831 75.6598 51.0661 82.2489 57.6551C88.8379 64.2442 99.5209 64.2442 106.11 57.6551Z"
      fill="#CDD1FE"
    />
    <path
      d="M94.1757 63.277C90.1372 63.2731 86.2236 61.8763 83.0953 59.3222C79.967 56.7681 77.8155 53.2131 77.0037 49.257C76.1919 45.3009 76.7696 41.1859 78.6392 37.6062C80.5089 34.0265 83.5561 31.2014 87.2668 29.6073C90.9774 28.0133 95.1243 27.748 99.0078 28.8562C102.891 29.9644 106.274 32.3782 108.584 35.6905C110.895 39.0027 111.992 43.0105 111.691 47.0378C111.39 51.0651 109.709 54.8653 106.932 57.7972C105.292 59.5329 103.315 60.9146 101.121 61.857C98.9269 62.7994 96.5634 63.2827 94.1757 63.277ZM94.1757 29.5501C90.4163 29.5528 86.7754 30.8655 83.8792 33.2624C80.983 35.6592 79.0126 38.9904 78.3069 42.683C77.6012 46.3755 78.2043 50.1986 80.0125 53.4945C81.8207 56.7905 84.7209 59.3533 88.2144 60.7422C91.7078 62.131 95.576 62.259 99.1537 61.1043C102.731 59.9495 105.795 57.5841 107.817 54.4149C109.839 51.2458 110.694 47.471 110.234 43.7399C109.774 40.0087 108.028 36.5545 105.297 33.9713V33.9713C102.296 31.1276 98.3178 29.5443 94.1836 29.5481L94.1757 29.5501Z"
      fill="#172B4D"
    />
    <path
      d="M103.035 54.4739C107.926 49.583 107.926 41.6533 103.035 36.7624C98.144 31.8716 90.2144 31.8716 85.3235 36.7624C80.4326 41.6533 80.4326 49.583 85.3235 54.4739C90.2144 59.3648 98.144 59.3648 103.035 54.4739Z"
      fill="white"
    />
    <path
      d="M94.1692 58.8318C92.0122 58.8302 89.8884 58.3004 87.9833 57.2889C86.0781 56.2774 84.4496 54.8148 83.24 53.0289C82.0303 51.2429 81.2763 49.188 81.0438 47.0436C80.8113 44.8991 81.1073 42.7304 81.906 40.7267C82.7048 38.723 83.9819 36.9454 85.626 35.549C87.27 34.1526 89.2309 33.1799 91.3374 32.716C93.444 32.252 95.6321 32.3109 97.7106 32.8874C99.7892 33.4639 101.695 34.5405 103.262 36.0232V36.0232C105.159 37.8197 106.482 40.1385 107.062 42.6865C107.641 45.2345 107.453 47.8972 106.519 50.3379C105.586 52.7786 103.95 54.8878 101.818 56.3986C99.6855 57.9095 97.1532 58.7542 94.5411 58.8259L94.1692 58.8318ZM94.1869 33.7821C94.0767 33.7821 93.9646 33.7821 93.8544 33.7821C91.1173 33.8574 88.491 34.8795 86.4229 36.6741C84.3549 38.4687 82.9731 40.9249 82.513 43.6241C82.0529 46.3234 82.543 49.0986 83.8997 51.477C85.2564 53.8554 87.3958 55.6898 89.9534 56.6676C92.511 57.6455 95.3285 57.7062 97.9259 56.8396C100.523 55.9729 102.74 54.2325 104.198 51.9148C105.656 49.5971 106.265 46.8456 105.922 44.129C105.578 41.4125 104.304 38.899 102.315 37.0169V37.0169C100.123 34.9326 97.2117 33.774 94.1869 33.7821V33.7821Z"
      fill="#172B4D"
    />
  </svg>
);

export const CreatedMachineIcon = (props) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_10852_131032)">
      <path
        d="M6.10016 1.89983L1.83953 6.16047C1.37587 6.62413 1.37607 7.37607 1.83998 7.83998L6.09972 12.0997C6.56362 12.5636 7.31557 12.5638 7.77923 12.1002L12.0399 7.83953C12.5035 7.37587 12.5033 6.62393 12.0394 6.16002L7.77967 1.90028C7.31576 1.43637 6.56382 1.43617 6.10016 1.89983Z"
        stroke="#227E9E"
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.82149 5.17851L5.29983 6.70017C5.13424 6.86576 5.13431 7.13431 5.29999 7.29999L6.82133 8.82133C6.98701 8.98701 7.25556 8.98708 7.42115 8.82149L8.94281 7.29983C9.1084 7.13424 9.10833 6.86569 8.94265 6.70001L7.42131 5.17867C7.25563 5.01299 6.98708 5.01292 6.82149 5.17851Z"
        fill="#227E9E"
      />
    </g>
    <defs>
      <clipPath id="clip0_10852_131032">
        <rect width="14" height="14" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const NotificationOffAlertImage = () => (
  <svg
    width="84"
    height="114"
    viewBox="0 0 84 114"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_13284_2995)">
      <path
        d="M6.34483 48.5946H6.28299L0.646153 48.0601C0.470411 48.0437 0.308383 47.9581 0.195712 47.8223C0.0830422 47.6864 0.0289597 47.5113 0.0453623 47.3356C0.0617649 47.1599 0.147309 46.9978 0.283176 46.8852C0.419043 46.7725 0.594103 46.7184 0.769846 46.7348L6.40668 47.2693C6.58242 47.2775 6.74771 47.3552 6.86618 47.4853C6.98465 47.6154 7.04659 47.7872 7.03839 47.9629C7.03019 48.1386 6.95251 48.3039 6.82245 48.4224C6.69238 48.5409 6.52058 48.6028 6.34483 48.5946V48.5946Z"
        fill="#172B4D"
      />
      <path
        d="M9.23406 42.3108C9.06192 42.3113 8.89636 42.2448 8.77242 42.1253L4.69721 38.1959C4.57067 38.0737 4.49784 37.9063 4.49473 37.7305C4.49163 37.5546 4.5585 37.3848 4.68064 37.2582C4.80278 37.1317 4.97018 37.0589 5.14602 37.0558C5.32186 37.0527 5.49174 37.1195 5.61827 37.2417L9.69349 41.1711C9.78857 41.2628 9.8541 41.3807 9.88167 41.5099C9.90924 41.639 9.89759 41.7735 9.84822 41.8959C9.79885 42.0184 9.714 42.1234 9.60457 42.1973C9.49514 42.2712 9.36612 42.3108 9.23406 42.3108V42.3108Z"
        fill="#172B4D"
      />
      <path
        d="M5.94294 44.3737C5.84678 44.3735 5.75182 44.3524 5.66463 44.3119L0.529196 41.9286C0.369563 41.8545 0.245907 41.72 0.185429 41.5547C0.124952 41.3894 0.132608 41.2069 0.206712 41.0473C0.280817 40.8876 0.4153 40.764 0.580578 40.7035C0.745855 40.643 0.928387 40.6507 1.08802 40.7248L6.23009 43.1103C6.36695 43.174 6.47797 43.2825 6.54478 43.4178C6.61158 43.5532 6.63016 43.7073 6.59744 43.8547C6.56472 44.0021 6.48266 44.1338 6.36485 44.2282C6.24703 44.3226 6.10053 44.3739 5.94957 44.3737H5.94294Z"
        fill="#172B4D"
      />
      <path
        d="M6.4508 62.6052C5.84802 62.6044 5.25001 62.4982 4.68376 62.2916C4.06369 62.0732 3.49541 61.7291 3.01454 61.2808C2.53368 60.8325 2.15059 60.2898 1.88931 59.6865C1.62802 59.0833 1.49416 58.4326 1.49611 57.7752C1.49807 57.1178 1.63581 56.4679 1.90069 55.8662C2.44001 54.6256 3.43681 53.6407 4.68376 53.1162L17.2739 47.6826L17.8857 49.1007L5.2956 54.5365C4.41567 54.907 3.71238 55.6023 3.33198 56.478C3.15024 56.8857 3.05503 57.3265 3.05237 57.7728C3.04971 58.2191 3.13965 58.6611 3.31651 59.0709C3.49337 59.4807 3.7533 59.8493 4.07988 60.1535C4.40647 60.4577 4.7926 60.6908 5.21387 60.8382C6.11216 61.1628 7.10101 61.1288 7.97486 60.7432L19.4672 55.78L20.079 57.1981L8.57565 62.1635C7.90473 62.4535 7.18174 62.6038 6.4508 62.6052Z"
        fill="#172B4D"
      />
      <path
        d="M31.8739 63.7584C31.2712 63.7576 30.6732 63.6514 30.1069 63.4448C29.4874 63.2258 28.9198 62.8812 28.4398 62.4325C27.9598 61.9839 27.5776 61.4409 27.3174 60.8376C27.0571 60.2343 26.9243 59.5837 26.9273 58.9266C26.9303 58.2696 27.0691 57.6203 27.3349 57.0194C27.8653 55.7843 28.8497 54.7999 30.0848 54.2694L42.6749 48.8336C42.8633 48.7525 43.0761 48.7495 43.2667 48.8253C43.4572 48.9011 43.6098 49.0495 43.691 49.2378C43.7721 49.4261 43.7751 49.639 43.6993 49.8295C43.6235 50.0201 43.4751 50.1727 43.2868 50.2539L30.6967 55.6875C29.8171 56.0585 29.114 56.7537 28.733 57.629C28.5506 58.0367 28.4548 58.4778 28.4518 58.9245C28.4488 59.3711 28.5386 59.8135 28.7155 60.2237C28.8924 60.6338 29.1526 61.0027 29.4796 61.307C29.8066 61.6113 30.1932 61.8443 30.6149 61.9914C31.5126 62.315 32.5004 62.281 33.3737 61.8964L44.8837 56.931C45.0721 56.8499 45.2849 56.8469 45.4755 56.9227C45.666 56.9985 45.8186 57.1469 45.8998 57.3352C45.9809 57.5236 45.9839 57.7364 45.9081 57.927C45.8323 58.1175 45.6839 58.2701 45.4956 58.3513L33.9944 63.3144C33.3252 63.6054 32.6036 63.7565 31.8739 63.7584V63.7584Z"
        fill="#172B4D"
      />
      <path
        d="M57.4407 21.9193H55.8946C55.8946 16.5884 53.7769 11.4759 50.0074 7.70641C46.2379 3.93693 41.1254 1.81925 35.7946 1.81925C30.4637 1.81925 25.3512 3.93693 21.5817 7.70641C17.8123 11.4759 15.6946 16.5884 15.6946 21.9193H14.1484C14.1484 16.1769 16.4296 10.6697 20.4901 6.60921C24.5505 2.54873 30.0577 0.267578 35.8001 0.267578C41.5425 0.267578 47.0497 2.54873 51.1101 6.60921C55.1706 10.6697 57.4518 16.1769 57.4518 21.9193H57.4407Z"
        fill="#172B4D"
      />
      <path
        d="M48.418 91.3704C46.9977 91.3704 45.1667 90.6393 43.6448 89.8795C39.7728 87.9446 33.8576 83.7766 27.6797 74.9083C25.4872 71.7872 23.5581 68.4892 21.9125 65.0482C15.9885 51.9589 13.3711 37.4361 14.1398 21.8862L15.6859 21.9635C14.9261 37.2638 17.4949 51.5437 23.3283 64.4099C23.4079 64.5844 31.4434 82.0537 44.3428 88.4968C47.0684 89.8574 48.714 90.0164 49.0099 89.6939C49.434 89.2323 48.2347 86.8843 47.5897 85.6209C46.3506 83.1912 45.1821 80.9118 46.3528 79.571C47.7598 77.9608 51.1657 79.264 55.4707 81.1326C58.517 82.4768 61.4464 84.0719 64.2285 85.9014C65.4654 86.6855 67.0072 87.6685 67.3495 87.5293C67.3495 87.5293 67.5859 87.2952 67.3495 86.003C67.0545 84.7129 66.6858 83.4408 66.2451 82.1929C65.0723 78.6036 63.9657 75.2153 65.7194 73.8812C66.371 73.3864 67.0823 73.192 73.205 75.3854C75.193 76.0988 76.6198 76.6598 77.6624 77.0707C78.4001 77.36 79.065 77.6229 79.467 77.7444C79.1224 76.6112 77.0704 73.6515 75.0759 70.78C73.4568 68.4431 71.4402 65.5386 69.3551 62.2806C68.8272 61.4545 68.3015 60.6483 67.7869 59.8509C64.4118 54.6448 61.2246 49.728 59.3537 43.8063C57.133 36.7269 55.9697 29.3584 55.9014 21.9392L57.4475 21.9216C57.5142 29.1907 58.6529 36.4102 60.827 43.3468C62.636 49.0698 65.6245 53.6796 69.0834 59.016C69.6032 59.8141 70.1275 60.624 70.6561 61.4457C72.7279 64.6772 74.7335 67.5707 76.3482 69.8965C80.2445 75.5113 81.8569 77.8349 80.6906 79.0078C80.028 79.6704 79.2947 79.3722 77.0969 78.5064C76.0632 78.1 74.6496 77.5434 72.6793 76.8388C67.9813 75.1557 66.8923 75.0717 66.6427 75.1181C65.7835 75.8161 66.9431 79.3612 67.7118 81.7114C68.8162 85.093 69.6997 87.7634 68.2441 88.7795C67.1397 89.5658 65.7305 88.6735 63.4024 87.1958C60.6884 85.4092 57.8299 83.8525 54.8566 82.5419C53.4319 81.9212 51.6583 81.1525 50.1607 80.7218C48.058 80.1144 47.5919 80.5009 47.5102 80.5782C47.0287 81.1282 48.2369 83.4938 48.9591 84.9075C50.1607 87.2554 51.2938 89.4752 50.1431 90.7276C49.7433 91.1893 49.1314 91.3704 48.418 91.3704Z"
        fill="#172B4D"
      />
      <path
        d="M22.1463 35.534C21.4058 35.5334 20.6958 35.2392 20.172 34.7158C19.6482 34.1924 19.3533 33.4826 19.3522 32.7421V26.277C19.3417 25.9036 19.4062 25.5319 19.5418 25.1838C19.6774 24.8358 19.8815 24.5185 20.1419 24.2506C20.4023 23.9828 20.7137 23.7699 21.0578 23.6246C21.4019 23.4792 21.7717 23.4043 22.1452 23.4043C22.5187 23.4043 22.8885 23.4792 23.2326 23.6246C23.5767 23.7699 23.8881 23.9828 24.1485 24.2506C24.4089 24.5185 24.613 24.8358 24.7486 25.1838C24.8842 25.5319 24.9487 25.9036 24.9382 26.277V32.7421C24.937 33.4822 24.6425 34.1917 24.1192 34.715C23.5959 35.2383 22.8864 35.5328 22.1463 35.534V35.534ZM22.1463 25.029C21.8155 25.0296 21.4984 25.1613 21.2645 25.3952C21.0306 25.6291 20.8989 25.9462 20.8983 26.277V32.7421C20.8913 32.9103 20.9183 33.0781 20.9777 33.2355C21.0372 33.393 21.1279 33.5368 21.2444 33.6583C21.3609 33.7798 21.5007 33.8764 21.6555 33.9425C21.8103 34.0085 21.9769 34.0426 22.1452 34.0426C22.3135 34.0426 22.4801 34.0085 22.6349 33.9425C22.7897 33.8764 22.9295 33.7798 23.046 33.6583C23.1625 33.5368 23.2532 33.393 23.3127 33.2355C23.3721 33.0781 23.3991 32.9103 23.3921 32.7421V26.277C23.3921 25.9464 23.2609 25.6293 23.0273 25.3953C22.7938 25.1613 22.4769 25.0296 22.1463 25.029Z"
        fill="#172B4D"
      />
      <path
        d="M31.1077 35.534C30.368 35.5323 29.6591 35.2375 29.1362 34.7142C28.6134 34.1909 28.3192 33.4818 28.318 32.7421V26.277C28.3075 25.9036 28.372 25.5319 28.5076 25.1838C28.6433 24.8358 28.8473 24.5185 29.1077 24.2506C29.3681 23.9828 29.6795 23.7699 30.0236 23.6246C30.3677 23.4792 30.7375 23.4043 31.111 23.4043C31.4846 23.4043 31.8543 23.4792 32.1984 23.6246C32.5425 23.7699 32.8539 23.9828 33.1143 24.2506C33.3747 24.5185 33.5788 24.8358 33.7144 25.1838C33.8501 25.5319 33.9145 25.9036 33.904 26.277V32.7421C33.9029 33.483 33.6077 34.1931 33.0834 34.7165C32.5592 35.24 31.8486 35.534 31.1077 35.534ZM31.1077 25.029C30.7769 25.0296 30.4598 25.1613 30.2259 25.3952C29.992 25.6291 29.8603 25.9462 29.8597 26.277V32.7421C29.8527 32.9103 29.8797 33.0781 29.9391 33.2355C29.9986 33.393 30.0893 33.5368 30.2058 33.6583C30.3223 33.7798 30.4621 33.8764 30.6169 33.9425C30.7717 34.0085 30.9383 34.0426 31.1066 34.0426C31.2749 34.0426 31.4415 34.0085 31.5963 33.9425C31.7511 33.8764 31.8909 33.7798 32.0074 33.6583C32.1239 33.5368 32.2146 33.393 32.2741 33.2355C32.3335 33.0781 32.3605 32.9103 32.3535 32.7421V26.277C32.3535 25.9464 32.2223 25.6293 31.9887 25.3953C31.7552 25.1613 31.4383 25.0296 31.1077 25.029V25.029Z"
        fill="#172B4D"
      />
      <path
        d="M49.5178 112.955C67.971 112.955 82.9302 109.792 82.9302 105.891C82.9302 101.99 67.971 98.8271 49.5178 98.8271C31.0647 98.8271 16.1055 101.99 16.1055 105.891C16.1055 109.792 31.0647 112.955 49.5178 112.955Z"
        fill="#CDD1FE"
      />
      <path
        d="M49.5221 113.727C40.5456 113.727 32.0992 112.985 25.7379 111.64C18.8376 110.182 15.3389 108.247 15.3389 105.897C15.3389 103.547 18.8376 101.603 25.7379 100.154C32.0992 98.8093 40.5368 98.0693 49.5221 98.0693C58.5075 98.0693 66.9473 98.8093 73.3086 100.154C80.2088 101.612 83.7076 103.547 83.7076 105.897C83.7076 108.247 80.2088 110.189 73.3086 111.64C66.9428 112.985 58.4942 113.727 49.5221 113.727ZM49.5221 99.5912C29.3006 99.5912 16.885 103.253 16.885 105.882C16.885 108.51 29.2962 112.181 49.5221 112.181C69.748 112.181 82.1614 108.517 82.1614 105.891C82.1614 103.264 69.7414 99.6 49.5221 99.6V99.5912Z"
        fill="#172B4D"
      />
      <path
        d="M73.849 46.8947C79.3859 41.3577 79.3859 32.3806 73.849 26.8436C68.312 21.3067 59.3349 21.3067 53.798 26.8436C48.261 32.3806 48.261 41.3577 53.798 46.8947C59.3349 52.4316 68.312 52.4316 73.849 46.8947Z"
        fill="#FDE5F0"
      />
      <path
        d="M63.8226 51.8258C60.8646 51.8254 57.9732 50.9479 55.514 49.3042C53.0548 47.6605 51.1381 45.3245 50.0065 42.5916C48.8748 39.8587 48.579 36.8516 49.1563 33.9506C49.7336 31.0495 51.1583 28.3848 53.25 26.2933C55.3417 24.2019 58.0067 22.7777 60.9078 22.2008C63.809 21.6239 66.816 21.9202 69.5488 23.0523C72.2815 24.1843 74.6172 26.1013 76.2605 28.5608C77.9038 31.0202 78.7809 33.9118 78.7809 36.8697C78.7768 40.8354 77.1994 44.6375 74.395 47.4415C71.5906 50.2455 67.7883 51.8223 63.8226 51.8258V51.8258ZM63.8226 23.4694C61.1723 23.4699 58.5817 24.2562 56.3784 25.7289C54.175 27.2016 52.4578 29.2946 51.4439 31.7432C50.43 34.1918 50.165 36.8861 50.6823 39.4853C51.1996 42.0846 52.476 44.4721 54.3501 46.3459C56.2243 48.2198 58.612 49.4958 61.2113 50.0127C63.8107 50.5295 66.5049 50.264 68.9534 49.2497C71.4018 48.2354 73.4945 46.5179 74.9669 44.3143C76.4392 42.1107 77.2251 39.5199 77.2251 36.8697C77.221 33.3166 75.8075 29.9103 73.2949 27.398C70.7823 24.8858 67.3757 23.473 63.8226 23.4694Z"
        fill="#172B4D"
      />
      <path
        d="M70.146 35.5749L58.844 35.7114C58.1071 35.7203 57.517 36.3249 57.5259 37.0618C57.5348 37.7986 58.1393 38.3888 58.8762 38.3799L70.1783 38.2433C70.9151 38.2344 71.5053 37.6299 71.4964 36.893C71.4875 36.1561 70.8829 35.566 70.146 35.5749Z"
        fill="#EE0064"
      />
    </g>
    <defs>
      <clipPath id="clip0_13284_2995">
        <rect
          width="83.658"
          height="113.455"
          fill="white"
          transform="translate(0.0454102 0.272949)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SidebarAnalyticsIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1930_40456)">
      <path
        d="M17.5 16.25H2.5V3.75"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.25 5L10 11.25L7.5 8.75L2.5 13.75"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.25 8.125V5H13.125"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_1930_40456">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SidebarAnalyticsFilledIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_1930_40458)">
      <path
        d="M18.125 16.25C18.125 16.4158 18.0592 16.5747 17.9419 16.6919C17.8247 16.8092 17.6658 16.875 17.5 16.875H2.5C2.33424 16.875 2.17527 16.8092 2.05806 16.6919C1.94085 16.5747 1.875 16.4158 1.875 16.25V3.75C1.875 3.58424 1.94085 3.42527 2.05806 3.30806C2.17527 3.19085 2.33424 3.125 2.5 3.125C2.66576 3.125 2.82473 3.19085 2.94194 3.30806C3.05915 3.42527 3.125 3.58424 3.125 3.75V12.2422L7.05469 8.30469C7.17318 8.18732 7.33322 8.12148 7.5 8.12148C7.66678 8.12148 7.82682 8.18732 7.94531 8.30469L10 10.3672L13.8047 6.5625L12.6797 5.44531C12.5949 5.35495 12.5374 5.24242 12.5139 5.12076C12.4904 4.99909 12.5018 4.87326 12.5469 4.75781C12.5955 4.64467 12.6761 4.54818 12.7788 4.48018C12.8815 4.41218 13.0018 4.37563 13.125 4.375H16.25C16.4158 4.375 16.5747 4.44085 16.6919 4.55806C16.8092 4.67527 16.875 4.83424 16.875 5V8.125C16.8754 8.24839 16.8392 8.36913 16.7711 8.47202C16.703 8.5749 16.6059 8.65531 16.4922 8.70313C16.4151 8.73374 16.3329 8.74964 16.25 8.75C16.084 8.7493 15.9247 8.68499 15.8047 8.57031L14.6875 7.44531L10.4453 11.6953C10.3268 11.8127 10.1668 11.8785 10 11.8785C9.83322 11.8785 9.67318 11.8127 9.55469 11.6953L7.5 9.63281L3.125 14.0078V15.625H17.5C17.6658 15.625 17.8247 15.6908 17.9419 15.8081C18.0592 15.9253 18.125 16.0842 18.125 16.25Z"
        fill="#0517F8"
      />
    </g>
    <defs>
      <clipPath id="clip0_1930_40458">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const InventoryIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_14437_6537)">
      <path
        d="M17.1875 13.6953V6.30466C17.1869 6.19339 17.157 6.08425 17.1007 5.98824C17.0445 5.89222 16.9639 5.81274 16.8672 5.75779L10.3047 2.04685C10.2121 1.99337 10.107 1.96521 10 1.96521C9.89303 1.96521 9.78795 1.99337 9.69531 2.04685L3.13281 5.75779C3.03606 5.81274 2.9555 5.89222 2.89926 5.98824C2.84302 6.08425 2.8131 6.19339 2.8125 6.30466V13.6953C2.8131 13.8066 2.84302 13.9157 2.89926 14.0117C2.9555 14.1077 3.03606 14.1872 3.13281 14.2422L9.69531 17.9531C9.78795 18.0066 9.89303 18.0347 10 18.0347C10.107 18.0347 10.2121 18.0066 10.3047 17.9531L16.8672 14.2422C16.9639 14.1872 17.0445 14.1077 17.1007 14.0117C17.157 13.9157 17.1869 13.8066 17.1875 13.6953V13.6953Z"
        stroke={strokeColor || "#748094"}
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 12.8125C11.5533 12.8125 12.8125 11.5533 12.8125 10C12.8125 8.4467 11.5533 7.1875 10 7.1875C8.4467 7.1875 7.1875 8.4467 7.1875 10C7.1875 11.5533 8.4467 12.8125 10 12.8125Z"
        stroke={strokeColor || "#748094"}
        strokeWidth="1.4"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_14437_6537">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const InventoryFilledIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_14437_1249)">
      <g clipPath="url(#clip1_14437_1249)">
        <path
          d="M17.1797 5.21094L10.6172 1.50782C10.4293 1.40024 10.2165 1.34364 10 1.34364C9.78348 1.34364 9.57072 1.40024 9.38281 1.50782L2.82031 5.21094C2.62825 5.32183 2.4687 5.48123 2.35764 5.67319C2.24658 5.86515 2.18791 6.08292 2.1875 6.30469V13.6953C2.18791 13.9171 2.24658 14.1349 2.35764 14.3268C2.4687 14.5188 2.62825 14.6782 2.82031 14.7891L9.38281 18.4922C9.57072 18.5998 9.78348 18.6564 10 18.6564C10.2165 18.6564 10.4293 18.5998 10.6172 18.4922L17.1797 14.7891C17.3717 14.6782 17.5313 14.5188 17.6424 14.3268C17.7534 14.1349 17.8121 13.9171 17.8125 13.6953V6.30469C17.8121 6.08292 17.7534 5.86515 17.6424 5.67319C17.5313 5.48123 17.3717 5.32183 17.1797 5.21094V5.21094ZM10 12.8125C9.44374 12.8125 8.89997 12.6476 8.43746 12.3385C7.97495 12.0295 7.61446 11.5902 7.40159 11.0763C7.18872 10.5624 7.13302 9.99689 7.24154 9.45131C7.35006 8.90574 7.61793 8.4046 8.01126 8.01127C8.4046 7.61793 8.90574 7.35007 9.45131 7.24155C9.99688 7.13303 10.5624 7.18872 11.0763 7.40159C11.5902 7.61447 12.0295 7.97495 12.3385 8.43746C12.6475 8.89998 12.8125 9.44375 12.8125 10C12.8125 10.7459 12.5162 11.4613 11.9887 11.9887C11.4613 12.5162 10.7459 12.8125 10 12.8125V12.8125Z"
          fill="#0517F8"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_14437_1249">
        <rect width="20" height="20" fill="white" />
      </clipPath>
      <clipPath id="clip1_14437_1249">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const DollarIcon = (props) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8 0.666748V15.3334"
      stroke={props?.strokeColor || "#A2AAB8"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.3333 3.33325H6.33333C5.71449 3.33325 5.121 3.57908 4.68342 4.01667C4.24583 4.45425 4 5.04775 4 5.66659C4 6.28542 4.24583 6.87892 4.68342 7.3165C5.121 7.75409 5.71449 7.99992 6.33333 7.99992H9.66667C10.2855 7.99992 10.879 8.24575 11.3166 8.68334C11.7542 9.12092 12 9.71441 12 10.3333C12 10.9521 11.7542 11.5456 11.3166 11.9832C10.879 12.4208 10.2855 12.6666 9.66667 12.6666H4"
      stroke={props?.strokeColor || "#A2AAB8"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const QrCodeIcon = (props) => (
  <svg
    width="18"
    height="18"
    viewBox="0 0 18 18"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <mask id="path-1-inside-1_15742_203313" fill="white">
      <rect x="3.66699" y="3.66602" width="4.26667" height="4.26667" rx="0.8" />
    </mask>
    <rect
      x="3.66699"
      y="3.66602"
      width="4.26667"
      height="4.26667"
      rx="0.8"
      stroke={props.color || "#0517F8"}
      strokeWidth="1"
      mask="url(#path-1-inside-1_15742_203313)"
    />
    <mask id="path-2-inside-2_15742_203313" fill="white">
      <rect x="3.66699" y="10.0664" width="4.26667" height="4.26667" rx="0.8" />
    </mask>
    <rect
      x="3.66699"
      y="10.0664"
      width="4.26667"
      height="4.26667"
      rx="0.8"
      stroke={props.color || "#0517F8"}
      strokeWidth="1"
      mask="url(#path-2-inside-2_15742_203313)"
    />
    <mask id="path-3-inside-3_15742_203313" fill="white">
      <rect x="10.0664" y="3.66602" width="4.26667" height="4.26667" rx="0.8" />
    </mask>
    <rect
      x="10.0664"
      y="3.66602"
      width="4.26667"
      height="4.26667"
      rx="0.8"
      stroke={props.color || "#0517F8"}
      strokeWidth="1"
      mask="url(#path-3-inside-3_15742_203313)"
    />
    <circle
      cx="10.5997"
      cy="10.5997"
      r="0.5"
      fill={props.color || "#0517F8"}
      stroke={props.color || "#0517F8"}
      strokeWidth="0.0666666"
    />
    <circle
      cx="13.7999"
      cy="10.5997"
      r="0.5"
      fill={props.color || "#0517F8"}
      stroke={props.color || "#0517F8"}
      strokeWidth="0.0666666"
    />
    <circle
      cx="13.7999"
      cy="13.7999"
      r="0.5"
      fill={props.color || "#0517F8"}
      stroke={props.color || "#0517F8"}
      strokeWidth="0.0666666"
    />
    <circle
      cx="10.5997"
      cy="13.7999"
      r="0.5"
      fill={props.color || "#0517F8"}
      stroke={props.color || "#0517F8"}
      strokeWidth="0.0666666"
    />
    <circle
      cx="12.2003"
      cy="12.1993"
      r="0.5"
      fill={props.color || "#0517F8"}
      stroke={props.color || "#0517F8"}
      strokeWidth="0.0666666"
    />
    <path
      d="M5 1H1.8C1.35817 1 1 1.35817 1 1.8V5"
      stroke={props.color || "#0517F8"}
      strokeLinecap="round"
    />
    <path
      d="M13 1H16.2C16.6418 1 17 1.35817 17 1.8V5"
      stroke={props.color || "#0517F8"}
      strokeLinecap="round"
    />
    <path
      d="M13 17H16.2C16.6418 17 17 16.6418 17 16.2V13"
      stroke={props.color || "#0517F8"}
      strokeLinecap="round"
    />
    <path
      d="M5 17H1.8C1.35817 17 1 16.6418 1 16.2V13"
      stroke={props.color || "#0517F8"}
      strokeLinecap="round"
    />
  </svg>
);

export const ProgressLoadingIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M7.51452 11.5127V13.6252"
        stroke="#748094"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.02213 10.4873L3.53174 11.9748"
        stroke="#748094"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.99526 8H1.87866"
        stroke="#748094"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.53174 4.02539L5.02213 5.51289"
        stroke="#748094"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.51452 4.25V2.375"
        stroke="#748094"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10.5015 5.01914L11.4971 4.02539"
        stroke="#748094"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.1505 8H12.2112"
        stroke="#748094"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.4974 11.9752L11.1655 11.6439"
        stroke="#748094"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const HorizontalBarsIcon = () => {
  return (
    <svg
      width="16"
      height="16"
      viewBox="0 0 16 16"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <path
        d="M3.2002 3.5002H12.8002"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.2002 6.5002H12.8002"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.2002 9.5002H12.8002"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.2002 12.5002H12.8002"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const DottedCircleArrowUp = (props) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M7.99998 5.33203V10.6676"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.99927 7.33286L8.0001 5.33203L10.0009 7.33286"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.95215 1.99805C11.2672 1.99805 13.9546 4.68546 13.9546 8.00055C13.9546 11.3156 11.2672 14.003 7.95215 14.003"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.9013 13.6341C5.24335 13.3959 4.63211 13.0445 4.09521 12.5957"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.09521 3.40367C4.63179 2.95443 5.24311 2.60294 5.9013 2.36523"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.04565 6.95753C2.16617 6.2677 2.40705 5.60444 2.75728 4.99805"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.04565 9.04102C2.16609 9.73107 2.40697 10.3946 2.75728 11.0012"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ViewFullScreenIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13.7031 3.3335H16.6661V6.29646"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.2217 7.77794L16.6661 3.3335"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.29597 16.6671H3.33301V13.7041"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.77745 12.2222L3.33301 16.6666"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.07422 9.2594V5.5557C4.07422 4.73718 4.73718 4.07422 5.5557 4.07422H9.2594"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.9259 10.7407V14.4444C15.9259 15.2629 15.263 15.9259 14.4444 15.9259H10"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const CheckboxEmptyIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_16604_113117)">
      <rect
        x="3.1"
        y="4.09756"
        width="13.8"
        height="13.8"
        rx="2.4"
        stroke="#A2AAB8"
        strokeWidth="1.2"
      />
    </g>
    <defs>
      <clipPath id="clip0_16604_113117">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0 0.997559)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const CheckboxCheckedIcon = ({ strokeColor, fillColor, ...props }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_16604_113120)">
      <rect
        x="3.1"
        y="4.09756"
        width="13.8"
        height="13.8"
        rx="2.4"
        fill={fillColor || "#172B4D"}
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
      />
      <g clipPath="url(#clip1_16604_113120)">
        <path
          d="M13.4375 8.81006L9.0625 13.1851L6.875 10.9976"
          stroke="white"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_16604_113120">
        <rect
          width="20"
          height="20"
          fill="white"
          transform="translate(0 0.997559)"
        />
      </clipPath>
      <clipPath id="clip1_16604_113120">
        <rect
          width="10"
          height="10"
          fill="white"
          transform="translate(5 5.99756)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SidebarOpenTicketsFilledIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_20377_77920)">
      <g clipPath="url(#clip1_20377_77920)">
        <path
          d="M18.8985 8.64067C18.7813 8.48133 18.6283 8.35169 18.452 8.26215C18.2756 8.1726 18.0807 8.12565 17.8829 8.12504H16.8751V6.87504C16.8751 6.54352 16.7434 6.22558 16.5089 5.99116C16.2745 5.75674 15.9566 5.62504 15.6251 5.62504H10.211L8.03911 4.00004C7.82384 3.83569 7.55995 3.74773 7.28911 3.75004H3.12505C2.79353 3.75004 2.47559 3.88174 2.24117 4.11616C2.00675 4.35058 1.87505 4.66852 1.87505 5.00004V16.25C1.874 16.3324 1.88945 16.4142 1.92049 16.4905C1.95153 16.5668 1.99752 16.6361 2.05577 16.6943C2.11402 16.7526 2.18333 16.7986 2.25964 16.8296C2.33594 16.8606 2.41768 16.8761 2.50005 16.875H16.2501C16.3817 16.8751 16.5099 16.8335 16.6165 16.7564C16.7231 16.6792 16.8027 16.5704 16.8438 16.4454L19.0704 9.77348C19.1322 9.58436 19.1489 9.38338 19.119 9.18665C19.0892 8.98993 19.0137 8.80293 18.8985 8.64067ZM7.28911 5.00004L9.46099 6.62504C9.67627 6.7894 9.94016 6.87736 10.211 6.87504H15.6251V8.12504H5.45318C5.19093 8.12448 4.93515 8.20641 4.72203 8.35923C4.50892 8.51206 4.34927 8.72804 4.26568 8.97661L3.12505 12.3985V5.00004H7.28911Z"
          fill="#0517F8"
        />
      </g>
      <path
        d="M2.5 16.25V5C2.5 4.83424 2.56585 4.67527 2.68306 4.55806C2.80027 4.44085 2.95924 4.375 3.125 4.375H7.28906C7.4242 4.37556 7.55562 4.41936 7.66406 4.5L9.83594 6.125C9.94438 6.20564 10.0758 6.24944 10.2109 6.25H15.625C15.7908 6.25 15.9497 6.31585 16.0669 6.43306C16.1842 6.55027 16.25 6.70924 16.25 6.875V8.75"
        stroke="#0517F8"
        strokeWidth="1.5"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_20377_77920">
        <rect width="20" height="20" fill="white" />
      </clipPath>
      <clipPath id="clip1_20377_77920">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SidebarMyTicketFilledIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_20377_43649)">
      <path d="M2 4H7L8 6H2V4Z" fill="#0517F8" />
      <path
        d="M16 5.5H9.38281L7.25 3.36719C7.1344 3.2507 6.99686 3.15828 6.84534 3.09525C6.69381 3.03223 6.5313 2.99985 6.36719 3H2.25C1.91848 3 1.60054 3.1317 1.36612 3.36612C1.1317 3.60054 1 3.91848 1 4.25V15.5469C1 15.866 1.12676 16.172 1.35239 16.3976C1.57802 16.6232 1.88404 16.75 2.20313 16.75H7.92188C8.08764 16.75 8.24661 16.6841 8.36382 16.5669C8.48103 16.4497 8.54688 16.2908 8.54688 16.125C8.54688 15.9592 8.48103 15.8003 8.36382 15.6831C8.24661 15.5658 8.08764 15.5 7.92188 15.5H2.25V6.75H16V8C16 8.16576 16.0658 8.32473 16.1831 8.44194C16.3003 8.55915 16.4592 8.625 16.625 8.625C16.7908 8.625 16.9497 8.55915 17.0669 8.44194C17.1842 8.32473 17.25 8.16576 17.25 8V6.75C17.25 6.41848 17.1183 6.10054 16.8839 5.86612C16.6495 5.6317 16.3315 5.5 16 5.5ZM6.36719 4.25L7.61719 5.5H2.25V4.25H6.36719Z"
        fill="#0517F8"
      />
      <path
        d="M3 15.95V7H15.44C15.9923 7 16.44 7.44771 16.44 8V11.475L9.72 15.95H3Z"
        fill="#0517F8"
        stroke="#0517F8"
        strokeWidth="1.6"
      />
      <path
        d="M15.759 14.1818L17.5168 12.7287C17.6107 12.6478 17.6788 12.5412 17.7127 12.4219C17.7465 12.3027 17.7446 12.1761 17.7071 12.058C17.6697 11.9398 17.5984 11.8353 17.502 11.7573C17.4057 11.6793 17.2886 11.6313 17.1652 11.6193L14.8371 11.4396L13.9387 9.36933C13.8893 9.25932 13.8093 9.16592 13.7081 9.10039C13.6069 9.03487 13.4889 9 13.3683 9C13.2478 9 13.1298 9.03487 13.0286 9.10039C12.9274 9.16592 12.8473 9.25932 12.798 9.36933L11.8996 11.4396L9.57147 11.6193C9.44811 11.6313 9.331 11.6793 9.23466 11.7573C9.13832 11.8353 9.06701 11.9398 9.02957 12.058C8.99212 12.1761 8.9902 12.3027 9.02404 12.4219C9.05788 12.5412 9.12598 12.6478 9.21991 12.7287L10.9777 14.1818L10.4465 16.3459C10.4166 16.465 10.4229 16.5904 10.4647 16.7059C10.5064 16.8214 10.5817 16.9218 10.6808 16.9943C10.779 17.0674 10.8965 17.1101 11.0187 17.1171C11.1409 17.124 11.2625 17.0949 11.3683 17.0334L13.3683 15.8537L15.3683 17.0334C15.4664 17.088 15.5764 17.1176 15.6887 17.1193C15.8218 17.1212 15.9515 17.077 16.0558 16.9943C16.155 16.9218 16.2303 16.8214 16.272 16.7059C16.3138 16.5904 16.3201 16.465 16.2902 16.3459L15.759 14.1818Z"
        fill="#0517F8"
        stroke="white"
        strokeWidth="1.2"
      />
    </g>
    <defs>
      <clipPath id="clip0_20377_43649">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SidebarFacilityFilledIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_20377_60879)">
      <mask
        id="path-1-outside-1_20377_60879"
        maskUnits="userSpaceOnUse"
        x="10.2627"
        y="6.42871"
        width="8"
        height="11"
        fill="black"
      >
        <rect fill="white" x="10.2627" y="6.42871" width="8" height="11" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M12.1574 7.42871C11.6633 7.42871 11.2627 7.8293 11.2627 8.32345V16.0001H17.2777V8.32345C17.2777 7.8293 16.8771 7.42871 16.383 7.42871H12.1574ZM13.5672 9.26547C13.2911 9.26547 13.0672 9.48933 13.0672 9.76547C13.0672 10.0416 13.2911 10.2655 13.5672 10.2655H14.9732C15.2494 10.2655 15.4732 10.0416 15.4732 9.76547C15.4732 9.48933 15.2494 9.26547 14.9732 9.26547H13.5672Z"
        />
      </mask>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M12.1574 7.42871C11.6633 7.42871 11.2627 7.8293 11.2627 8.32345V16.0001H17.2777V8.32345C17.2777 7.8293 16.8771 7.42871 16.383 7.42871H12.1574ZM13.5672 9.26547C13.2911 9.26547 13.0672 9.48933 13.0672 9.76547C13.0672 10.0416 13.2911 10.2655 13.5672 10.2655H14.9732C15.2494 10.2655 15.4732 10.0416 15.4732 9.76547C15.4732 9.48933 15.2494 9.26547 14.9732 9.26547H13.5672Z"
        fill="#0517F8"
      />
      <path
        d="M11.2627 16.0001H10.368V16.8949H11.2627V16.0001ZM17.2777 16.0001V16.8949H18.1725V16.0001H17.2777ZM12.1574 8.32345V8.32345V6.53397C11.1691 6.53397 10.368 7.33515 10.368 8.32345H12.1574ZM12.1574 16.0001V8.32345H10.368V16.0001H12.1574ZM17.2777 15.1054H11.2627V16.8949H17.2777V15.1054ZM16.383 8.32345V16.0001H18.1725V8.32345H16.383ZM16.383 8.32345H16.383H18.1725C18.1725 7.33515 17.3713 6.53397 16.383 6.53397V8.32345ZM12.1574 8.32345H16.383V6.53397H12.1574V8.32345ZM13.9619 9.76547C13.9619 9.98348 13.7852 10.1602 13.5672 10.1602V8.37074C12.7969 8.37074 12.1725 8.99518 12.1725 9.76547H13.9619ZM13.5672 9.37074C13.7852 9.37074 13.9619 9.54746 13.9619 9.76547H12.1725C12.1725 10.5358 12.7969 11.1602 13.5672 11.1602V9.37074ZM14.9732 9.37074H13.5672V11.1602H14.9732V9.37074ZM14.5785 9.76547C14.5785 9.54747 14.7552 9.37074 14.9732 9.37074V11.1602C15.7435 11.1602 16.3679 10.5358 16.3679 9.76547H14.5785ZM14.9732 10.1602C14.7552 10.1602 14.5785 9.98348 14.5785 9.76547H16.3679C16.3679 8.99518 15.7435 8.37074 14.9732 8.37074V10.1602ZM13.5672 10.1602H14.9732V8.37074H13.5672V10.1602Z"
        fill="#F3F4F6"
        mask="url(#path-1-outside-1_20377_60879)"
      />
      <mask
        id="path-3-outside-2_20377_60879"
        maskUnits="userSpaceOnUse"
        x="1.8418"
        y="3"
        width="11"
        height="14"
        fill="black"
      >
        <rect fill="white" x="1.8418" y="3" width="11" height="14" />
        <path
          fillRule="evenodd"
          clipRule="evenodd"
          d="M3.73653 4C3.24238 4 2.8418 4.40059 2.8418 4.89474V16H11.2628V4.89474C11.2628 4.40059 10.8623 4 10.3681 4H3.73653ZM5.86807 6.57141C5.59193 6.57141 5.36807 6.79527 5.36807 7.07141C5.36807 7.34755 5.59193 7.57141 5.86807 7.57141H8.23649C8.51264 7.57141 8.73649 7.34755 8.73649 7.07141C8.73649 6.79527 8.51264 6.57141 8.23649 6.57141H5.86807Z"
        />
      </mask>
      <path
        fillRule="evenodd"
        clipRule="evenodd"
        d="M3.73653 4C3.24238 4 2.8418 4.40059 2.8418 4.89474V16H11.2628V4.89474C11.2628 4.40059 10.8623 4 10.3681 4H3.73653ZM5.86807 6.57141C5.59193 6.57141 5.36807 6.79527 5.36807 7.07141C5.36807 7.34755 5.59193 7.57141 5.86807 7.57141H8.23649C8.51264 7.57141 8.73649 7.34755 8.73649 7.07141C8.73649 6.79527 8.51264 6.57141 8.23649 6.57141H5.86807Z"
        fill="#0517F8"
      />
      <path
        d="M2.8418 16H1.94706V16.8947H2.8418V16ZM11.2628 16V16.8947H12.1576V16H11.2628ZM3.73653 4.89474V4.89474V3.10526C2.74823 3.10526 1.94706 3.90644 1.94706 4.89474H3.73653ZM3.73653 16V4.89474H1.94706V16H3.73653ZM11.2628 15.1053H2.8418V16.8947H11.2628V15.1053ZM10.3681 4.89474V16H12.1576V4.89474H10.3681ZM10.3681 4.89474H10.3681H12.1576C12.1576 3.90644 11.3564 3.10526 10.3681 3.10526V4.89474ZM3.73653 4.89474H10.3681V3.10526H3.73653V4.89474ZM6.26281 7.07141C6.26281 7.28942 6.08608 7.46615 5.86807 7.46615V5.67667C5.09778 5.67667 4.47334 6.30112 4.47334 7.07141H6.26281ZM5.86807 6.67667C6.08608 6.67667 6.26281 6.8534 6.26281 7.07141H4.47334C4.47334 7.8417 5.09778 8.46615 5.86807 8.46615V6.67667ZM8.23649 6.67667H5.86807V8.46615H8.23649V6.67667ZM7.84176 7.07141C7.84176 6.8534 8.01849 6.67667 8.23649 6.67667V8.46615C9.00678 8.46615 9.63123 7.8417 9.63123 7.07141H7.84176ZM8.23649 7.46615C8.01849 7.46615 7.84176 7.28942 7.84176 7.07141H9.63123C9.63123 6.30112 9.00679 5.67667 8.23649 5.67667V7.46615ZM5.86807 7.46615H8.23649V5.67667H5.86807V7.46615Z"
        fill="#F3F4F6"
        mask="url(#path-3-outside-2_20377_60879)"
      />
      <path
        d="M2 16L18 16"
        stroke="#0517F8"
        strokeWidth="1.3"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_20377_60879">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SettingsFilledIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_20377_46507)">
      <g clipPath="url(#clip1_20377_46507)">
        <path
          d="M18.3442 11.7504L17.1801 10.2035C17.1879 10.0629 17.1801 9.91446 17.1801 9.80509L18.3442 8.2504C18.402 8.17474 18.4414 8.08656 18.459 7.99294C18.4767 7.89933 18.4721 7.80288 18.4457 7.71134C18.2488 6.99107 17.9628 6.29821 17.5942 5.64884C17.5466 5.56635 17.4812 5.4955 17.4029 5.44144C17.3245 5.38739 17.235 5.35148 17.141 5.33634L15.227 5.0629L14.9379 4.77384L14.6645 2.85978C14.6492 2.7668 14.6141 2.67821 14.5615 2.60002C14.5089 2.52183 14.4401 2.45588 14.3598 2.40665C13.707 2.0394 13.0117 1.75342 12.2895 1.55509C12.1979 1.5287 12.1015 1.52415 12.0079 1.54179C11.9142 1.55942 11.8261 1.59877 11.7504 1.65665L10.2035 2.8129H9.79728L8.2504 1.65665C8.17474 1.59877 8.08656 1.55942 7.99294 1.54179C7.89933 1.52415 7.80288 1.5287 7.71134 1.55509C6.99107 1.75198 6.29821 2.03805 5.64884 2.40665C5.56635 2.45421 5.4955 2.51956 5.44144 2.59794C5.38739 2.67632 5.35148 2.76577 5.33634 2.85978L5.0629 4.77384L4.77384 5.0629L2.85978 5.33634C2.76577 5.35148 2.67632 5.38739 2.59794 5.44144C2.51956 5.4955 2.45421 5.56635 2.40665 5.64884C2.03805 6.29821 1.75198 6.99107 1.55509 7.71134C1.5287 7.80288 1.52415 7.89933 1.54179 7.99294C1.55942 8.08656 1.59877 8.17474 1.65665 8.2504L2.82071 9.79728V10.1957L1.65665 11.7504C1.59877 11.8261 1.55942 11.9142 1.54179 12.0079C1.52415 12.1015 1.5287 12.1979 1.55509 12.2895C1.75198 13.0097 2.03805 13.7026 2.40665 14.352C2.45421 14.4345 2.51956 14.5053 2.59794 14.5594C2.67632 14.6134 2.76577 14.6493 2.85978 14.6645L4.77384 14.9379L5.0629 15.227L5.33634 17.141C5.35148 17.235 5.38739 17.3245 5.44144 17.4029C5.4955 17.4812 5.56635 17.5466 5.64884 17.5942C6.29821 17.9628 6.99107 18.2488 7.71134 18.4457C7.76466 18.4612 7.81989 18.469 7.8754 18.4692C8.01091 18.4709 8.14305 18.4269 8.2504 18.3442L9.79728 17.1879H10.2035L11.7504 18.3442C11.8261 18.402 11.9142 18.4414 12.0079 18.459C12.1015 18.4767 12.1979 18.4721 12.2895 18.4457C13.0102 18.2502 13.7032 17.9641 14.352 17.5942C14.4345 17.5466 14.5053 17.4812 14.5594 17.4029C14.6134 17.3245 14.6493 17.235 14.6645 17.141L14.9379 15.2192C15.0317 15.1254 15.141 15.0238 15.2192 14.9379L17.141 14.6645C17.235 14.6493 17.3245 14.6134 17.4029 14.5594C17.4812 14.5053 17.5466 14.4345 17.5942 14.352C17.9628 13.7026 18.2488 13.0097 18.4457 12.2895C18.4721 12.1979 18.4767 12.1015 18.459 12.0079C18.4414 11.9142 18.402 11.8261 18.3442 11.7504ZM10.0004 13.4379C9.32053 13.4379 8.65592 13.2363 8.09063 12.8586C7.52534 12.4809 7.08474 11.944 6.82457 11.3159C6.56439 10.6878 6.49632 9.99659 6.62895 9.32978C6.76159 8.66297 7.08898 8.05047 7.56972 7.56972C8.05047 7.08898 8.66297 6.76159 9.32978 6.62895C9.99659 6.49632 10.6878 6.56439 11.3159 6.82457C11.944 7.08474 12.4809 7.52534 12.8586 8.09063C13.2363 8.65592 13.4379 9.32053 13.4379 10.0004C13.4379 10.9121 13.0757 11.7864 12.4311 12.4311C11.7864 13.0757 10.9121 13.4379 10.0004 13.4379Z"
          fill="#0517F8"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_20377_46507">
        <rect width="20" height="20" fill="white" />
      </clipPath>
      <clipPath id="clip1_20377_46507">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SidebarProceduresIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.1783 5.845L12.8217 3.48833C12.5092 3.17583 12.085 3 11.6433 3H5.66667C4.74583 3 4 3.74583 4 4.66667V16.3333C4 17.2542 4.74583 18 5.66667 18H14C14.9208 18 15.6667 17.2542 15.6667 16.3333V7.02333C15.6667 6.58167 15.4908 6.1575 15.1783 5.845V5.845Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.6667 7.16667H12.3333C11.8733 7.16667 11.5 6.79333 11.5 6.33333V3"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.5 12.1667H11.5"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.5 14.6667H10.1083"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SidebarProceduresFilledIcon = (props) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12.362 3.94795L14.7187 6.30462C14.9095 6.49536 15.0167 6.75425 15.0167 7.02333V16.3333C15.0167 16.8952 14.5618 17.35 14 17.35H5.66667C5.10482 17.35 4.65 16.8952 4.65 16.3333V4.66667C4.65 4.10482 5.10482 3.65 5.66667 3.65H11.6433C11.9124 3.65 12.1713 3.75721 12.362 3.94795Z"
      fill="#0517F8"
      stroke="#0517F8"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.6667 7.16667H12.3333C11.8733 7.16667 11.5 6.79333 11.5 6.33333V3"
      stroke="#F3F4F6"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.5 12.1667H11.5"
      stroke="#F3F4F6"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.5 14.6667H10.1083"
      stroke="#F3F4F6"
      strokeWidth="1.3"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DuplicateIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x="5.83203"
      y="5.83203"
      width="11.6715"
      height="11.6715"
      rx="2"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.83082 14.1686H4.16345C3.2426 14.1686 2.49609 13.4221 2.49609 12.5012V4.16443C2.49609 3.24357 3.2426 2.49707 4.16345 2.49707H12.5003C13.4211 2.49707 14.1676 3.24357 14.1676 4.16443V5.83179"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DuplicateReverseIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.3593 17.538H11.5636C12.4639 17.538 13.1946 16.8684 13.1946 16.0434V8.13715C13.1946 7.31214 12.4639 6.64258 11.5636 6.64258H4.3593C3.45897 6.64258 2.72827 7.31214 2.72827 8.13715V16.0449C2.72827 16.8699 3.45734 17.538 4.3593 17.538Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.21362 6.64278V4.02728C7.21362 3.20228 7.94432 2.53271 8.84465 2.53271H16.1027C17.0031 2.53271 17.7338 3.20228 17.7338 4.02728V11.935C17.7338 12.76 17.0031 13.4296 16.1027 13.4296H13.193"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DocumentFieldTypeIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_19871_154970)">
      <path
        d="M12.5 14H3.5C3.36739 14 3.24021 13.9473 3.14645 13.8536C3.05268 13.7598 3 13.6326 3 13.5V2.5C3 2.36739 3.05268 2.24021 3.14645 2.14645C3.24021 2.05268 3.36739 2 3.5 2H9.5L13 5.5V13.5C13 13.6326 12.9473 13.7598 12.8536 13.8536C12.7598 13.9473 12.6326 14 12.5 14Z"
        stroke={strokeColor || "#0517F8"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.5 2V5.5H13"
        stroke={strokeColor || "#0517F8"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.5 9.5H9.5"
        stroke={strokeColor || "#0517F8"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8 8V11"
        stroke={strokeColor || "#0517F8"}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_19871_154970">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ImageFieldTypeIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_19871_154962)">
      <path
        d="M13.5 3H2.5C2.22386 3 2 3.22386 2 3.5V12.5C2 12.7761 2.22386 13 2.5 13H13.5C13.7761 13 14 12.7761 14 12.5V3.5C14 3.22386 13.7761 3 13.5 3Z"
        stroke={strokeColor || "#00A5BC"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 10.4999L5.14375 7.35619C5.19027 7.30895 5.24573 7.27144 5.30689 7.24583C5.36805 7.22022 5.43369 7.20703 5.5 7.20703C5.56631 7.20703 5.63195 7.22022 5.69311 7.24583C5.75427 7.27144 5.80973 7.30895 5.85625 7.35619L8.64375 10.1437C8.69027 10.1909 8.74573 10.2285 8.80689 10.2541C8.86805 10.2797 8.93369 10.2929 9 10.2929C9.06631 10.2929 9.13195 10.2797 9.19311 10.2541C9.25427 10.2285 9.30973 10.1909 9.35625 10.1437L10.6438 8.85619C10.6903 8.80895 10.7457 8.77144 10.8069 8.74583C10.8681 8.72022 10.9337 8.70703 11 8.70703C11.0663 8.70703 11.1319 8.72022 11.1931 8.74583C11.2543 8.77144 11.3097 8.80895 11.3562 8.85619L14 11.4999"
        stroke={strokeColor || "#00A5BC"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M9.75 7C10.1642 7 10.5 6.66421 10.5 6.25C10.5 5.83579 10.1642 5.5 9.75 5.5C9.33579 5.5 9 5.83579 9 6.25C9 6.66421 9.33579 7 9.75 7Z"
        fill={strokeColor || "#00A5BC"}
      />
    </g>
    <defs>
      <clipPath id="clip0_19871_154962">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const TableFieldTypeIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.6667 14H3.33333C2.59667 14 2 13.4033 2 12.6667V3.33333C2 2.59667 2.59667 2 3.33333 2H12.6667C13.4033 2 14 2.59667 14 3.33333V12.6667C14 13.4033 13.4033 14 12.6667 14Z"
      stroke={strokeColor || "#00A5BC"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2 5.99984H14"
      stroke={strokeColor || "#00A5BC"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2 9.99984H14"
      stroke={strokeColor || "#00A5BC"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.00033 6V14"
      stroke={strokeColor || "#00A5BC"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ProcedureDescriptionIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.49707 3.36362H13.3349"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.5033 16.702L13.3349 8.36523L9.1665 16.702"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 15.035H16.6694"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.49707 6.69809H10.0002"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.49707 10.0331H6.66547"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PdfAttachmentIcon = ({ strokeColor, ...props }) => (
  <svg
    width="41"
    height="41"
    viewBox="0 0 41 41"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M30.7061 10.6974L25.99 5.98116C25.3646 5.35579 24.5157 5.00391 23.6319 5.00391H11.6713C9.82851 5.00391 8.33594 6.49648 8.33594 8.33926V31.6867C8.33594 33.5295 9.82851 35.0221 11.6713 35.0221H28.348C30.1908 35.0221 31.6834 33.5295 31.6834 31.6867V13.0554C31.6834 12.1716 31.3315 11.3227 30.7061 10.6974V10.6974Z"
      stroke={strokeColor || "#828B9D"}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M31.6821 13.3423H25.0114C24.0909 13.3423 23.3438 12.5952 23.3438 11.6746V5.00391"
      stroke={strokeColor || "#828B9D"}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.2422 27.6014H22.7789"
      stroke={strokeColor || "#828B9D"}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M18.0625 21.2658V19.318C18.0625 18.781 18.4978 18.3457 19.0348 18.3457H20.9826C21.5196 18.3457 21.9549 18.781 21.9549 19.318V21.2658C21.9549 21.8028 21.5196 22.2381 20.9826 22.2381H19.0348C18.4978 22.2364 18.0625 21.8011 18.0625 21.2658Z"
      stroke={strokeColor || "#828B9D"}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.3359 29.049V27.1012C13.3359 26.5642 13.7712 26.1289 14.3082 26.1289H16.256C16.793 26.1289 17.2283 26.5642 17.2283 27.1012V29.049C17.2283 29.586 16.793 30.0213 16.256 30.0213H14.3082C13.7712 30.0213 13.3359 29.586 13.3359 29.049Z"
      stroke={strokeColor || "#828B9D"}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M22.7891 29.049V27.1012C22.7891 26.5642 23.2243 26.1289 23.7613 26.1289H25.7092C26.2462 26.1289 26.6814 26.5642 26.6814 27.1012V29.049C26.6797 29.586 26.2445 30.0213 25.7075 30.0213H23.7596C23.2243 30.0213 22.7891 29.586 22.7891 29.049H22.7891Z"
      stroke={strokeColor || "#828B9D"}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M23.7448 26.133L21.2266 22.1973"
      stroke={strokeColor || "#828B9D"}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.2734 26.133L18.7916 22.1973"
      stroke={strokeColor || "#828B9D"}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ProcedureCreateNote = () => (
  <svg
    width="136"
    height="150"
    viewBox="0 0 136 150"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M49.773 146.353C49.8807 147.251 49.9854 148.148 50.0931 149.046C33.5288 116.972 16.9645 84.9007 0.400192 52.8311C0.183832 52.4121 0.0521352 51.9546 0.0126244 51.4847C-0.0268864 51.0148 0.0265629 50.5418 0.169919 50.0925C0.313275 49.6433 0.54373 49.2267 0.84812 48.8665C1.15251 48.5064 1.52487 48.2097 1.94393 47.9935L18.955 39.2217C29.2267 74.9312 39.4993 110.642 49.773 146.353Z"
      fill="#A2AAB8"
    />
    <path
      d="M53.2228 150C52.2171 149.998 51.2393 149.669 50.4361 149.064C49.6329 148.459 49.0476 147.609 48.7681 146.643L14.734 28.3316C14.3957 27.1498 14.5399 25.882 15.1349 24.8064C15.73 23.7307 16.7273 22.9349 17.9082 22.5934L95.8313 0.179263C97.0131 -0.159005 98.2808 -0.0148098 99.3565 0.580231C100.432 1.17527 101.228 2.17259 101.569 3.35351L135.604 121.662C135.942 122.844 135.798 124.113 135.203 125.189C134.608 126.265 133.61 127.061 132.429 127.403L54.5062 149.818C54.0892 149.939 53.6571 150 53.2228 150ZM97.1087 2.09099C96.8719 2.09126 96.6363 2.12449 96.4087 2.18972L18.4856 24.6069C17.8417 24.7992 17.2994 25.2372 16.9758 25.8261C16.6523 26.415 16.5734 27.1076 16.7564 27.7542L50.7785 146.066C50.9676 146.712 51.4045 147.258 51.9941 147.583C52.5837 147.909 53.278 147.988 53.9258 147.804L131.849 125.39C132.496 125.201 133.042 124.765 133.368 124.175C133.694 123.585 133.774 122.891 133.59 122.243L99.556 3.93091C99.464 3.61026 99.3093 3.31105 99.1008 3.05067C98.8923 2.79029 98.6341 2.57391 98.3413 2.4141C97.9643 2.20402 97.5403 2.09286 97.1087 2.09099Z"
      fill="#172B4D"
    />
    <path d="M114.825 93.5874L54.8887 112.435L114.825 93.5874Z" fill="white" />
    <path
      d="M54.8881 113.486C54.6377 113.486 54.3956 113.396 54.2055 113.233C54.0154 113.07 53.89 112.845 53.852 112.597C53.814 112.35 53.8659 112.097 53.9984 111.884C54.1308 111.672 54.3351 111.514 54.574 111.439L114.511 92.5912C114.776 92.5079 115.063 92.5332 115.309 92.6617C115.556 92.7902 115.741 93.0113 115.824 93.2763C115.907 93.5413 115.882 93.8286 115.753 94.0749C115.625 94.3212 115.404 94.5064 115.139 94.5897L55.2022 113.438C55.1006 113.47 54.9947 113.486 54.8881 113.486Z"
      fill="#172B4D"
    />
    <path d="M92.8518 22.8657L32.9121 41.7167L92.8518 22.8657Z" fill="white" />
    <path
      d="M32.9105 42.779C32.6614 42.7758 32.4216 42.6838 32.2342 42.5197C32.0468 42.3556 31.924 42.13 31.888 41.8835C31.8519 41.6369 31.905 41.3856 32.0376 41.1747C32.1701 40.9638 32.3736 40.807 32.6114 40.7326L92.551 21.8846C92.816 21.8013 93.1033 21.8267 93.3496 21.9552C93.5959 22.0837 93.7811 22.3047 93.8644 22.5698C93.9477 22.8348 93.9223 23.122 93.7938 23.3683C93.6654 23.6146 93.4443 23.7998 93.1793 23.8831L33.2396 42.7311C33.1332 42.7646 33.0221 42.7808 32.9105 42.779Z"
      fill="#172B4D"
    />
    <path d="M96.3371 34.7817L36.4004 53.6297L96.3371 34.7817Z" fill="white" />
    <path
      d="M36.3871 54.6799C36.138 54.6767 35.8982 54.5847 35.7108 54.4206C35.5233 54.2564 35.4006 54.0309 35.3645 53.7843C35.3285 53.5378 35.3815 53.2865 35.5141 53.0756C35.6467 52.8647 35.8502 52.7079 36.0879 52.6335L96.0246 33.7855C96.1558 33.7443 96.2939 33.7293 96.4309 33.7414C96.5679 33.7535 96.7012 33.7924 96.8232 33.8561C96.9451 33.9197 97.0534 34.0067 97.1417 34.1121C97.23 34.2176 97.2967 34.3394 97.338 34.4706C97.3792 34.6019 97.3942 34.7399 97.3821 34.8769C97.37 35.014 97.331 35.1473 97.2674 35.2692C97.2038 35.3912 97.1168 35.4994 97.0113 35.5877C96.9059 35.6761 96.7841 35.7428 96.6529 35.784L36.7162 54.632C36.6097 54.6652 36.4986 54.6814 36.3871 54.6799Z"
      fill="#172B4D"
    />
    <path d="M76.3275 55.667L39.6816 67.1463L76.3275 55.667Z" fill="white" />
    <path
      d="M39.682 68.2083C39.4329 68.2051 39.1931 68.1131 39.0057 67.949C38.8183 67.7848 38.6955 67.5593 38.6595 67.3127C38.6234 67.0662 38.6765 66.8149 38.809 66.604C38.9416 66.3931 39.1451 66.2363 39.3829 66.1619L76.0258 54.6826C76.1576 54.6393 76.2967 54.6227 76.435 54.6336C76.5733 54.6446 76.708 54.6829 76.8314 54.7464C76.9548 54.8099 77.0643 54.8972 77.1536 55.0034C77.2429 55.1096 77.3102 55.2324 77.3517 55.3648C77.3931 55.4972 77.4078 55.6365 77.3949 55.7747C77.382 55.9128 77.3418 56.047 77.2766 56.1695C77.2115 56.2919 77.1226 56.4002 77.0152 56.4881C76.9078 56.5759 76.784 56.6415 76.651 56.6811L39.9962 68.1455C39.8953 68.1825 39.7894 68.2037 39.682 68.2083Z"
      fill="#172B4D"
    />
    <path
      d="M113.431 109.351L106.287 123.191C105.114 118.222 103.942 113.255 102.771 108.289C106.142 74.3823 109.516 40.4759 112.893 6.56938C113.195 5.29565 113.949 4.17476 115.015 3.41458C116.08 2.65441 117.386 2.30654 118.688 2.4355C119.991 2.56446 121.203 3.16149 122.099 4.11587C122.995 5.07024 123.514 6.3172 123.561 7.62547L113.431 109.351Z"
      fill="#CDD1FE"
    />
    <path
      d="M105.911 126.186L101.723 108.361L111.895 6.30344C112.265 4.79355 113.167 3.4679 114.436 2.57002C115.705 1.67213 117.256 1.26232 118.803 1.41585C120.35 1.56939 121.789 2.27596 122.857 3.40578C123.925 4.53559 124.549 6.01277 124.616 7.56596V7.64673L114.465 109.665L105.911 126.186ZM103.817 108.22L106.641 120.187L112.397 109.043L122.503 7.60485C122.45 6.56328 122.025 5.57539 121.305 4.8205C120.586 4.06561 119.619 3.59376 118.581 3.49059C117.543 3.38742 116.503 3.65978 115.649 4.25823C114.795 4.85667 114.183 5.74154 113.926 6.7522L103.817 108.22Z"
      fill="#172B4D"
    />
    <path
      d="M106.287 123.191C105.114 118.223 103.942 113.256 102.771 108.289C103.295 107.256 104.119 106.405 105.135 105.848C106.151 105.291 107.312 105.055 108.465 105.169C109.618 105.284 110.709 105.745 111.596 106.491C112.482 107.238 113.122 108.235 113.431 109.351L106.287 123.191Z"
      fill="white"
    />
    <path
      d="M105.91 126.186L101.662 108.158L101.839 107.814C102.458 106.592 103.432 105.586 104.633 104.928C105.834 104.27 107.206 103.991 108.568 104.126C109.931 104.262 111.221 104.806 112.269 105.688C113.317 106.57 114.073 107.748 114.44 109.067L114.554 109.465L105.91 126.186ZM103.876 108.445L106.646 120.193L112.292 109.259C111.987 108.429 111.455 107.703 110.756 107.163C110.056 106.623 109.219 106.291 108.34 106.206C107.46 106.121 106.575 106.286 105.785 106.682C104.996 107.078 104.334 107.689 103.876 108.445Z"
      fill="#172B4D"
    />
    <path
      d="M121.279 20.5742C121.041 19.6513 120.526 18.8234 119.804 18.2017C119.082 17.5801 118.186 17.1945 117.238 17.0967C116.29 16.999 115.335 17.1938 114.501 17.655C113.667 18.1163 112.994 18.8217 112.573 19.6767L110.697 18.7432C111.309 17.508 112.284 16.4892 113.491 15.8233C114.698 15.1574 116.08 14.8763 117.451 15.0175C118.823 15.1588 120.118 15.7156 121.164 16.6135C122.21 17.5115 122.957 18.7076 123.304 20.0417L121.279 20.5742Z"
      fill="#172B4D"
    />
    <path
      d="M120.605 28.5528C120.367 27.6301 119.851 26.8026 119.129 26.1813C118.406 25.56 117.511 25.1746 116.563 25.0769C115.615 24.9792 114.66 25.1738 113.825 25.6347C112.991 26.0956 112.318 26.8007 111.896 27.6552L110.023 26.7218C110.635 25.4865 111.61 24.4676 112.817 23.8015C114.024 23.1354 115.405 22.854 116.777 22.9949C118.148 23.1358 119.443 23.6923 120.49 24.5899C121.536 25.4875 122.283 26.6833 122.631 28.0172L120.605 28.5528Z"
      fill="#172B4D"
    />
  </svg>
);

export const FieldIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="20" height="20" rx="4" fill="#E5F7F5" />
      <g clipPath="url(#clip0_19871_148567)">
        <path
          d="M4.5 4.5V15.5"
          stroke="#00B09B"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M13 5.5H7C6.72386 5.5 6.5 5.72386 6.5 6V8.5C6.5 8.77614 6.72386 9 7 9H13C13.2761 9 13.5 8.77614 13.5 8.5V6C13.5 5.72386 13.2761 5.5 13 5.5Z"
          stroke="#00B09B"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
        <path
          d="M15.5 11H7C6.72386 11 6.5 11.2239 6.5 11.5V14C6.5 14.2761 6.72386 14.5 7 14.5H15.5C15.7761 14.5 16 14.2761 16 14V11.5C16 11.2239 15.7761 11 15.5 11Z"
          stroke="#00B09B"
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
      <defs>
        <clipPath id="clip0_19871_148567">
          <rect
            width="16"
            height="16"
            fill="white"
            transform="translate(2 2)"
          />
        </clipPath>
      </defs>
    </svg>
  );
};
export const SectionIcon = () => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
    >
      <rect width="20" height="20" rx="4" fill="#FFE4CC" />
      <path
        d="M5.33398 12V14.6667C5.33398 15.4033 5.93065 16 6.66732 16H13.334C14.0707 16 14.6673 15.4033 14.6673 14.6667V12"
        stroke="#FF7A00"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.6673 8V7.21867C14.6673 6.86533 14.5267 6.526 14.2767 6.276L12.3913 4.39067C12.1413 4.14067 11.802 4 11.4487 4H6.66732C5.93065 4 5.33398 4.59667 5.33398 5.33333V8"
        stroke="#FF7A00"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.334 9.99984H12.6673"
        stroke="#FF7A00"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M14.666 9.99984H15.9993"
        stroke="#FF7A00"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M7.59961 9.99984H8.93294"
        stroke="#FF7A00"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4 9.99984H5.33333"
        stroke="#FF7A00"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};
export const HeadingIcon = ({ strokeColor, ...props }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
      xmlns="http://www.w3.org/2000/svg"
      {...props}
    >
      <rect width="20" height="20" rx="4" fill="#E6E8FE" />
      <path
        d="M3.99609 4.66453H12.6664"
        stroke="#0517F8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M16.0015 15.3355L12.6668 8.66602L9.33203 15.3355"
        stroke="#0517F8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 14.0014H15.3356"
        stroke="#0517F8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.99609 7.33201H9.99859"
        stroke="#0517F8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.99609 9.99998H7.33082"
        stroke="#0517F8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </svg>
  );
};

export const ShareIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.23473 7.45814C5.16136 7.91785 2.08221 11.3623 2.08008 15.4615V15.9717C3.85039 13.8391 6.46346 12.5855 9.23473 12.5394V15.2297C9.23482 15.6201 9.45794 15.976 9.80922 16.1463C10.1605 16.3165 10.5782 16.271 10.8846 16.0292L17.5457 10.7695C17.7822 10.5831 17.9202 10.2986 17.9202 9.99753C17.9202 9.69642 17.7822 9.41192 17.5457 9.22554L10.8846 3.96585C10.5782 3.72404 10.1605 3.67858 9.80922 3.84881C9.45794 4.01903 9.23482 4.37501 9.23473 4.76535V7.45814Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ProcedureTemplateSaveAlertIcon = ({
  strokeColor,
  fillColor,
  ...props
}) => (
  <svg
    width="136"
    height="131"
    viewBox="0 0 136 131"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M48.4113 107.163C48.475 107.693 48.5368 108.224 48.6005 108.754C38.8125 89.8014 29.0245 70.85 19.2365 51.8999C19.1086 51.6523 19.0308 51.3819 19.0075 51.1043C18.9841 50.8266 19.0157 50.547 19.1004 50.2816C19.1851 50.0161 19.3213 49.77 19.5012 49.5571C19.681 49.3443 19.9011 49.169 20.1487 49.0412L30.2007 43.8579C36.2703 64.959 42.3405 86.0606 48.4113 107.163Z"
      fill="#A2AAB8"
    />
    <path
      d="M50.4489 109.318C49.8547 109.317 49.2769 109.122 48.8023 108.765C48.3277 108.407 47.9818 107.905 47.8166 107.334L27.7055 37.423C27.5057 36.7247 27.5909 35.9756 27.9425 35.3399C28.2941 34.7043 28.8834 34.2341 29.5812 34.0323L75.6267 20.7876C76.325 20.5877 77.0741 20.6729 77.7098 21.0245C78.3454 21.3761 78.8156 21.9654 79.0174 22.6633L99.1285 92.5729C99.3284 93.2715 99.2433 94.0208 98.8917 94.6568C98.5401 95.2927 97.9507 95.7633 97.2528 95.9654L51.2073 109.21C50.9609 109.282 50.7056 109.318 50.4489 109.318ZM76.3815 21.9172C76.2416 21.9174 76.1024 21.937 75.9679 21.9756L29.9224 35.2221C29.542 35.3357 29.2215 35.5945 29.0303 35.9425C28.8391 36.2905 28.7925 36.6998 28.9006 37.0818L49.0046 106.993C49.1163 107.375 49.3745 107.698 49.7229 107.89C50.0713 108.082 50.4816 108.129 50.8644 108.02L96.9098 94.7757C97.2921 94.6643 97.6149 94.4063 97.8076 94.0578C98.0004 93.7094 98.0475 93.2989 97.9387 92.9159L77.8276 23.0045C77.7733 22.815 77.6819 22.6382 77.5587 22.4843C77.4355 22.3304 77.2829 22.2026 77.1099 22.1082C76.8871 21.984 76.6366 21.9183 76.3815 21.9172Z"
      fill="#172B4D"
    />
    <path d="M86.8507 75.9834L51.4336 87.1209L86.8507 75.9834Z" fill="white" />
    <path
      d="M51.4339 87.7414C51.2859 87.7415 51.1428 87.6887 51.0305 87.5924C50.9182 87.4961 50.8441 87.3628 50.8216 87.2165C50.7992 87.0703 50.8299 86.9208 50.9081 86.7953C50.9864 86.6697 51.1071 86.5764 51.2483 86.5322L86.6654 75.3947C86.822 75.3455 86.9917 75.3605 87.1373 75.4364C87.2828 75.5123 87.3922 75.6429 87.4415 75.7995C87.4907 75.9561 87.4757 76.1259 87.3998 76.2714C87.3239 76.417 87.1932 76.5264 87.0366 76.5756L51.6195 87.7131C51.5594 87.732 51.4968 87.7415 51.4339 87.7414Z"
      fill="#172B4D"
    />
    <path d="M73.8662 34.1934L38.4473 45.3326L73.8662 34.1934Z" fill="white" />
    <path
      d="M38.4486 45.9601C38.3014 45.9582 38.1597 45.9039 38.049 45.8069C37.9382 45.7099 37.8657 45.5766 37.8444 45.4309C37.8231 45.2853 37.8544 45.1368 37.9328 45.0121C38.0111 44.8875 38.1314 44.7949 38.2719 44.7509L73.6907 33.6135C73.8473 33.5642 74.0171 33.5792 74.1626 33.6551C74.3082 33.7311 74.4176 33.8617 74.4668 34.0183C74.5161 34.1749 74.5011 34.3446 74.4251 34.4902C74.3492 34.6357 74.2186 34.7451 74.062 34.7944L38.6431 45.9318C38.5802 45.9516 38.5146 45.9612 38.4486 45.9601Z"
      fill="#172B4D"
    />
    <path d="M75.9269 41.2344L40.5098 52.3718L75.9269 41.2344Z" fill="white" />
    <path
      d="M40.5014 52.9923C40.3542 52.9904 40.2125 52.9361 40.1017 52.8391C39.991 52.7421 39.9184 52.6088 39.8971 52.4632C39.8758 52.3175 39.9072 52.169 39.9855 52.0444C40.0639 51.9197 40.1841 51.8271 40.3246 51.7831L75.7417 40.6457C75.8193 40.6213 75.9008 40.6124 75.9818 40.6196C76.0628 40.6267 76.1415 40.6498 76.2136 40.6874C76.2857 40.725 76.3496 40.7764 76.4018 40.8387C76.454 40.901 76.4934 40.973 76.5178 41.0505C76.5422 41.1281 76.551 41.2096 76.5439 41.2906C76.5367 41.3716 76.5137 41.4503 76.4761 41.5224C76.4385 41.5945 76.3871 41.6584 76.3248 41.7106C76.2625 41.7628 76.1905 41.8022 76.113 41.8266L40.6958 52.9641C40.6329 52.9837 40.5673 52.9932 40.5014 52.9923Z"
      fill="#172B4D"
    />
    <path d="M64.1036 53.5757L42.4492 60.3589L64.1036 53.5757Z" fill="white" />
    <path
      d="M42.4486 60.9867C42.3014 60.9848 42.1597 60.9305 42.049 60.8335C41.9382 60.7365 41.8657 60.6032 41.8444 60.4575C41.8231 60.3118 41.8544 60.1633 41.9328 60.0387C42.0111 59.9141 42.1314 59.8214 42.2719 59.7775L63.9245 52.9942C64.0024 52.9687 64.0846 52.9588 64.1663 52.9653C64.248 52.9718 64.3277 52.9944 64.4005 53.0319C64.4734 53.0694 64.5382 53.1211 64.5909 53.1838C64.6437 53.2465 64.6835 53.3191 64.708 53.3974C64.7325 53.4756 64.7411 53.5579 64.7335 53.6396C64.7259 53.7212 64.7022 53.8005 64.6636 53.8729C64.6251 53.9452 64.5726 54.0092 64.5091 54.0611C64.4457 54.113 64.3725 54.1518 64.294 54.1752L42.6343 60.9496C42.5747 60.9714 42.5121 60.984 42.4486 60.9867Z"
      fill="#172B4D"
    />
    <path
      d="M86.0274 85.2983L81.8057 93.4764C81.1127 90.5406 80.4203 87.6053 79.7285 84.6707C81.7203 64.6351 83.7138 44.5994 85.7092 24.5638C85.8878 23.8111 86.3333 23.1488 86.963 22.6996C87.5928 22.2504 88.3641 22.0448 89.1339 22.121C89.9037 22.1972 90.6198 22.55 91.1493 23.114C91.6787 23.6779 91.9857 24.4148 92.0133 25.1878L86.0274 85.2983Z"
      fill="#CDD1FE"
    />
    <path
      d="M81.5824 95.2458L79.1074 84.713L85.1181 24.4063C85.3368 23.5141 85.8701 22.7307 86.62 22.2002C87.3699 21.6696 88.2861 21.4274 89.2002 21.5182C90.1144 21.6089 90.9651 22.0264 91.5961 22.694C92.227 23.3617 92.596 24.2345 92.635 25.1523V25.2001L86.6367 85.4838L81.5824 95.2458ZM80.3449 84.6299L82.0138 91.7013L85.4151 85.1161L91.3869 25.1753C91.3553 24.5598 91.1041 23.9761 90.6789 23.53C90.2537 23.0839 89.6826 22.8051 89.0693 22.7442C88.4561 22.6832 87.8413 22.8441 87.3366 23.1978C86.8318 23.5514 86.4706 24.0743 86.3185 24.6715L80.3449 84.6299Z"
      fill="#172B4D"
    />
    <path
      d="M81.8057 93.4765C81.1127 90.5407 80.4203 87.6054 79.7285 84.6708C80.0379 84.06 80.5248 83.5572 81.1253 83.2281C81.7257 82.8991 82.4116 82.7594 83.0929 82.8273C83.7742 82.8952 84.419 83.1675 84.9427 83.6085C85.4664 84.0495 85.8445 84.6386 86.0274 85.2984L81.8057 93.4765Z"
      fill="white"
    />
    <path
      d="M81.5826 95.246L79.0723 84.5929L79.1766 84.3896C79.5424 83.6678 80.118 83.0735 80.8277 82.6846C81.5373 82.2957 82.348 82.1305 83.1533 82.2106C83.9585 82.2907 84.7207 82.6124 85.3399 83.1335C85.9591 83.6545 86.4063 84.3506 86.6228 85.1303L86.6899 85.3654L81.5826 95.246ZM80.3805 84.7626L82.0175 91.705L85.3534 85.2435C85.1735 84.7534 84.8589 84.3242 84.4457 84.0051C84.0326 83.686 83.5377 83.49 83.0181 83.4398C82.4985 83.3896 81.9753 83.4871 81.5087 83.7211C81.042 83.9551 80.651 84.3161 80.3805 84.7626Z"
      fill="#172B4D"
    />
    <path
      d="M90.665 32.8392C90.5244 32.2939 90.2203 31.8046 89.7934 31.4373C89.3665 31.0699 88.8375 30.8421 88.2773 30.7843C87.7171 30.7266 87.1526 30.8417 86.6598 31.1142C86.167 31.3868 85.7694 31.8036 85.5206 32.3088L84.4121 31.7573C84.7738 31.0273 85.3498 30.4253 86.0631 30.0318C86.7764 29.6384 87.5929 29.4722 88.4032 29.5557C89.2136 29.6392 89.979 29.9682 90.5972 30.4988C91.2153 31.0294 91.6566 31.7362 91.8618 32.5245L90.665 32.8392Z"
      fill="#172B4D"
    />
    <path
      d="M90.2666 37.5537C90.1255 37.0085 89.8211 36.5195 89.3941 36.1523C88.9671 35.7852 88.438 35.5575 87.8778 35.4997C87.3176 35.442 86.7532 35.557 86.2603 35.8294C85.7674 36.1017 85.3696 36.5183 85.1203 37.0233L84.0137 36.4717C84.3752 35.7418 84.9511 35.1397 85.6643 34.7461C86.3774 34.3525 87.1938 34.1862 88.0041 34.2695C88.8145 34.3527 89.58 34.6816 90.1982 35.212C90.8164 35.7424 91.2579 36.449 91.4634 37.2372L90.2666 37.5537Z"
      fill="#172B4D"
    />
  </svg>
);
export const SignatureIcon = () => (
  <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" fill="none">
    <path
      stroke="#172B4D"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="1.2"
      d="M7.334 6.589v2.078h2.078l4.4-4.328a.667.667 0 0 0 .004-.947l-1.208-1.208a.667.667 0 0 0-.947.004l-4.327 4.4Z"
      clip-rule="evenodd"
    />
    <path
      stroke="#172B4D"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="1.2"
      d="M4.667 8.666H3.333C2.597 8.666 2 9.263 2 10v0c0 .736.597 1.333 1.333 1.333h9.334c.736 0 1.333.597 1.333 1.333v0c0 .737-.597 1.334-1.333 1.334H10"
    />
  </svg>
);
export const ActiveSignatureIcon = () => (
  <svg
    width="21"
    height="22"
    viewBox="0 0 21 22"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      stroke="#0517F8"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="1.2"
      d="M7.334 6.589v2.078h2.078l4.4-4.328a.667.667 0 0 0 .004-.947l-1.208-1.208a.667.667 0 0 0-.947.004l-4.327 4.4Z"
      clipRule="evenodd"
    />
    <path
      stroke="#0517F8"
      stroke-linecap="round"
      stroke-linejoin="round"
      stroke-width="1.2"
      d="M4.667 8.666H3.333C2.597 8.666 2 9.263 2 10v0c0 .736.597 1.333 1.333 1.333h9.334c.736 0 1.333.597 1.333 1.333v0c0 .737-.597 1.334-1.333 1.334H10"
    />
    {/* <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.06117 14.0173L14.0124 7.06606C14.4359 6.64255 14.4359 5.95643 14.0124 5.53375L12.2934 3.8147C11.8698 3.39119 11.1837 3.39119 10.761 3.8147L3.80982 10.7659C3.60557 10.9702 3.49219 11.2453 3.49219 11.5321V14.3349H6.29502C6.58181 14.3349 6.85692 14.2215 7.06117 14.0173Z"
      stroke="#0517F8"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.9953 14.334H16.4142C17.5655 14.334 18.4984 15.2669 18.4984 16.4182V16.4182C18.4984 17.5695 17.5655 18.5024 16.4142 18.5024H3.49219"
      stroke="#0517F8"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    /> */}
  </svg>
);

export const ExportProcedureIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.1667 10.8334H5.83333C3.9925 10.8334 2.5 12.3259 2.5 14.1667V14.1667C2.5 16.0075 3.9925 17.5 5.83333 17.5H14.1667C16.0075 17.5 17.5 16.0075 17.5 14.1667V14.1667C17.5 12.3259 16.0075 10.8334 14.1667 10.8334Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.16602 14.1667H14.166"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.86227 14.1374C5.87893 14.1541 5.87893 14.1799 5.86227 14.1966C5.8456 14.2133 5.81977 14.2133 5.8031 14.1966C5.78643 14.1799 5.78643 14.1541 5.8031 14.1374C5.81977 14.1208 5.84643 14.1208 5.86227 14.1374"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.5 5L10 2.5L12.5 5"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.0007 8.33333V2.5"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const ImageAttachmentIcon = ({ strokeColor, ...props }) => (
  <svg
    width="26"
    height="32"
    viewBox="0 0 26 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M23.7061 6.69735L18.99 1.98116C18.3646 1.35579 17.5157 1.00391 16.6319 1.00391H4.67129C2.82851 1.00391 1.33594 2.49648 1.33594 4.33926V27.6867C1.33594 29.5295 2.82851 31.0221 4.67129 31.0221H21.348C23.1908 31.0221 24.6834 29.5295 24.6834 27.6867V9.05544C24.6834 8.17158 24.3315 7.32273 23.7061 6.69735V6.69735Z"
      stroke="#828B9D"
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24.6821 9.34228H18.0114C17.0909 9.34228 16.3438 8.59517 16.3438 7.67461V1.00391"
      stroke="#828B9D"
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M19.7648 22.7374L16.4163 19.3889C16.1113 19.0839 15.6176 19.0839 15.3134 19.3889L11.9648 22.7374"
      stroke="#748094"
      strokeWidth="1.8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M17.4227 26.6377H8.06266C6.7702 26.6377 5.72266 25.5901 5.72266 24.2977V14.9377C5.72266 13.6452 6.7702 12.5977 8.06266 12.5977H17.4227C18.7151 12.5977 19.7627 13.6452 19.7627 14.9377V24.2977C19.7627 25.5901 18.7151 26.6377 17.4227 26.6377Z"
      stroke="#748094"
      strokeWidth="1.8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.8702 26.6373L10.9617 21.7287C10.6567 21.4238 10.163 21.4238 9.85879 21.7287L6.07812 25.5094"
      stroke="#748094"
      strokeWidth="1.8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.6164 17.0699C10.7306 17.1841 10.7306 17.3693 10.6164 17.4835C10.5021 17.5978 10.3169 17.5978 10.2027 17.4835C10.0885 17.3693 10.0885 17.1841 10.2027 17.0699C10.3169 16.9557 10.5021 16.9557 10.6164 17.0699"
      stroke="#748094"
      strokeWidth="1.8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const AccessPermissionHandIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M5 6V1.75C5 1.336 5.336 1 5.75 1V1C6.164 1 6.5 1.336 6.5 1.75V6"
      stroke={strokeColor || "#0517F8"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 2.75C5 2.336 4.664 2 4.25 2V2C3.836 2 3.5 2.336 3.5 2.75V6"
      stroke={strokeColor || "#0517F8"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.5 2.75C6.5 2.336 6.836 2 7.25 2V2C7.664 2 8 2.336 8 2.75V5.5V7L8.9505 6.0495C9.254 5.746 9.746 5.746 10.0495 6.0495V6.0495C10.3115 6.3115 10.352 6.722 10.1465 7.03L8.3905 9.664C7.834 10.4985 6.8975 11 5.8945 11H5C3.343 11 2 9.657 2 8V5.5V3.75C2 3.336 2.336 3 2.75 3V3C3.164 3 3.5 3.336 3.5 3.75"
      stroke={strokeColor || "#0517F8"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const BrandingIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M4.33162 14.0026V14.0026C3.04242 14.0026 1.99731 12.9575 1.99731 11.6683V3.33145C1.99731 2.59476 2.59452 1.99756 3.3312 1.99756H5.33204C6.06872 1.99756 6.66593 2.59476 6.66593 3.33145V11.6683C6.66593 12.9575 5.62082 14.0026 4.33162 14.0026Z"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.98242 13.319L12.4591 6.84225C12.9798 6.32137 12.9798 5.47702 12.4591 4.95613L11.0445 3.54155C10.5236 3.02082 9.67929 3.02082 9.15841 3.54155L6.66604 6.03258"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.96747 9.33398H12.6686C13.4053 9.33398 14.0025 9.93119 14.0025 10.6679V12.6687C14.0025 13.4054 13.4053 14.0026 12.6686 14.0026H4.33179"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.99731 9.97214H6.66593"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.99731 5.94187H6.66593"
      stroke="#0517F8"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);
export const ColorPickerIcon = ({ strokeColor, ...props }) => (
  <svg
    width="24"
    height="24"
    viewBox="0 0 24 24"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M18.4331 10.7129L13.2871 5.56689"
      stroke="#323232"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.145 13.1054C11.7075 15.543 10.0824 13.9179 8.99902 15.0013C10.0824 13.9179 8.45734 12.2928 10.8949 9.85526L17.0832 3.66697C17.9811 2.77182 19.4343 2.77294 20.3308 3.66946C21.2274 4.56599 21.2285 6.0192 20.3333 6.91709L14.145 13.1054Z"
      stroke="#323232"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.99658 14.0009H4.49721"
      stroke="#323232"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.99923 21.0038V19.5032"
      stroke="#323232"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.9512 18.9527L13.8901 17.8916"
      stroke="#323232"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.10847 10.1102L5.04736 9.04907"
      stroke="#323232"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.04736 18.9527L6.10847 17.8916"
      stroke="#323232"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ProcedureCheckCircle = ({ fillColor = undefined, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx="10" cy="10" r="10" fill={fillColor || "#0517F8"} />
    <g clipPath="url(#clip0_21062_187343)">
      <path
        d="M14.2969 7.26562L8.82812 12.7344L6.09375 10"
        stroke="white"
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_21062_187343">
        <rect
          width="12.5"
          height="12.5"
          fill="white"
          transform="translate(3.75 3.75)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ProcedureCrossCircle = ({ strokeColor = undefined, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle
      cx="10"
      cy="10"
      r="9.58333"
      stroke={strokeColor || "#0517F8"}
      strokeWidth="0.833333"
    />
    <g clipPath="url(#clip0_21062_187348)">
      <path
        d="M13.5156 6.48438L6.48438 13.5156"
        stroke={strokeColor || "#0517F8"}
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5156 13.5156L6.48438 6.48438"
        stroke={strokeColor || "#0517F8"}
        strokeWidth="1.25"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_21062_187348">
        <rect
          width="12.5"
          height="12.5"
          fill="white"
          transform="translate(3.75 3.75)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const ProcedureCheckboxFieldIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_21062_194622)">
      <rect
        x="2.6"
        y="2.6"
        width="10.8"
        height="10.8"
        rx="2.4"
        stroke={strokeColor || "#ECB800"}
        strokeWidth="1.2"
      />
      <g clipPath="url(#clip1_21062_194622)">
        <path
          d="M10.75 6.25L7.25 9.75L5.5 8"
          stroke={strokeColor || "#ECB800"}
          strokeWidth="1.2"
          strokeLinecap="round"
          strokeLinejoin="round"
        />
      </g>
    </g>
    <defs>
      <clipPath id="clip0_21062_194622">
        <rect width="16" height="16" fill="white" />
      </clipPath>
      <clipPath id="clip1_21062_194622">
        <rect width="8" height="8" fill="white" transform="translate(4 4)" />
      </clipPath>
    </defs>
  </svg>
);
export const ProcedureLoadingIcon = ({ strokeColor, ...props }) => (
  <svg
    width="32"
    height="32"
    viewBox="0 0 32 32"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M16.0007 23.4932V27.9998"
      stroke="#00B09B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.6929 21.3066L7.51953 24.48"
      stroke="#00B09B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.50667 15.9997H4"
      stroke="#00B09B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.51953 7.51953L10.6929 10.6929"
      stroke="#00B09B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M16.0007 8V4"
      stroke="#00B09B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M22.3594 9.63953L24.4794 7.51953"
      stroke="#00B09B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M28 15.9997H26"
      stroke="#00B09B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M24.4809 24.48L23.7743 23.7733"
      stroke="#00B09B"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const BrandingDisbaleImageIcon = ({ strokeColor, ...props }) => (
  <svg
    width="114"
    height="94"
    viewBox="0 0 114 94"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M100.111 21.7036H7.29791C5.94238 21.7036 4.84351 22.7989 4.84351 24.1501V78.5042C4.84351 79.8553 5.94238 80.9506 7.29791 80.9506H100.111C101.467 80.9506 102.565 79.8553 102.565 78.5042V24.1501C102.565 22.7989 101.467 21.7036 100.111 21.7036Z"
      fill="#E8EAED"
    />
    <path
      d="M100.111 81.6624H7.29795C6.45731 81.6618 5.65126 81.3287 5.05683 80.7362C4.46241 80.1437 4.12823 79.3403 4.12769 78.5024V24.1605C4.12823 23.3226 4.46241 22.5191 5.05683 21.9266C5.65126 21.3341 6.45731 21.001 7.29795 21.0005H100.111C100.952 21.001 101.758 21.3341 102.352 21.9266C102.947 22.5191 103.281 23.3226 103.281 24.1605V78.5126C103.278 79.3487 102.943 80.1496 102.349 80.7398C101.754 81.3301 100.95 81.6618 100.111 81.6624ZM7.29795 22.4174C6.83703 22.4179 6.39514 22.6007 6.06922 22.9255C5.7433 23.2504 5.55996 23.6909 5.55942 24.1503V78.5024C5.55996 78.9618 5.7433 79.4023 6.06922 79.7271C6.39514 80.052 6.83703 80.2347 7.29795 80.2353H100.111C100.572 80.2347 101.014 80.052 101.34 79.7271C101.666 79.4023 101.849 78.9618 101.85 78.5024V24.1605C101.849 23.7011 101.666 23.2606 101.34 22.9357C101.014 22.6109 100.572 22.4281 100.111 22.4276L7.29795 22.4174Z"
      fill="#172B4D"
    />
    <path
      d="M12.7877 29.0226C13.5332 29.0226 14.1376 28.4202 14.1376 27.6771C14.1376 26.934 13.5332 26.3315 12.7877 26.3315C12.0421 26.3315 11.4377 26.934 11.4377 27.6771C11.4377 28.4202 12.0421 29.0226 12.7877 29.0226Z"
      fill="#0517F8"
    />
    <path
      d="M17.4715 29.0226C18.217 29.0226 18.8214 28.4202 18.8214 27.6771C18.8214 26.934 18.217 26.3315 17.4715 26.3315C16.726 26.3315 16.1216 26.934 16.1216 27.6771C16.1216 28.4202 16.726 29.0226 17.4715 29.0226Z"
      fill="#0517F8"
    />
    <path
      d="M22.4825 29.0226C23.228 29.0226 23.8324 28.4202 23.8324 27.6771C23.8324 26.934 23.228 26.3315 22.4825 26.3315C21.7369 26.3315 21.1326 26.934 21.1326 27.6771C21.1326 28.4202 21.7369 29.0226 22.4825 29.0226Z"
      fill="#0517F8"
    />
    <path
      d="M107.182 93.0916H0.227295V87.5443C0.227837 86.7064 0.56202 85.9029 1.15644 85.3104C1.75087 84.7179 2.55692 84.3848 3.39756 84.3843H104.012C104.852 84.3848 105.658 84.7179 106.253 85.3104C106.847 85.9029 107.181 86.7064 107.182 87.5443V93.0916ZM1.65903 91.6645H105.75V87.5443C105.75 87.0849 105.566 86.6444 105.24 86.3195C104.914 85.9947 104.473 85.8119 104.012 85.8114H3.39756C2.93664 85.8119 2.49475 85.9947 2.16883 86.3195C1.84291 86.6444 1.65957 87.0849 1.65903 87.5443V91.6645Z"
      fill="#172B4D"
    />
    <path
      d="M54.0195 58.0971C50.7361 58.095 47.5831 56.8154 45.2321 54.5306C42.8811 52.2459 41.5178 49.1366 41.4326 45.8648C41.369 43.3821 42.0456 40.9364 43.3766 38.837C44.7076 36.7375 46.6333 35.0787 48.9103 34.0702C51.1872 33.0617 53.713 32.7488 56.1682 33.1712C58.6235 33.5936 60.898 34.7323 62.704 36.4432C64.51 38.1541 65.7665 40.3605 66.3144 42.7832C66.8624 45.2059 66.6773 47.7361 65.7825 50.054C64.8877 52.3718 63.3235 54.3731 61.2876 55.8048C59.2517 57.2365 56.8355 58.0342 54.3447 58.0971H54.0195ZM42.6598 45.822C42.7171 48.0631 43.4398 50.2368 44.7365 52.0685C46.0332 53.9001 47.8457 55.3074 49.9448 56.1123C52.0438 56.9173 54.3352 57.0837 56.5292 56.5907C58.7231 56.0977 60.7212 54.9673 62.2707 53.3424C63.8202 51.7176 64.8515 49.6712 65.2342 47.4622C65.617 45.2531 65.334 42.9805 64.4211 40.9318C63.5082 38.883 62.0063 37.1501 60.1053 35.9521C58.2044 34.7541 55.9897 34.1448 53.7414 34.2014C52.245 34.2284 50.7688 34.5517 49.3989 35.1524C48.0289 35.7531 46.7924 36.6193 45.7613 37.7006C44.7302 38.7819 43.925 40.0567 43.3925 41.4509C42.8601 42.8452 42.611 44.331 42.6598 45.822Z"
      fill="#172B4D"
    />
    <path
      d="M54.0298 58.0992C49.4728 58.0992 45.644 51.3714 45.5069 45.7507C45.3678 40.1116 48.8817 33.1657 53.4571 32.9985C54.942 32.9455 56.4699 33.6102 57.8628 34.9109C60.4235 37.3104 62.2091 41.4959 62.3073 45.3287C62.4055 49.1615 60.8346 53.4387 58.3966 55.9646C57.0712 57.3408 55.5822 58.0788 54.1014 58.0992H54.0298ZM46.7341 45.7201C46.8474 48.42 47.6667 51.0436 49.1108 53.3306C50.5671 55.6079 52.3833 56.9025 54.083 56.8759C55.2386 56.8596 56.427 56.2521 57.5212 55.1165C59.7036 52.8475 61.1783 48.8291 61.0821 45.3633C60.986 41.8975 59.3231 37.9608 57.0221 35.8038C55.8727 34.7274 54.6577 34.1728 53.5021 34.2238C51.7984 34.285 50.0537 35.6652 48.716 38.0097C47.3934 40.3648 46.7102 43.0232 46.7341 45.7221V45.7201Z"
      fill="#172B4D"
    />
    <path
      d="M45.9137 54.5211L45.2388 53.5018C47.7509 51.8548 50.6688 50.9274 53.6737 50.8208C56.9516 50.7233 60.1849 51.5959 62.9657 53.3285L62.3132 54.3641C59.7417 52.7637 56.7531 51.9566 53.7228 52.0441C50.9412 52.1407 48.2398 52.9976 45.9137 54.5211Z"
      fill="#172B4D"
    />
    <path
      d="M53.2729 40.2786C50.3558 40.2746 47.4924 39.4963 44.9771 38.0238L45.5907 36.9678C47.9883 38.3728 50.7271 39.0942 53.5081 39.0534C56.548 38.9947 59.5021 38.0383 61.9963 36.3052L62.7019 37.3062C60.0114 39.1755 56.8259 40.209 53.547 40.2766L53.2729 40.2786Z"
      fill="#172B4D"
    />
    <path
      d="M54.0913 33.5982L52.8645 33.6289L53.4666 57.5049L54.6934 57.4741L54.0913 33.5982Z"
      fill="#172B4D"
    />
    <path
      d="M65.9961 44.619L42.0325 45.2236L42.0635 46.4465L66.0271 45.8418L65.9961 44.619Z"
      fill="#172B4D"
    />
    <path d="M25.3992 66.9365L82.9405 67.0041L25.3992 66.9365Z" fill="white" />
    <path
      d="M82.9406 67.7175L25.3992 67.6503C25.2094 67.6503 25.0273 67.5751 24.893 67.4413C24.7588 67.3074 24.6833 67.1259 24.6833 66.9367C24.6833 66.7475 24.7588 66.566 24.893 66.4321C25.0273 66.2983 25.2094 66.2231 25.3992 66.2231L82.9406 66.2904C83.1304 66.2904 83.3125 66.3656 83.4468 66.4994C83.581 66.6332 83.6564 66.8147 83.6564 67.004C83.6564 67.1932 83.581 67.3747 83.4468 67.5085C83.3125 67.6424 83.1304 67.7175 82.9406 67.7175Z"
      fill="#172B4D"
    />
    <path
      d="M73.6389 55.3513C71.6025 52.4557 70.3494 49.0827 70.0012 45.5599C69.3441 38.5997 72.5437 33.482 73.5714 31.9794L76.8918 34.3008C77.2179 29.2302 78.8314 24.3265 81.5797 20.0529C82.6442 21.1447 83.708 22.2413 84.7711 23.3426C87.4549 9.97908 94.5092 1.12122 101.431 1.03525C102.823 1.01887 104.839 1.34231 107.296 3.02913"
      fill="#CDD1FE"
    />
    <path
      d="M73.0514 55.7605C70.9429 52.7607 69.6467 49.2662 69.2888 45.6171C68.6746 39.0254 71.3359 33.9854 72.9736 31.5677L73.383 30.9679L76.2797 32.9925C76.7795 28.2403 78.3872 23.6715 80.9737 19.6536L81.4609 18.8941L84.3473 21.8706C87.2337 9.20305 94.1428 0.408661 101.42 0.318588C103.496 0.291976 105.611 1.00642 107.705 2.43735L106.886 3.62058C105.044 2.35956 103.201 1.727 101.433 1.75157C94.6934 1.8355 87.9789 10.9636 85.465 23.4735L85.1969 24.8082L81.7065 21.1991C79.3145 25.1898 77.9108 29.6944 77.6123 34.3375L77.5304 35.6067L73.7719 32.9803C72.2427 35.4163 70.1874 39.8442 70.7197 45.484C71.0553 48.8862 72.2655 52.1436 74.2325 54.9396L73.0514 55.7605Z"
      fill="#172B4D"
    />
    <path
      d="M73.1086 54.9992C76.5834 55.6994 80.1802 55.4879 83.5489 54.3851C90.1877 52.1906 93.5798 47.2058 94.5501 45.6582L91.0905 43.5497C93.7051 42.1902 96.1054 40.4534 98.2144 38.3951C99.7251 36.9209 101.075 35.2904 102.241 33.5311L97.9422 31.9323C109.175 24.2147 114.391 14.1225 111.658 7.71706C111.103 6.41714 109.92 4.56246 107.087 2.9043"
      fill="#CDD1FE"
    />
    <path
      d="M77.2029 56.1209C75.7797 56.1243 74.3597 55.9872 72.9634 55.7115L73.2438 54.3072C76.5947 54.9844 80.0638 54.7827 83.3136 53.7217C88.6913 51.9428 91.9114 48.2825 93.5348 45.9018L89.6187 43.5128L90.7466 42.9253C94.8792 40.7861 98.4395 37.6884 101.13 33.8913L96.4028 32.1308L97.539 31.3509C108.037 24.1389 113.701 14.321 111.005 8.0138C110.262 6.26966 108.823 4.76299 106.73 3.53881L107.455 2.31055C109.818 3.69439 111.457 5.42625 112.323 7.45903C113.756 10.8204 113.169 15.1664 110.669 19.6987C108.294 23.9977 104.438 28.1533 99.4673 31.7583L103.336 33.1912L102.837 33.9446C100.196 37.9262 96.6601 41.2339 92.5113 43.6029L95.5287 45.4453L95.1438 46.0594C93.5921 48.5384 90.0486 53.0073 83.7619 55.0851C81.6449 55.7785 79.4306 56.1282 77.2029 56.1209Z"
      fill="#172B4D"
    />
    <path d="M99.5001 14.6484L65.8967 66.8907L99.5001 14.6484Z" fill="white" />
    <path
      d="M65.8968 67.607C65.7682 67.6063 65.6422 67.5712 65.5318 67.5053C65.4215 67.4393 65.3308 67.345 65.2694 67.232C65.2079 67.1191 65.1778 66.9918 65.1823 66.8633C65.1868 66.7348 65.2257 66.6099 65.295 66.5015L98.9024 14.2634C99.0068 14.1076 99.1682 13.9989 99.3518 13.9608C99.5354 13.9226 99.7267 13.958 99.8845 14.0593C100.042 14.1606 100.154 14.3197 100.196 14.5026C100.238 14.6854 100.206 14.8773 100.108 15.0372L66.5007 67.2774C66.4356 67.3785 66.3463 67.4616 66.2408 67.5192C66.1353 67.5768 66.017 67.6069 65.8968 67.607Z"
      fill="#172B4D"
    />
  </svg>
);

export const NoResultIcon = () => (
  <svg
    width="207"
    height="153"
    viewBox="0 0 207 153"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M114.657 88.8132L114.539 88.939L127.987 101.517L128.105 101.391L114.657 88.8132Z"
      fill="#0C266D"
    />
    <path
      d="M169.034 32.0864H11.9659C9.67189 32.0864 7.81226 33.9399 7.81226 36.2263V128.205C7.81226 130.491 9.67189 132.345 11.9659 132.345H169.034C171.328 132.345 173.188 130.491 173.188 128.205V36.2263C173.188 33.9399 171.328 32.0864 169.034 32.0864Z"
      fill="#E8EAED"
    />
    <path
      d="M169.034 133.549H11.9659C10.5433 133.548 9.17918 132.984 8.17323 131.982C7.16729 130.979 6.60175 129.619 6.60083 128.202V36.2366C6.60175 34.8186 7.16729 33.459 8.17323 32.4564C9.17918 31.4538 10.5433 30.8901 11.9659 30.8892H169.034C170.457 30.8901 171.821 31.4538 172.827 32.4564C173.833 33.459 174.398 34.8186 174.399 36.2366V128.212C174.396 129.628 173.829 130.985 172.823 131.985C171.818 132.986 170.455 133.548 169.034 133.549ZM11.9659 33.2938C11.1859 33.2947 10.4381 33.6039 9.88651 34.1537C9.33495 34.7034 9.02468 35.4488 9.02376 36.2262V128.202C9.02468 128.979 9.33495 129.724 9.88651 130.274C10.4381 130.824 11.1859 131.133 11.9659 131.134H169.034C169.814 131.133 170.562 130.824 171.114 130.274C171.665 129.724 171.975 128.979 171.976 128.202V36.2366C171.975 35.4591 171.665 34.7138 171.114 34.164C170.562 33.6143 169.814 33.305 169.034 33.3041L11.9659 33.2938Z"
      fill="#172B4D"
    />
    <path
      d="M70.639 122.716C69.5206 122.704 68.4402 122.31 67.5792 121.598C66.4561 120.674 65.7061 119.377 65.4678 117.945L55.2396 60.3861C54.987 58.9638 55.2443 57.4984 55.9664 56.2461C56.3177 55.6541 56.7902 55.1426 57.3533 54.7449C57.9163 54.3472 58.5571 54.0723 59.2339 53.9381L80.1577 50.2329L100.926 66.1026L108.797 110.262C109.226 111.66 109.102 113.168 108.451 114.478C108.089 115.149 107.558 115.714 106.91 116.119C106.263 116.523 105.521 116.753 104.757 116.786L71.449 122.651C71.1815 122.696 70.9104 122.718 70.639 122.716ZM79.7112 52.0648L59.5351 55.639C59.103 55.7285 58.6947 55.9078 58.3369 56.1652C57.979 56.4226 57.6797 56.7523 57.4583 57.1328C56.9528 58.0333 56.7743 59.0799 56.9529 60.0963L67.1708 117.655C67.3371 118.676 67.8682 119.602 68.6661 120.263C69.0044 120.549 69.4007 120.759 69.8279 120.878C70.255 120.997 70.7029 121.022 71.1409 120.953L104.595 115.047H104.671C105.137 115.028 105.591 114.888 105.986 114.641C106.382 114.394 106.706 114.049 106.928 113.639C107.365 112.726 107.442 111.681 107.142 110.714L107.111 110.6L99.3404 67.0479L79.7112 52.0648Z"
      fill="#172B4D"
    />
    <path
      d="M21.2562 44.4714C22.5178 44.4714 23.5406 43.452 23.5406 42.1944C23.5406 40.9369 22.5178 39.9175 21.2562 39.9175C19.9945 39.9175 18.9717 40.9369 18.9717 42.1944C18.9717 43.452 19.9945 44.4714 21.2562 44.4714Z"
      fill="#0517F8"
    />
    <path
      d="M29.1824 44.4714C30.4441 44.4714 31.4669 43.452 31.4669 42.1944C31.4669 40.9369 30.4441 39.9175 29.1824 39.9175C27.9207 39.9175 26.8979 40.9369 26.8979 42.1944C26.8979 43.452 27.9207 44.4714 29.1824 44.4714Z"
      fill="#0517F8"
    />
    <path
      d="M37.6627 44.4714C38.9243 44.4714 39.9471 43.452 39.9471 42.1944C39.9471 40.9369 38.9243 39.9175 37.6627 39.9175C36.401 39.9175 35.3782 40.9369 35.3782 42.1944C35.3782 43.452 36.401 44.4714 37.6627 44.4714Z"
      fill="#0517F8"
    />
    <path
      d="M115.955 118L77.648 124.762C77.025 124.872 76.3863 124.859 75.7685 124.723C75.1506 124.587 74.5657 124.331 74.0472 123.969C73.5286 123.608 73.0867 123.148 72.7465 122.617C72.4064 122.085 72.1748 121.491 72.0649 120.87L61.8193 63.2978C61.7092 62.6766 61.723 62.0399 61.8598 61.4241C61.9966 60.8083 62.2538 60.2253 62.6167 59.7086C62.9796 59.1918 63.4411 58.7514 63.9748 58.4124C64.5085 58.0734 65.1039 57.8426 65.7272 57.733L89.3023 53.5586L112.009 68.5348L119.838 112.414C120.035 113.012 120.101 113.645 120.031 114.27C119.96 114.895 119.755 115.498 119.43 116.037C118.256 117.9 116.107 117.979 115.955 118Z"
      fill="white"
    />
    <path
      d="M76.7828 125.683C75.4495 125.678 74.1603 125.207 73.1395 124.352C72.1188 123.497 71.4311 122.313 71.1962 121.005L60.9679 63.4496C60.7093 61.9717 61.0489 60.452 61.9125 59.2233C62.7761 57.9946 64.0931 57.1572 65.575 56.8947L89.4962 52.6616L112.805 68.0345L120.686 112.204C120.911 112.916 120.981 113.667 120.892 114.407C120.802 115.148 120.555 115.861 120.167 116.499C118.841 118.59 116.536 118.818 116.031 118.842L77.7866 125.59C77.4553 125.65 77.1194 125.681 76.7828 125.683ZM89.119 54.4659L65.8761 58.5817C65.3651 58.6717 64.8769 58.8611 64.4394 59.1392C64.0018 59.4173 63.6236 59.7787 63.3263 60.2025C63.0289 60.6264 62.8183 61.1045 62.7064 61.6096C62.5946 62.1147 62.5837 62.6367 62.6744 63.146L72.9026 120.701C72.9928 121.211 73.183 121.698 73.4623 122.134C73.7415 122.571 74.1044 122.948 74.53 123.244C74.9557 123.541 75.4358 123.75 75.943 123.862C76.4501 123.973 76.9742 123.983 77.4854 123.892L115.854 117.12H115.924C116.473 117.093 117.009 116.939 117.489 116.672C117.969 116.404 118.381 116.03 118.693 115.578C118.952 115.15 119.115 114.67 119.17 114.173C119.225 113.675 119.171 113.172 119.011 112.697L118.98 112.566L111.23 69.0488L89.119 54.4659Z"
      fill="#172B4D"
    />
    <path
      d="M111.282 68.9349C105.079 70.1009 100.206 70.715 97.6269 70.8496C96.7408 70.8944 95.204 70.922 93.951 70.0388C93.3056 69.5864 92.7795 68.9851 92.4176 68.2863C92.2345 67.9366 92.095 67.5658 92.0023 67.1823C91.9711 67.0546 91.9538 66.9546 91.9434 66.9063V66.8752C91.9261 66.7786 91.0331 63.1286 88.8594 54.3071"
      fill="#CDD1FE"
    />
    <path
      d="M97.0108 71.7294C95.751 71.7752 94.5083 71.4278 93.456 70.7358C92.3007 69.9349 91.4804 68.7392 91.1508 67.3756C91.1162 67.2272 91.0919 67.1065 91.0816 67.0478C91.0435 66.8788 90.4827 64.5708 88.001 54.5038L89.6832 54.0898C92.7465 66.5303 92.7672 66.6442 92.7984 66.7132C92.8295 66.7822 92.8261 66.865 92.8503 66.9685C93.0822 67.9234 93.6573 68.7605 94.4667 69.3213C95.5571 70.0803 96.99 70.0113 97.6027 69.9768C100.548 69.825 105.612 69.1143 111.143 68.0759L111.465 69.7733C105.858 70.8082 100.711 71.5465 97.6892 71.6983C97.4608 71.719 97.2393 71.7294 97.0108 71.7294Z"
      fill="#172B4D"
    />
    <path
      d="M181 152.889H0V143.502C0.000916934 142.084 0.566458 140.725 1.5724 139.722C2.57835 138.719 3.94244 138.156 5.36507 138.155H175.635C177.058 138.156 178.422 138.719 179.428 139.722C180.434 140.725 180.999 142.084 181 143.502V152.889ZM2.42293 150.474H178.577V143.502C178.576 142.725 178.266 141.979 177.714 141.43C177.163 140.88 176.415 140.571 175.635 140.57H5.36507C4.58504 140.571 3.83723 140.88 3.28568 141.43C2.73412 141.979 2.42385 142.725 2.42293 143.502V150.474Z"
      fill="#172B4D"
    />
    <circle cx="92" cy="95" r="16" fill="#E6E8FE" />
    <path
      d="M92.12 112.073C88.7655 112.073 85.4863 111.082 82.6972 109.224C79.908 107.367 77.7342 104.726 76.4505 101.638C75.1668 98.5486 74.8309 95.1496 75.4853 91.8704C76.1397 88.5912 77.7551 85.5791 80.127 83.2149C82.499 80.8508 85.5211 79.2408 88.8111 78.5885C92.1011 77.9362 95.5113 78.271 98.6105 79.5505C101.71 80.8299 104.358 82.9967 106.222 85.7766C108.086 88.5566 109.08 91.8249 109.08 95.1684C109.076 99.6504 107.288 103.947 104.108 107.117C100.928 110.286 96.6168 112.068 92.12 112.073ZM92.12 79.9887C89.1078 79.9887 86.1632 80.8789 83.6587 82.5469C81.1542 84.2149 79.2021 86.5856 78.0494 89.3593C76.8967 92.1331 76.5951 95.1852 77.1827 98.1298C77.7704 101.074 79.2209 103.779 81.3508 105.902C83.4808 108.025 86.1945 109.471 89.1488 110.056C92.1031 110.642 95.1653 110.342 97.9482 109.193C100.731 108.044 103.11 106.098 104.783 103.602C106.457 101.105 107.35 98.1706 107.35 95.1684C107.346 91.1433 105.741 87.284 102.885 84.4375C100.03 81.591 96.1584 79.9898 92.12 79.9852V79.9887Z"
      fill="#172B4D"
    />
    <path
      d="M98.8874 97.6119L87.4929 90.2816C86.8719 89.8821 86.0431 90.0607 85.6418 90.6805C85.2404 91.3003 85.4185 92.1266 86.0395 92.5261L97.4339 99.8564C98.0549 100.256 98.8837 100.077 99.2851 99.4575C99.6865 98.8377 99.5084 98.0114 98.8874 97.6119Z"
      fill="#0517F8"
    />
    <path
      d="M95.433 88.3351L87.526 99.6448C87.1459 100.189 87.2794 100.936 87.8244 101.315C88.3694 101.693 89.1194 101.559 89.4996 101.015L97.4065 89.7058C97.7867 89.162 97.6531 88.4143 97.1082 88.0358C96.5632 87.6573 95.8132 87.7913 95.433 88.3351Z"
      fill="#0517F8"
    />
    <path
      d="M119.252 23.4698C119.207 23.4731 119.161 23.4731 119.116 23.4698L110.658 22.3176C110.524 22.3049 110.394 22.2653 110.276 22.201C110.157 22.1368 110.053 22.0493 109.97 21.9438C109.886 21.8383 109.824 21.717 109.789 21.5871C109.753 21.4573 109.744 21.3216 109.763 21.1882C109.781 21.0548 109.826 20.9265 109.895 20.8109C109.964 20.6953 110.055 20.5949 110.164 20.5156C110.273 20.4364 110.397 20.38 110.528 20.3497C110.659 20.3195 110.795 20.3161 110.928 20.3397L119.385 21.4918C119.65 21.5095 119.897 21.6317 120.072 21.8315C120.247 22.0314 120.335 22.2925 120.318 22.5574C120.3 22.8223 120.178 23.0694 119.978 23.2442C119.778 23.4191 119.517 23.5074 119.252 23.4897V23.4698Z"
      fill="#172B4D"
    />
    <path
      d="M123.99 14.1827C123.855 14.1827 123.722 14.1552 123.597 14.102C123.473 14.0487 123.361 13.9709 123.268 13.8731L117.374 7.69959C117.275 7.60677 117.196 7.49468 117.141 7.37018C117.087 7.24569 117.058 7.1114 117.058 6.97554C117.057 6.83968 117.084 6.70509 117.137 6.58002C117.19 6.45494 117.268 6.342 117.366 6.24811C117.464 6.15422 117.581 6.08135 117.708 6.03395C117.835 5.98655 117.971 5.96563 118.107 5.97245C118.242 5.97928 118.375 6.01372 118.497 6.07365C118.619 6.13359 118.728 6.21777 118.816 6.32105L124.713 12.4945C124.848 12.636 124.938 12.814 124.973 13.0064C125.008 13.1988 124.985 13.3971 124.908 13.5769C124.831 13.7566 124.703 13.9098 124.54 14.0175C124.377 14.1252 124.186 14.1826 123.99 14.1827Z"
      fill="#172B4D"
    />
    <path
      d="M118.906 17.0863C118.747 17.0855 118.591 17.0479 118.45 16.9764L110.861 13.0639C110.633 12.9382 110.464 12.7286 110.388 12.4798C110.313 12.231 110.337 11.9625 110.456 11.7313C110.575 11.5002 110.78 11.3247 111.027 11.2421C111.273 11.1596 111.542 11.1764 111.777 11.2891L119.365 15.1983C119.565 15.3006 119.725 15.4674 119.818 15.6715C119.912 15.8757 119.934 16.1054 119.88 16.3236C119.827 16.5417 119.702 16.7357 119.526 16.874C119.349 17.0124 119.13 17.0872 118.906 17.0863Z"
      fill="#172B4D"
    />
    <path
      d="M176.552 59.1587L204.119 85.3144C204.502 85.6752 204.81 86.1081 205.025 86.5882C205.24 87.0683 205.359 87.5862 205.373 88.1122C205.388 88.6381 205.298 89.1618 205.11 89.6531C204.922 90.1444 204.638 90.5936 204.276 90.975L202.897 92.4302C202.536 92.8131 202.103 93.121 201.623 93.3363C201.143 93.5515 200.625 93.6698 200.099 93.6843C199.573 93.6988 199.05 93.6093 198.558 93.421C198.067 93.2326 197.618 92.9491 197.236 92.5867L169.599 66.4943L176.552 59.1587Z"
      fill="#CDD1FE"
    />
    <path
      d="M199.99 94.8275C198.672 94.8318 197.403 94.3273 196.447 93.419L167.974 66.5274L176.505 57.5103L204.921 84.4817C205.913 85.4241 206.491 86.721 206.528 88.0883C206.565 89.4556 206.06 90.7821 205.121 91.7774L203.743 93.2325C203.278 93.7253 202.719 94.1209 202.1 94.3964C201.481 94.6719 200.814 94.8218 200.137 94.8375L199.99 94.8275ZM171.27 66.4342L198.049 91.7407C198.595 92.2514 199.321 92.5266 200.068 92.5066C200.816 92.4867 201.526 92.1733 202.044 91.6342L203.42 90.1791C203.934 89.6341 204.211 88.9075 204.191 88.1585C204.17 87.4094 203.853 86.6991 203.31 86.1833L176.588 60.8301L171.27 66.4342Z"
      fill="#172B4D"
    />
    <path
      d="M176.552 59.1584L169.622 66.484C169.261 66.8667 168.828 67.1744 168.348 67.3893C167.868 67.6042 167.35 67.7222 166.824 67.7364C166.298 67.7506 165.774 67.6609 165.283 67.4722C164.792 67.2836 164.343 66.9998 163.962 66.6371C163.579 66.2761 163.271 65.843 163.056 65.3628C162.841 64.8825 162.723 64.3646 162.709 63.8386C162.695 63.3127 162.785 62.7891 162.973 62.2979C163.162 61.8067 163.446 61.3577 163.808 60.9765L170.738 53.6509C171.099 53.2681 171.532 52.9605 172.012 52.7456C172.492 52.5306 173.01 52.4127 173.536 52.3984C174.062 52.3842 174.586 52.474 175.077 52.6626C175.568 52.8513 176.017 53.1351 176.399 53.4977C176.781 53.8587 177.089 54.2918 177.304 54.7721C177.519 55.2523 177.637 55.7703 177.651 56.2962C177.665 56.8222 177.575 57.3458 177.387 57.8369C177.198 58.3281 176.914 58.7772 176.552 59.1584Z"
      fill="white"
    />
    <path
      d="M166.719 68.8914C165.71 68.8922 164.723 68.597 163.88 68.0426C163.037 67.4881 162.375 66.6986 161.977 65.7719C161.578 64.8452 161.46 63.8219 161.637 62.8286C161.814 61.8354 162.278 60.9159 162.973 60.184L169.902 52.8584C170.843 51.8636 172.14 51.2833 173.509 51.2452C174.878 51.2071 176.206 51.7143 177.201 52.6553C178.196 53.5962 178.776 54.8939 178.814 56.2627C178.852 57.6315 178.345 58.9594 177.404 59.9542L170.475 67.2798C169.992 67.791 169.41 68.1977 168.764 68.4749C168.118 68.7521 167.422 68.8939 166.719 68.8914ZM173.651 53.5743H173.571C173.2 53.5824 172.834 53.6644 172.494 53.8153C172.155 53.9662 171.848 54.1831 171.593 54.4534L164.664 61.779C164.402 62.0474 164.196 62.3654 164.057 62.7144C163.919 63.0633 163.852 63.4363 163.859 63.8116C163.866 64.1869 163.948 64.557 164.1 64.9003C164.251 65.2437 164.47 65.5534 164.742 65.8116C165.015 66.0697 165.336 66.2711 165.687 66.4039C166.038 66.5368 166.412 66.5985 166.787 66.5854C167.162 66.5724 167.531 66.4849 167.872 66.328C168.213 66.1711 168.519 65.9479 168.773 65.6715L175.706 58.3459C176.088 57.9452 176.343 57.441 176.441 56.8962C176.539 56.3513 176.474 55.7897 176.255 55.2813C176.036 54.7729 175.673 54.34 175.21 54.0365C174.747 53.733 174.205 53.5723 173.651 53.5743Z"
      fill="#172B4D"
    />
    <path
      d="M169.57 60.5705C180.721 49.4198 180.721 31.3409 169.57 20.1902C158.42 9.03955 140.341 9.03955 129.19 20.1903C118.039 31.3409 118.039 49.4198 129.19 60.5705C140.341 71.7212 158.42 71.7212 169.57 60.5705Z"
      fill="#CDD1FE"
    />
    <path
      d="M149.374 70.0839C142.539 70.0773 135.916 67.7134 130.622 63.3911C125.328 59.0687 121.687 53.0526 120.313 46.3577C118.94 39.6627 119.917 32.6989 123.081 26.641C126.245 20.5831 131.402 15.802 137.682 13.1044C143.961 10.4068 150.979 9.95785 157.551 11.8332C164.123 13.7086 169.847 17.7936 173.757 23.3989C177.668 29.0042 179.525 35.7868 179.015 42.6022C178.506 49.4176 175.661 55.8487 170.961 60.8104C168.186 63.7478 164.839 66.0859 161.127 67.6808C157.414 69.2757 153.414 70.0935 149.374 70.0839ZM149.374 13.0076C143.012 13.0122 136.85 15.2336 131.949 19.2899C127.048 23.3461 123.713 28.9835 122.519 35.2324C121.325 41.4814 122.345 47.9511 125.405 53.5289C128.465 59.1067 133.373 63.4438 139.285 65.7941C145.197 68.1445 151.744 68.3612 157.798 66.4069C163.852 64.4527 169.037 60.4497 172.459 55.0865C175.881 49.7233 177.327 43.3352 176.549 37.021C175.771 30.7068 172.816 24.8612 168.194 20.4897C163.115 15.6772 156.383 12.9977 149.387 13.0042L149.374 13.0076Z"
      fill="#172B4D"
    />
    <path
      d="M164.366 55.1863C172.643 46.9094 172.643 33.49 164.366 25.2131C156.089 16.9362 142.67 16.9362 134.393 25.2131C126.116 33.49 126.116 46.9094 134.393 55.1863C142.67 63.4632 156.089 63.4632 164.366 55.1863Z"
      fill="white"
    />
    <path
      d="M149.364 62.5617C145.713 62.5589 142.119 61.6624 138.895 59.9506C135.671 58.2387 132.915 55.7636 130.868 52.7412C128.821 49.7189 127.545 46.2414 127.151 42.6123C126.758 38.9833 127.259 35.3131 128.61 31.9222C129.962 28.5314 132.124 25.523 134.906 23.1599C137.688 20.7968 141.006 19.1508 144.571 18.3656C148.136 17.5805 151.839 17.6801 155.357 18.6557C158.874 19.6313 162.099 21.4533 164.751 23.9625C167.962 27.0026 170.2 30.9268 171.181 35.2388C172.163 39.5507 171.843 44.0568 170.264 48.1873C168.684 52.3178 165.915 55.8871 162.307 58.444C158.699 61.0008 154.413 62.4303 149.993 62.5517L149.364 62.5617ZM149.394 20.1698C149.207 20.1698 149.017 20.1698 148.831 20.1698C144.199 20.2973 139.754 22.0269 136.254 25.064C132.755 28.101 130.416 32.2577 129.638 36.8256C128.859 41.3935 129.688 46.09 131.984 50.115C134.28 54.14 137.901 57.2444 142.229 58.8992C146.557 60.554 151.325 60.6568 155.721 59.1902C160.117 57.7235 163.868 54.7782 166.335 50.8559C168.802 46.9337 169.833 42.2772 169.252 37.68C168.672 33.0827 166.515 28.8292 163.149 25.644C159.439 22.1169 154.512 20.1561 149.394 20.1698Z"
      fill="#172B4D"
    />
  </svg>
);

export const AscendingSortIcon = ({ strokeColor, ...props }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10.5015 1.7478V11.6686"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.7478 11.6687H6.99999"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.91504 9.33427H7.00007"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.08228 7.00004H7.00016"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.89917 4.66581H7.00005"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.04248 3.20674L10.5014 1.7478L11.9604 3.20674"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DescendingSortIcon = ({ strokeColor, ...props }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10.5015 12.2521V2.3313"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.7478 2.33134H6.99999"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.91504 4.66581H7.00007"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.08228 7.00004H7.00016"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.89917 9.33427H7.00005"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.5015 12.2522L11.9604 10.7932"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.04248 10.7932L10.5014 12.2522"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const AtoZSortIcon = ({ strokeColor, ...props }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9.56372 8.45825H12.0201L9.56372 11.3749H12.0201"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.2499 5.54167L10.7916 2.625L9.33325 5.54167"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.57764 5.05265H12.0055"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 11.3749H1.75"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 8.45817H1.75"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 5.54167H1.75"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 2.62492H1.75"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ZtoASortIcon = ({ strokeColor, ...props }) => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M9.56372 2.625H12.0201L9.56372 5.54167H12.0201"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.2499 11.9582L10.7916 9.0415L9.33325 11.9582"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.57764 11.4692H12.0055"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 11.3749H1.75"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 8.45817H1.75"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 5.54167H1.75"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 2.62492H1.75"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SortIcon = ({ strokeColor, ...props }) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M5 4L3.5 2.5L2 4"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.5 9.5V2.5"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7 8L8.5 9.5L10 8"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.5 2.5V9.5"
      stroke={strokeColor || "#172B4D"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const TableStatusIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle
      cx="8"
      cy="8"
      r="6"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11 6.5L8.37467 9.12533L6.87467 7.62533L5 9.5"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.125 6.5H10.9997V8.37467"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LaptopWithFeatherIcon = ({ ...props }) => (
  <svg
    width="200"
    height="158"
    viewBox="0 0 200 158"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_4569_162)">
      <path
        d="M178.034 36.1904H20.9656C18.6716 36.1904 16.812 38.044 16.812 40.3306V132.314C16.812 134.601 18.6716 136.455 20.9656 136.455H178.034C180.328 136.455 182.188 134.601 182.188 132.314V40.3306C182.188 38.044 180.328 36.1904 178.034 36.1904Z"
        fill="#E8EAED"
      />
      <path
        d="M178.034 137.659H20.9657C19.543 137.658 18.1789 137.094 17.173 136.091C16.167 135.089 15.6015 133.729 15.6006 132.311V40.3477C15.6015 38.9297 16.167 37.57 17.173 36.5673C18.1789 35.5646 19.543 35.0009 20.9657 35H178.034C179.457 35.0009 180.821 35.5646 181.827 36.5673C182.833 37.57 183.398 38.9297 183.399 40.3477V132.328C183.394 133.743 182.826 135.098 181.821 136.097C180.815 137.096 179.454 137.658 178.034 137.659ZM20.9657 37.3978C20.1856 37.3988 19.4378 37.708 18.8863 38.2578C18.3347 38.8076 18.0244 39.553 18.0235 40.3304V132.311C18.0244 133.088 18.3347 133.834 18.8863 134.384C19.4378 134.933 20.1856 135.243 20.9657 135.243H178.034C178.814 135.243 179.562 134.933 180.113 134.384C180.665 133.834 180.975 133.088 180.976 132.311V40.3477C180.975 39.5702 180.665 38.8248 180.113 38.275C179.562 37.7253 178.814 37.416 178.034 37.4151L20.9657 37.3978Z"
        fill="#172B4D"
      />
      <path
        d="M30.2562 48.5766C31.5178 48.5766 32.5406 47.5571 32.5406 46.2995C32.5406 45.0419 31.5178 44.0225 30.2562 44.0225C28.9945 44.0225 27.9717 45.0419 27.9717 46.2995C27.9717 47.5571 28.9945 48.5766 30.2562 48.5766Z"
        fill="#0517F8"
      />
      <path
        d="M38.1824 48.5766C39.4441 48.5766 40.4669 47.5571 40.4669 46.2995C40.4669 45.0419 39.4441 44.0225 38.1824 44.0225C36.9207 44.0225 35.8979 45.0419 35.8979 46.2995C35.8979 47.5571 36.9207 48.5766 38.1824 48.5766Z"
        fill="#0517F8"
      />
      <path
        d="M46.6629 48.5766C47.9246 48.5766 48.9474 47.5571 48.9474 46.2995C48.9474 45.0419 47.9246 44.0225 46.6629 44.0225C45.4012 44.0225 44.3784 45.0419 44.3784 46.2995C44.3784 47.5571 45.4012 48.5766 46.6629 48.5766Z"
        fill="#0517F8"
      />
      <path
        d="M190 157H9V147.612C9.00092 146.194 9.56646 144.835 10.5724 143.832C11.5784 142.829 12.9424 142.266 14.3651 142.265H184.635C186.058 142.266 187.422 142.829 188.428 143.832C189.434 144.835 189.999 146.194 190 147.612V157ZM11.4229 154.585H187.577V147.612C187.576 146.835 187.266 146.089 186.714 145.54C186.163 144.99 185.415 144.681 184.635 144.68H14.3651C13.585 144.681 12.8372 144.99 12.2857 145.54C11.7341 146.089 11.4238 146.835 11.4229 147.612V154.585Z"
        fill="#172B4D"
      />
      <path
        d="M100.033 97.779C94.4764 97.7755 89.1407 95.6099 85.1621 91.7434C81.1835 87.8769 78.8764 82.615 78.732 77.0782C78.6245 72.8767 79.7694 68.7378 82.0219 65.1849C84.2744 61.632 87.5334 58.8247 91.3866 57.118C95.2398 55.4113 99.5142 54.8819 103.669 55.5967C107.824 56.3116 111.674 58.2385 114.73 61.1339C117.786 64.0293 119.912 67.7631 120.84 71.8631C121.767 75.963 121.454 80.245 119.94 84.1675C118.425 88.09 115.778 91.4769 112.333 93.8997C108.887 96.3225 104.799 97.6725 100.583 97.779H100.033ZM80.8088 77.0058C80.9059 80.7983 82.1289 84.477 84.3234 87.5767C86.5178 90.6764 89.5851 93.0579 93.1373 94.4201C96.6895 95.7823 100.567 96.064 104.28 95.2297C107.993 94.3953 111.374 92.4823 113.997 89.7326C116.619 86.9828 118.364 83.5198 119.012 79.7814C119.66 76.043 119.181 72.1971 117.636 68.73C116.091 65.2628 113.549 62.3302 110.332 60.3028C107.115 58.2754 103.367 57.2444 99.5623 57.34C97.0299 57.3858 94.5319 57.9329 92.2135 58.9495C89.8951 59.9661 87.8026 61.432 86.0576 63.2618C84.3126 65.0917 82.95 67.2491 82.0489 69.6085C81.1478 71.968 80.7263 74.4825 80.8088 77.0058Z"
        fill="#172B4D"
      />
      <path
        d="M100.05 97.7818C92.3384 97.7818 85.8588 86.3964 85.6269 76.8844C85.3915 67.3413 91.3381 55.5867 99.0811 55.3038C101.594 55.2141 104.18 56.3388 106.537 58.54C110.87 62.6008 113.892 69.6839 114.058 76.1702C114.224 82.6564 111.566 89.8948 107.44 94.1695C105.197 96.4983 102.677 97.7473 100.171 97.7818H100.05ZM87.7037 76.8326C87.8954 81.4017 89.2819 85.8416 91.7257 89.7119C94.1902 93.5657 97.2639 95.7566 100.14 95.7117C102.096 95.6841 104.107 94.656 105.959 92.7342C109.652 88.8943 112.148 82.0941 111.985 76.2288C111.822 70.3636 109.008 63.7014 105.114 60.0512C103.169 58.2295 101.113 57.2911 99.1572 57.3773C96.2739 57.4808 93.3214 59.8166 91.0577 63.7842C88.8193 67.7698 87.6632 72.2686 87.7037 76.8361V76.8326Z"
        fill="#172B4D"
      />
      <path
        d="M86.3156 91.7266L85.1733 90.0016C89.4247 87.2144 94.3626 85.645 99.4479 85.4646C104.995 85.2995 110.467 86.7762 115.173 89.7083L114.069 91.461C109.717 88.7526 104.659 87.3866 99.5309 87.5347C94.8236 87.6982 90.2519 89.1483 86.3156 91.7266Z"
        fill="#172B4D"
      />
      <path
        d="M98.7696 67.6247C93.833 67.6179 88.9872 66.3008 84.7305 63.8088L85.7689 62.0217C89.8264 64.3994 94.4614 65.6203 99.1677 65.5512C104.312 65.4519 109.311 63.8334 113.532 60.9004L114.726 62.5944C110.173 65.7578 104.782 67.5069 99.2335 67.6212L98.7696 67.6247Z"
        fill="#172B4D"
      />
      <path
        d="M100.155 56.3191L98.0784 56.3711L99.0974 96.7766L101.174 96.7246L100.155 56.3191Z"
        fill="#172B4D"
      />
      <path
        d="M120.301 74.97L79.7471 75.9932L79.7996 78.0626L120.353 77.0394L120.301 74.97Z"
        fill="#172B4D"
      />
      <path
        d="M51.5986 112.738L148.976 112.853L51.5986 112.738Z"
        fill="white"
      />
      <path
        d="M148.976 114.06L51.5987 113.946C51.2774 113.946 50.9692 113.819 50.742 113.593C50.5148 113.366 50.3872 113.059 50.3872 112.739C50.3872 112.419 50.5148 112.111 50.742 111.885C50.9692 111.658 51.2774 111.531 51.5987 111.531L148.976 111.645C149.298 111.645 149.606 111.772 149.833 111.999C150.06 112.225 150.188 112.532 150.188 112.853C150.188 113.173 150.06 113.48 149.833 113.707C149.606 113.933 149.298 114.06 148.976 114.06Z"
        fill="#172B4D"
      />
      <path
        d="M133.235 93.1323C129.789 88.2321 127.669 82.524 127.079 76.5623C125.967 64.7836 131.382 56.1227 133.121 53.5799L138.74 57.5085C139.292 48.9275 142.023 40.6289 146.674 33.3966C148.475 35.2443 150.275 37.1 152.075 38.9638C156.616 16.3486 168.554 1.3584 180.267 1.21289C182.623 1.18518 186.036 1.73254 190.193 4.58716"
        fill="#CDD1FE"
      />
      <path
        d="M132.241 93.825C128.673 88.7484 126.479 82.8347 125.873 76.6592C124.834 65.504 129.338 56.9748 132.109 52.8834L132.802 51.8684L137.704 55.2946C138.55 47.2523 141.271 39.5206 145.648 32.721L146.472 31.4357L151.357 36.4728C156.242 15.0355 167.934 0.152668 180.25 0.000236676C183.763 -0.0447998 187.341 1.16426 190.885 3.58583L189.5 5.58822C186.382 3.45419 183.264 2.3837 180.271 2.42528C168.866 2.56731 157.503 18.0148 153.249 39.1854L152.795 41.4442L146.888 35.3365C142.84 42.09 140.465 49.7131 139.96 57.5707L139.821 59.7186L133.46 55.2738C130.873 59.3964 127.394 66.8898 128.295 76.434C128.863 82.1915 130.911 87.7041 134.24 92.4358L132.241 93.825Z"
        fill="#172B4D"
      />
      <path
        d="M132.338 92.5366C138.218 93.7216 144.305 93.3635 150.006 91.4973C161.241 87.7835 166.981 79.3478 168.623 76.7288L162.769 73.1605C167.193 70.8598 171.256 67.9206 174.825 64.4373C177.381 61.9425 179.665 59.1833 181.639 56.206L174.364 53.5004C193.373 40.4398 202.2 23.3606 197.575 12.5206C196.636 10.3208 194.634 7.18209 189.839 4.37598"
        fill="#CDD1FE"
      />
      <path
        d="M139.267 94.4348C136.858 94.4406 134.455 94.2085 132.092 93.742L132.567 91.3654C138.237 92.5114 144.108 92.1701 149.608 90.3746C158.709 87.3641 164.158 81.1699 166.905 77.1408L160.278 73.0979L162.187 72.1037C169.18 68.4836 175.205 63.2413 179.758 56.8155L171.759 53.8362L173.681 52.5163C191.447 40.3114 201.032 23.6964 196.47 13.0228C195.212 10.0711 192.777 7.52138 189.236 5.4497L190.463 3.37109C194.461 5.71299 197.236 8.64383 198.701 12.0839C201.126 17.7724 200.132 25.1272 195.902 32.7972C191.883 40.0724 185.356 47.105 176.945 53.2057L183.492 55.6307L182.647 56.9056C178.179 63.6437 172.194 69.2414 165.173 73.2504L170.279 76.3683L169.628 77.4076C167.002 81.6029 161.005 89.1656 150.366 92.6819C146.784 93.8553 143.036 94.4472 139.267 94.4348Z"
        fill="#172B4D"
      />
      <path d="M177 24.251L120.133 112.661L177 24.251Z" fill="white" />
      <path
        d="M120.133 113.873C119.916 113.872 119.702 113.812 119.516 113.701C119.329 113.589 119.175 113.429 119.071 113.238C118.967 113.047 118.916 112.832 118.924 112.614C118.932 112.397 118.998 112.185 119.115 112.002L175.989 23.5989C176.166 23.3353 176.439 23.1514 176.749 23.0869C177.06 23.0223 177.384 23.0822 177.651 23.2536C177.918 23.425 178.107 23.6943 178.178 24.0038C178.249 24.3132 178.195 24.638 178.029 24.9085L121.155 113.315C121.045 113.486 120.894 113.627 120.715 113.724C120.537 113.822 120.337 113.873 120.133 113.873Z"
        fill="#172B4D"
      />
    </g>
    <defs>
      <clipPath id="clip0_4569_162">
        <rect width="200" height="157.666" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const TableInventoryNameIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_3218_223467)">
      <path
        d="M14 11.0813V4.91883C13.9995 4.82981 13.9756 4.7425 13.9306 4.66569C13.8856 4.58888 13.8212 4.52529 13.7437 4.48133L8.24375 1.38758C8.16964 1.34479 8.08557 1.32227 8 1.32227C7.91443 1.32227 7.83036 1.34479 7.75625 1.38758L2.25625 4.48133C2.17884 4.52529 2.1144 4.58888 2.06941 4.66569C2.02442 4.7425 2.00048 4.82981 2 4.91883V11.0813C2.00048 11.1703 2.02442 11.2577 2.06941 11.3345C2.1144 11.4113 2.17884 11.4749 2.25625 11.5188L7.75625 14.6126C7.83036 14.6554 7.91443 14.6779 8 14.6779C8.08557 14.6779 8.16964 14.6554 8.24375 14.6126L13.7437 11.5188C13.8212 11.4749 13.8856 11.4113 13.9306 11.3345C13.9756 11.2577 13.9995 11.1703 14 11.0813V11.0813Z"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.0625 9.53125V6.28125L5 2.9375"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.9328 4.66211L8.05781 7.99961L2.07031 4.66211"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.05625 8L8 14.675"
        stroke="#748094"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_3218_223467">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const SixDotsVertical = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_4193_246705)">
      <circle cx="6" cy="4" r="1" fill="#748094" />
      <circle cx="6" cy="8" r="1" fill="#748094" />
      <circle cx="6" cy="12" r="1" fill="#748094" />
      <circle cx="10" cy="4" r="1" fill="#748094" />
      <circle cx="10" cy="8" r="1" fill="#748094" />
      <circle cx="10" cy="12" r="1" fill="#748094" />
    </g>
    <defs>
      <clipPath id="clip0_4193_246705">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const CustomFieldColumnHeadIcon = ({ strokeColor, ...props }) => (
  <svg
    width="18"
    height="16"
    viewBox="0 0 18 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <rect
      x="1.26797"
      y="3.6"
      width="15.4667"
      height="8.8"
      rx="2.4"
      stroke="#748094"
      strokeWidth="1.2"
    />
    <rect
      x="4"
      y="7.16602"
      width="5.83333"
      height="1.2"
      rx="0.6"
      fill="#748094"
    />
    <path
      d="M11.5 7.16602L12.622 8.51237C12.6886 8.59233 12.8114 8.59233 12.878 8.51237L14 7.16602"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
    />
  </svg>
);

export const RadioChecked = ({
  strokeColor,
  fillColor = strokeColor,
  ...props
}) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M7.9998 0.799805C11.9763 0.799805 15.1998 4.02335 15.1998 7.9998C15.1998 11.9763 11.9763 15.1998 7.9998 15.1998C4.02335 15.1998 0.799805 11.9763 0.799805 7.9998C0.799805 4.02335 4.02335 0.799805 7.9998 0.799805ZM7.9998 2.1089C4.74635 2.1089 2.1089 4.74635 2.1089 7.9998C2.1089 11.2533 4.74635 13.8907 7.9998 13.8907C11.2533 13.8907 13.8907 11.2533 13.8907 7.9998C13.8907 4.74635 11.2533 2.1089 7.9998 2.1089ZM7.9998 3.41799C10.5303 3.41799 12.5816 5.46934 12.5816 7.9998C12.5816 10.5303 10.5303 12.5816 7.9998 12.5816C5.46934 12.5816 3.41799 10.5303 3.41799 7.9998C3.41799 5.46934 5.46934 3.41799 7.9998 3.41799Z"
      fill={fillColor || "#748094"}
    />
  </svg>
);

export const ChatBubbles = ({ strokeColor = "#0517F8", ...props }) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_7010_59796)">
      <path
        d="M3.35625 6.75L1.5 8.25V2.25C1.5 2.15054 1.53951 2.05516 1.60984 1.98484C1.68016 1.91451 1.77554 1.875 1.875 1.875H7.875C7.97446 1.875 8.06984 1.91451 8.14017 1.98484C8.21049 2.05516 8.25 2.15054 8.25 2.25V6.375C8.25 6.47446 8.21049 6.56984 8.14017 6.64017C8.06984 6.71049 7.97446 6.75 7.875 6.75H3.35625Z"
        stroke={strokeColor}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.75 6.75V8.625C3.75 8.72446 3.78951 8.81984 3.85984 8.89017C3.93016 8.96049 4.02554 9 4.125 9H8.64375L10.5 10.5V4.5C10.5 4.40054 10.4605 4.30516 10.3902 4.23484C10.3198 4.16451 10.2245 4.125 10.125 4.125H8.25"
        stroke={strokeColor}
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7010_59796">
        <rect width="12" height="12" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const LinkIcon = ({ strokeColor, ...props }) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M5.5626 6.37967C5.31298 6.16174 4.93395 6.18744 4.71601 6.43706C4.49808 6.68668 4.52377 7.06571 4.7734 7.28365L5.5626 6.37967ZM8.49755 6.83166L8.89215 7.28365C8.90235 7.27474 8.91224 7.2655 8.92181 7.25592L8.49755 6.83166ZM5.58024 2.24139C5.34346 2.47321 5.33943 2.85309 5.57125 3.08987C5.80307 3.32665 6.18295 3.33068 6.41973 3.09885L5.58024 2.24139ZM6.43737 5.61966C6.68699 5.83759 7.06602 5.8119 7.28395 5.56227C7.50188 5.31265 7.47619 4.93362 7.22657 4.71569L6.43737 5.61966ZM3.50241 5.16767L3.10781 4.71569C3.09761 4.72459 3.08772 4.73384 3.07814 4.74341L3.50241 5.16767ZM6.41973 9.75797C6.65651 9.52615 6.66053 9.14627 6.42871 8.90949C6.19689 8.6727 5.81701 8.66868 5.58023 8.9005L6.41973 9.75797ZM4.7734 7.28365C5.95315 8.31361 7.7124 8.31361 8.89215 7.28365L8.10295 6.37967C7.37531 7.01493 6.29024 7.01493 5.5626 6.37967L4.7734 7.28365ZM8.92181 7.25592L9.7538 6.42394L8.90527 5.57541L8.07329 6.4074L8.92181 7.25592ZM9.75827 6.41942C10.8912 5.26219 10.8814 3.40856 9.73626 2.26339L8.88774 3.11192C9.56778 3.79196 9.57361 4.89272 8.9008 5.57993L9.75827 6.41942ZM9.73626 2.26339C8.5911 1.11823 6.73747 1.10841 5.58024 2.24139L6.41973 3.09885C7.10694 2.42605 8.20769 2.43188 8.88774 3.11192L9.73626 2.26339ZM7.22657 4.71569C6.04682 3.68572 4.28756 3.68572 3.10781 4.71569L3.89701 5.61966C4.62465 4.9844 5.70972 4.9844 6.43737 5.61966L7.22657 4.71569ZM3.07814 4.74341L2.24616 5.57543L3.0947 6.42394L3.92668 5.59193L3.07814 4.74341ZM2.24169 5.57994C1.10872 6.73716 1.11853 8.5908 2.2637 9.73596L3.11223 8.88743C2.43219 8.20739 2.42636 7.10664 3.09916 6.41943L2.24169 5.57994ZM2.2637 9.73596C3.40887 10.8811 5.2625 10.8909 6.41973 9.75797L5.58023 8.9005C4.89303 9.5733 3.79227 9.56748 3.11223 8.88743L2.2637 9.73596Z"
      fill="#0517F8"
    />
  </svg>
);

export const UnlinkIcon = ({ strokeColor = COLOR.$gray_v2_60, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8.88518 10.2565C8.63555 10.0386 8.25652 10.0643 8.03859 10.3139C7.82066 10.5635 7.84635 10.9425 8.09598 11.1605L8.88518 10.2565ZM12.93 10.7085L13.3246 11.1605C13.3348 11.1516 13.3447 11.1423 13.3542 11.1328L12.93 10.7085ZM13.6106 9.17943C13.3787 9.41621 13.3828 9.79609 13.6195 10.0279C13.8563 10.2597 14.2362 10.2557 14.468 10.0189L13.6106 9.17943ZM9.18014 4.73104C8.94336 4.96286 8.93934 5.34274 9.17116 5.57952C9.40298 5.8163 9.78286 5.82033 10.0196 5.5885L9.18014 4.73104ZM10.3146 8.94202C10.5642 9.15995 10.9433 9.13426 11.1612 8.88463C11.3791 8.63501 11.3534 8.25598 11.1038 8.03805L10.3146 8.94202ZM6.26982 8.49003L5.87522 8.03805C5.86502 8.04695 5.85512 8.0562 5.84555 8.06578L6.26982 8.49003ZM5.58924 10.0191C5.82106 9.78235 5.81703 9.40247 5.58025 9.17065C5.34347 8.93883 4.96359 8.94285 4.73177 9.17963L5.58924 10.0191ZM5.1605 9.59938L4.73621 9.1751L4.73177 9.17963L5.1605 9.59938ZM10.0197 14.4675C10.2564 14.2357 10.2605 13.8558 10.0286 13.619C9.79682 13.3823 9.41694 13.3782 9.18016 13.6101L10.0197 14.4675ZM8.09598 11.1605C9.59362 12.468 11.8269 12.468 13.3246 11.1605L12.5354 10.2565C11.4898 11.1693 9.93072 11.1693 8.88518 10.2565L8.09598 11.1605ZM13.3542 11.1328L14.4636 10.0234L13.615 9.17491L12.5057 10.2842L13.3542 11.1328ZM14.468 10.0189C15.902 8.55429 15.8895 6.20826 14.4402 4.75889L13.5916 5.60742C14.5759 6.59166 14.5843 8.18481 13.6106 9.17943L14.468 10.0189ZM14.4402 4.75889C12.9908 3.30952 10.6448 3.2971 9.18014 4.73104L10.0196 5.5885C11.0142 4.61474 12.6074 4.62317 13.5916 5.60742L14.4402 4.75889ZM11.1038 8.03805C9.60618 6.73055 7.37286 6.73055 5.87522 8.03805L6.66442 8.94202C7.70996 8.02922 9.26908 8.02922 10.3146 8.94202L11.1038 8.03805ZM5.84555 8.06578L4.73623 9.17512L5.58477 10.0236L6.69409 8.91429L5.84555 8.06578ZM4.73177 9.17963C3.29783 10.6443 3.31025 12.9903 4.75962 14.4397L5.60815 13.5911C4.62391 12.6069 4.61547 11.0137 5.58924 10.0191L4.73177 9.17963ZM4.75962 14.4397C6.20899 15.889 8.55502 15.9015 10.0197 14.4675L9.18016 13.6101C8.18555 14.5838 6.59239 14.5754 5.60815 13.5911L4.75962 14.4397Z"
      fill={strokeColor}
    />
    <g clipPath="url(#clip0_7436_167620)">
      <path
        d="M5.1749 2.02539L2.0249 5.17539"
        stroke={strokeColor}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M5.1749 5.17539L2.0249 2.02539"
        stroke={strokeColor}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_7436_167620">
        <rect
          width="5.6"
          height="5.6"
          fill="white"
          transform="translate(0.800049 0.800781)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const EnvelopOutline = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.86333 4.2041H16.1358C16.8892 4.2041 17.5 4.81494 17.5 5.56827V14.4316C17.5 15.185 16.8892 15.795 16.1367 15.795H3.86333C3.11083 15.7958 2.5 15.185 2.5 14.4316V5.56827C2.5 4.81494 3.11083 4.2041 3.86333 4.2041Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.59912 5.06245L9.01077 9.64831C9.58994 10.0625 10.3683 10.0633 10.9483 9.64997L17.3966 5.05078"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EnvelopFilled = ({ fillColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.86333 4.2041H16.1358C16.8892 4.2041 17.5 4.81493 17.5 5.56827V14.4316C17.5 15.185 16.8892 15.795 16.1367 15.795H3.86333C3.11083 15.7958 2.5 15.185 2.5 14.4316V5.56827C2.5 4.81493 3.11083 4.2041 3.86333 4.2041Z"
      fill={fillColor || "#0517F8"}
      stroke={fillColor || "#0517F8"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.59918 5.06245L9.01083 9.64831C9.59 10.0625 10.3683 10.0633 10.9483 9.64997L17.3967 5.05078"
      fill={fillColor || "#0517F8"}
    />
    <path
      d="M2.59918 5.06245L9.01083 9.64831C9.59 10.0625 10.3683 10.0633 10.9483 9.64997L17.3967 5.05078"
      stroke="white"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChatBubbleSingle = ({ strokeColor, ...props }) => (
  <svg
    width="12"
    height="12"
    viewBox="0 0 12 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M4.5 10.4998L7 8.48926"
      stroke={strokeColor || "#0517F8"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.5 8.5V10.5"
      stroke={strokeColor || "#0517F8"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.5 8.5H4C2.6195 8.5 1.5 7.3805 1.5 6V4C1.5 2.6195 2.6195 1.5 4 1.5H8C9.3805 1.5 10.5 2.6195 10.5 4V6C10.5 7.3805 9.3805 8.5 8 8.5H7"
      stroke={strokeColor || "#0517F8"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const TimeTrackerIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M7.59766 4.66504V8.40126H10.6989"
      stroke={strokeColor || "#00B09B"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.99609 10.001H5.33082"
      stroke={strokeColor || "#00B09B"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.33091 12.0014H3.33008"
      stroke={strokeColor || "#00B09B"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.33101 14.0024H4.66406"
      stroke={strokeColor || "#00B09B"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.03711 7.33277C2.39637 4.12498 5.22854 1.77591 8.44746 2.01588C11.6664 2.25585 14.1189 4.99889 13.9986 8.2245C13.8782 11.4501 11.2281 14.0028 8.00026 14.0022"
      stroke={strokeColor || "#00B09B"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailInboxIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M11.3347 9.33386H4.66528"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.3347 11.3347H4.66528"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 1.99748V7.333"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.0009 5.33223L8 7.33306"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.99915 5.33223L8 7.33306"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.0024 8V11.3347C14.0024 12.8081 12.808 14.0025 11.3346 14.0025H4.66521C3.19184 14.0025 1.99744 12.8081 1.99744 11.3347V8"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailDraftsIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10.5 14H3.5C3.36739 14 3.24021 13.9473 3.14645 13.8536C3.05268 13.7598 3 13.6326 3 13.5V4.5C3 4.36739 3.05268 4.24021 3.14645 4.14645C3.24021 4.05268 3.36739 4 3.5 4H8.5L11 6.5V13.5C11 13.6326 10.9473 13.7598 10.8536 13.8536C10.7598 13.9473 10.6326 14 10.5 14Z"
      stroke={strokeColor || "#748094"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5 4V2.5C5 2.36739 5.05268 2.24021 5.14645 2.14645C5.24021 2.05268 5.36739 2 5.5 2H10.5L13 4.5V11.5C13 11.6326 12.9473 11.7598 12.8536 11.8536C12.7598 11.9473 12.6326 12 12.5 12H11"
      stroke={strokeColor || "#748094"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.5 9.5H8.5"
      stroke={strokeColor || "#748094"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.5 11.5H8.5"
      stroke={strokeColor || "#748094"}
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailSentIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M12.48 8.33793V3.86494"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.28 10.8979H2.87998C2.17278 10.8979 1.59998 10.3251 1.59998 9.61793V3.85791"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.1929 2.57792H2.88702C2.17598 2.57792 1.59998 3.15392 1.59998 3.86496C1.59998 4.29184 1.81182 4.69056 2.1651 4.92992L5.59358 7.25373C6.46718 7.84573 7.61407 7.84573 8.48767 7.25373L11.9155 4.93056C12.2681 4.6912 12.48 4.29184 12.48 3.8656C12.48 3.15456 11.904 2.57792 11.1929 2.57792Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.6499 9.088C14.6496 10.0877 14.6496 11.7088 13.6499 12.7085C12.6502 13.7082 11.0291 13.7082 10.0294 12.7085C9.02975 11.7088 9.02975 10.0877 10.0294 9.088C11.0291 8.08833 12.6502 8.08833 13.6499 9.088Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.8351 10.4L11.5903 11.6448L10.8441 10.8979"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailOutboxIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13.12 3.86495V8.33793"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.76 12.1779H9.92004"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.48 10.8979L13.76 12.1779L12.48 13.4579"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.36 10.8979H3.51999C2.81279 10.8979 2.23999 10.3251 2.23999 9.61794V3.86495"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M3.52703 2.57792H11.8336C12.544 2.57792 13.1206 3.15392 13.1206 3.86496C13.1206 4.29184 12.9088 4.69056 12.5555 4.92992L9.127 7.25373C8.2534 7.84573 7.10653 7.84573 6.23295 7.25373L2.80511 4.93056C2.45183 4.6912 2.23999 4.29184 2.23999 3.8656C2.23999 3.15456 2.81599 2.57792 3.52703 2.57792Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailSpamIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8.00106 8.74673V6.25339"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8.00039 10.75C7.90839 10.75 7.83373 10.8247 7.83439 10.9167C7.83439 11.0087 7.90906 11.0833 8.00106 11.0833C8.09306 11.0833 8.16773 11.0087 8.16773 10.9167C8.16773 10.8247 8.09306 10.75 8.00039 10.75"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.35194 2.75935L14.4579 11.6954C15.0513 12.734 14.3013 14.0267 13.1053 14.0267H2.89327C1.69661 14.0267 0.946605 12.734 1.54061 11.6954L6.64661 2.75935C7.24461 1.71202 8.75394 1.71202 9.35194 2.75935Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EnvelopOpenIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2 6.46867V12.6667C2 13.4033 2.59667 14 3.33333 14H12.666C13.4027 14 13.9993 13.4033 13.9993 12.6667V6.46867"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.59087 5.36266L7.25354 2.22599C7.70487 1.92266 8.29487 1.92266 8.7462 2.22599L13.4089 5.36266C14.1942 5.89133 14.1942 7.04667 13.4089 7.57533L9.49287 10.21C8.5902 10.8173 7.4102 10.8173 6.50754 10.21L2.59154 7.57533C1.80554 7.04667 1.80554 5.89066 2.59087 5.36266Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.08022 9.92L2.39355 13.6067"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.6066 13.6067L9.91992 9.92"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailReplyIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.16671 11.2847V13.8414C9.16671 14.5639 8.30921 14.9439 7.77337 14.4589L2.74837 9.90391C2.37504 9.56558 2.38504 8.97641 2.76921 8.65141L7.79421 4.38641C8.33587 3.92641 9.16671 4.31141 9.16671 5.02141V7.05225C13.7192 7.05225 17.5 9.86225 17.5 14.5372V15.4781C15.9609 12.4081 12.7609 11.2847 9.16671 11.2847Z"
      stroke={strokeColor || "#0517F8"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailReplyAllIcon = ({ strokeColor, ...props }) => (
  <svg
    width="21"
    height="21"
    viewBox="0 0 21 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.6921 11.2847V13.8414C11.6921 14.5639 10.8346 14.9439 10.2988 14.4589L5.27376 9.90391C4.90043 9.56558 4.91043 8.97641 5.2946 8.65141L10.3196 4.38641C10.8613 3.92641 11.6921 4.31141 11.6921 5.02141V7.05225C16.2446 7.05225 20.0254 9.86225 20.0254 14.5372V15.4781C18.4863 12.4081 15.2863 11.2847 11.6921 11.2847Z"
      stroke={strokeColor || "#0517F8"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.79876 14.4587L1.77376 9.90373C1.40043 9.5654 1.41043 8.97623 1.7946 8.65123L6.8196 4.38623"
      stroke={strokeColor || "#0517F8"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DocumentTypeIcon = ({ strokeColor = "#828B9D", ...props }) => (
  <svg
    width="41"
    height="41"
    viewBox="0 0 41 41"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M30.7061 11.1856L25.99 6.46945C25.3646 5.84407 24.5157 5.49219 23.6319 5.49219H11.6713C9.82851 5.49219 8.33594 6.98476 8.33594 8.82754V32.175C8.33594 34.0178 9.82851 35.5103 11.6713 35.5103H28.348C30.1908 35.5103 31.6834 34.0178 31.6834 32.175V13.5437C31.6834 12.6599 31.3315 11.811 30.7061 11.1856V11.1856Z"
      stroke={strokeColor}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M31.6821 13.8306H25.0114C24.0909 13.8306 23.3438 13.0834 23.3438 12.1629V5.49219"
      stroke={strokeColor}
      strokeWidth="1.87613"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const DownloadAttachmentIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10.0001 13.8345V7.16504"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.5011 11.3335L10.0001 13.8345L7.49902 11.3335"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.93994 2.99658C14.0838 2.99658 17.4431 6.35585 17.4431 10.4997C17.4431 14.6436 14.0838 18.0028 9.93994 18.0028"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.3765 17.5436C6.55406 17.2459 5.79002 16.8066 5.1189 16.2456"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.1189 4.7541C5.78961 4.19255 6.55376 3.75319 7.3765 3.45605"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.55688 9.1974C2.70753 8.33512 3.00862 7.50604 3.44642 6.74805"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.55688 11.8013C2.70743 12.6638 3.00853 13.4932 3.44642 14.2515"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const EmailFolderIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_11371_62971)">
      <path
        d="M2 5V12.5375C2 12.5982 2.01196 12.6584 2.03521 12.7145C2.05845 12.7706 2.09252 12.8216 2.13546 12.8645C2.17841 12.9075 2.2294 12.9416 2.28551 12.9648C2.34162 12.988 2.40176 13 2.4625 13H13.5562C13.6739 13 13.7868 12.9532 13.87 12.87C13.9532 12.7868 14 12.6739 14 12.5562V5.5C14 5.36739 13.9473 5.24021 13.8536 5.14645C13.7598 5.05268 13.6326 5 13.5 5H8"
        stroke={strokeColor || "#748094"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2 5V3.5C2 3.36739 2.05268 3.24022 2.14645 3.14645C2.24021 3.05268 2.36739 3 2.5 3H5.79375C5.85868 2.99978 5.92301 3.01236 5.98307 3.03702C6.04312 3.06169 6.09773 3.09796 6.14375 3.14375L8 5"
        stroke={strokeColor || "#748094"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_11371_62971">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const LeavingByDoorIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="120"
    height="114"
    viewBox="0 0 120 114"
    fill="none"
  >
    <g clipPath="url(#clip0_26295_40869)">
      <path
        d="M109.248 67.2417C109.106 67.2417 108.966 67.1975 108.85 67.1151C108.733 67.0327 108.645 66.9161 108.597 66.7815L106.651 61.2136C106.59 61.0409 106.6 60.8511 106.679 60.6858C106.758 60.5206 106.899 60.3934 107.072 60.3324C107.245 60.2714 107.434 60.2815 107.6 60.3604C107.765 60.4394 107.892 60.5808 107.953 60.7535L109.902 66.319C109.932 66.4048 109.945 66.4957 109.94 66.5865C109.935 66.6773 109.912 66.7662 109.872 66.8481C109.833 66.93 109.777 67.0033 109.709 67.0637C109.641 67.1241 109.562 67.1705 109.476 67.2002C109.403 67.2264 109.326 67.2404 109.248 67.2417Z"
        fill="#172B4D"
      />
      <path
        d="M116.454 67.2433C116.376 67.243 116.298 67.2298 116.224 67.2042C116.139 67.1742 116.06 67.1276 115.992 67.0671C115.924 67.0066 115.869 66.9333 115.83 66.8514C115.79 66.7696 115.768 66.6807 115.763 66.5901C115.758 66.4994 115.771 66.4086 115.801 66.323L117.747 60.7574C117.809 60.5847 117.936 60.4433 118.101 60.3644C118.266 60.2854 118.456 60.2753 118.629 60.3363C118.801 60.3973 118.943 60.5245 119.022 60.6897C119.101 60.855 119.111 61.0448 119.05 61.2175L117.099 66.7808C117.052 66.9148 116.965 67.031 116.85 67.1138C116.734 67.1965 116.596 67.2418 116.454 67.2433Z"
        fill="#172B4D"
      />
      <path
        d="M113.07 65.0234C112.887 65.0234 112.711 64.9507 112.582 64.8213C112.453 64.6918 112.38 64.5163 112.38 64.3332L112.403 58.4363C112.403 58.2533 112.476 58.0777 112.605 57.9483C112.735 57.8188 112.91 57.7461 113.093 57.7461C113.276 57.7461 113.452 57.8188 113.581 57.9483C113.711 58.0777 113.783 58.2533 113.783 58.4363L113.76 64.3355C113.76 64.5182 113.687 64.6932 113.557 64.8221C113.428 64.951 113.253 65.0234 113.07 65.0234Z"
        fill="#172B4D"
      />
      <path
        d="M107.879 84.4827C107.696 84.4949 107.516 84.4339 107.378 84.3131C107.24 84.1923 107.155 84.0215 107.143 83.8385C107.131 83.6554 107.192 83.475 107.313 83.3369C107.434 83.1989 107.604 83.1144 107.787 83.1022L110.926 82.6743C111.107 82.6496 111.291 82.698 111.437 82.8089C111.583 82.9198 111.679 83.084 111.703 83.2656C111.728 83.4471 111.68 83.631 111.569 83.7769C111.458 83.9227 111.293 84.0185 111.112 84.0432L107.974 84.4827C107.942 84.485 107.911 84.485 107.879 84.4827Z"
        fill="#172B4D"
      />
      <path
        d="M107.382 81.8505C107.23 81.8508 107.083 81.8011 106.962 81.7091C106.841 81.6171 106.754 81.4878 106.715 81.3414C106.675 81.195 106.684 81.0395 106.742 80.8991C106.799 80.7586 106.901 80.641 107.032 80.5644L109.77 78.9539C109.849 78.901 109.937 78.8648 110.03 78.8475C110.123 78.8303 110.219 78.8323 110.311 78.8535C110.403 78.8748 110.49 78.9147 110.566 78.9709C110.642 79.0271 110.706 79.0984 110.753 79.1802C110.801 79.2621 110.831 79.3529 110.842 79.4468C110.853 79.5408 110.844 79.636 110.817 79.7266C110.789 79.8171 110.743 79.901 110.682 79.9731C110.621 80.0452 110.545 80.104 110.461 80.1457L107.723 81.7562C107.619 81.8167 107.502 81.8492 107.382 81.8505Z"
        fill="#172B4D"
      />
      <path
        d="M71.7506 113.896C71.6245 113.896 71.4986 113.889 71.3733 113.875L21.9824 108.34V2.58899L70.9154 0.274406C71.3708 0.249768 71.8265 0.318719 72.2543 0.477004C72.682 0.635289 73.0728 0.879551 73.4025 1.19472C73.7345 1.50757 73.9994 1.88473 74.1809 2.30322C74.3625 2.72171 74.457 3.17277 74.4586 3.62894L75.135 110.482C75.1374 110.928 75.0517 111.37 74.8827 111.783C74.7138 112.196 74.4649 112.571 74.1504 112.888C73.8359 113.204 73.462 113.455 73.0502 113.627C72.6383 113.798 72.1967 113.887 71.7506 113.887V113.896ZM23.3629 107.106L71.5274 112.504C71.8088 112.535 72.0936 112.507 72.3632 112.42C72.6327 112.333 72.8808 112.19 73.0911 112.001C73.3015 111.811 73.4694 111.579 73.5837 111.32C73.6981 111.061 73.7563 110.781 73.7546 110.498L73.0781 3.64735C73.0766 3.37712 73.0204 3.10999 72.913 2.86199C72.8057 2.614 72.6493 2.39025 72.4534 2.20417C72.2574 2.01809 72.0259 1.87352 71.7727 1.77913C71.5194 1.68474 71.2498 1.64247 70.9798 1.65487L23.3537 3.90273L23.3629 107.106Z"
        fill="#172B4D"
      />
      <path
        d="M73.7822 5.66016L83.1464 6.12031C83.5032 6.13773 83.8402 6.28992 84.0892 6.54616C84.3381 6.8024 84.4806 7.14357 84.4877 7.50078C85.0077 39.546 85.5284 71.5928 86.05 103.641C86.0528 103.832 86.017 104.021 85.9447 104.198C85.8724 104.374 85.7651 104.535 85.6292 104.669C85.4933 104.802 85.3317 104.907 85.1541 104.977C84.9765 105.047 84.7866 105.08 84.5959 105.075L74.4103 104.766"
        fill="#172B4D"
      />
      <path
        d="M84.6364 106.01H84.5673L74.3818 105.7L74.437 103.859L84.6226 104.167C84.689 104.169 84.7551 104.158 84.8169 104.133C84.8787 104.109 84.9349 104.072 84.9822 104.026C85.0295 103.979 85.0668 103.923 85.092 103.862C85.1172 103.8 85.1297 103.734 85.1287 103.668L83.5665 7.53004C83.5639 7.4066 83.5153 7.28859 83.4302 7.19919C83.345 7.10979 83.2295 7.05546 83.1064 7.04687L73.7422 6.58672L73.8342 4.74609L83.1984 5.20625C83.7905 5.23766 84.3486 5.49269 84.7599 5.91978C85.1711 6.34687 85.405 6.91417 85.414 7.50703L86.9763 103.647C86.9811 103.957 86.9244 104.264 86.8094 104.551C86.6943 104.839 86.5232 105.1 86.3061 105.321C86.089 105.541 85.8301 105.717 85.5447 105.836C85.2592 105.956 84.9528 106.017 84.6433 106.017L84.6364 106.01Z"
        fill="#172B4D"
      />
      <path
        d="M65.345 61.6739C63.6194 61.6739 62.209 60.0427 62.209 58.0479C62.209 56.0531 63.6194 54.4219 65.345 54.4219C67.0705 54.4219 68.4809 56.0531 68.4809 58.0479C68.4809 60.0427 67.0705 61.6739 65.345 61.6739Z"
        fill="#172B4D"
      />
      <path
        d="M75.5674 64.2969C77.2335 66.2136 79.2012 67.8456 81.393 69.1285C82.8678 69.9913 85.7115 71.6295 89.6574 72.0275C91.8431 72.2576 93.6193 72.0413 96.0995 71.7514C97.6873 71.5665 99.2646 71.3008 100.825 70.9553C100.534 74.5445 100.245 78.1338 99.9579 81.723C98.6579 82.4344 97.2917 83.0177 95.8787 83.4647C93.8402 84.1207 91.7177 84.4787 89.5768 84.5276C87.0983 84.5382 84.638 84.1034 82.3133 83.2438C81.3291 82.8543 80.3661 82.4135 79.4281 81.9231C77.8336 81.134 76.5406 80.3931 75.6272 79.8524C75.6088 74.6573 75.5889 69.4721 75.5674 64.2969Z"
        fill="#0517F8"
      />
      <path
        d="M89.4246 85.0975C86.9288 85.0866 84.4541 84.6389 82.1128 83.7745C81.1936 83.4154 80.2943 83.0076 79.4185 82.5528L79.1724 82.4309C77.8632 81.782 76.5702 81.0734 75.3301 80.3256L75.054 80.1577L74.9941 62.75L76.0088 63.9188C77.6328 65.7911 79.5516 67.3858 81.6894 68.64C83.0377 69.4292 85.8469 71.0719 89.7214 71.4607C91.8451 71.6747 93.6005 71.4607 96.0302 71.187C97.5986 71.0037 99.1567 70.7411 100.698 70.4001L101.458 70.2298L100.512 82.0719L100.241 82.2215C98.9068 82.9495 97.5061 83.5473 96.0578 84.0069C93.9646 84.6785 91.7857 85.045 89.588 85.0952L89.4246 85.0975ZM76.202 79.5066C77.3294 80.1784 78.5028 80.8134 79.6831 81.4001L79.9316 81.5244C80.7715 81.9594 81.6331 82.351 82.5131 82.6978C84.7727 83.5314 87.1634 83.9537 89.5719 83.9448C91.6549 83.8963 93.72 83.5476 95.7035 82.9094C96.9822 82.5045 98.2219 81.9855 99.4077 81.3587L100.181 71.6839C98.8532 71.9554 97.505 72.1717 96.1567 72.3304C93.6558 72.6203 91.8474 72.832 89.5995 72.6065C85.4742 72.1901 82.52 70.4622 81.1004 69.6316C79.2853 68.5691 77.6189 67.2709 76.1445 65.7709L76.202 79.5066Z"
        fill="#172B4D"
      />
      <path
        d="M100.621 73.418L104.847 74.1082C105.452 74.2201 106.001 74.5303 106.409 74.9898C106.817 75.4493 107.06 76.0318 107.1 76.645C107.14 77.2583 106.974 77.8672 106.628 78.3754C106.283 78.8836 105.777 79.262 105.192 79.4506L100.073 80.3571C100.253 78.0456 100.435 75.7326 100.621 73.418Z"
        fill="#00A5BC"
      />
      <path
        d="M99.4326 81.0535L100.095 72.75L104.978 73.5507C105.698 73.6967 106.35 74.076 106.833 74.63C107.316 75.1839 107.603 75.8815 107.649 76.6149C107.696 77.3483 107.5 78.0767 107.091 78.6873C106.682 79.2979 106.083 79.7568 105.387 79.9929L105.29 80.0182L99.4326 81.0535ZM101.142 74.0891L100.698 79.6638L105.04 78.9C105.494 78.7409 105.884 78.4364 106.148 78.0337C106.413 77.631 106.537 77.1524 106.502 76.6719C106.467 76.1914 106.275 75.7358 105.956 75.3754C105.636 75.015 105.207 74.77 104.734 74.6781L101.142 74.0891Z"
        fill="#172B4D"
      />
      <path
        d="M112.117 89.7749C110.932 89.7749 109.968 89.5103 109.244 88.9834C107.904 88.0079 107.594 86.3398 107.124 83.8135C107.076 83.542 107.035 83.289 106.996 83.0474C106.832 82.0419 106.726 81.377 106.167 80.7742C105.838 80.4268 105.434 80.16 104.985 79.9942L105.399 78.9221C106.01 79.1509 106.561 79.5169 107.009 79.9919C107.803 80.8501 107.955 81.7842 108.128 82.8656C108.167 83.0957 108.206 83.3419 108.254 83.6019C108.687 85.9395 108.944 87.3475 109.918 88.0539C110.608 88.5485 111.636 88.7234 112.998 88.5738C113.398 88.53 113.791 88.4436 114.172 88.3162L111.358 72.098H110.79C109.874 72.0714 108.959 72.1291 108.054 72.2705C107.378 72.4178 106.356 72.9124 105.321 74.4402L104.368 73.7936C105.615 71.953 106.929 71.3364 107.819 71.1454C108.804 70.986 109.802 70.9198 110.799 70.9476C111.259 70.9476 111.618 70.9476 111.866 70.9591H112.327L115.463 89.034L115.039 89.2112C114.427 89.4701 113.781 89.6383 113.12 89.7104C112.787 89.7504 112.453 89.7719 112.117 89.7749Z"
        fill="#172B4D"
      />
      <path
        d="M104.191 85.4047C103.675 85.4049 103.177 85.2186 102.788 84.8801C102.44 84.5695 102.042 83.9851 102.109 82.959C102.203 81.5003 103.512 80.2418 104.693 79.6896C105.447 79.3353 106.062 79.3031 106.469 79.5999C107.212 80.1406 106.869 81.2564 106.301 83.1063C105.806 84.7168 105.328 85.0941 104.92 85.2644C104.689 85.3582 104.441 85.4058 104.191 85.4047ZM105.746 80.5501C105.309 80.6366 104.9 80.8276 104.552 81.1069C103.782 81.6706 103.298 82.3884 103.257 83.0326C103.227 83.4928 103.328 83.8195 103.554 84.022C103.678 84.1301 103.828 84.2037 103.99 84.2352C104.151 84.2667 104.319 84.255 104.474 84.2014C104.628 84.137 104.897 83.7275 105.194 82.768C105.454 81.9329 105.765 80.9228 105.746 80.5409V80.5501Z"
        fill="#172B4D"
      />
      <path
        d="M116.031 90.1771C115.702 90.1697 115.379 90.0883 115.086 89.9389C114.793 89.7896 114.537 89.5761 114.338 89.3143L115.244 88.6057C115.566 89.0175 116.063 89.1164 116.323 88.9508C116.4 88.8879 116.463 88.8103 116.51 88.7226C116.556 88.6349 116.584 88.5388 116.593 88.44L113.629 70.7769C113.403 70.738 113.17 70.757 112.953 70.8321C112.76 70.9106 112.586 71.0291 112.442 71.1798C112.299 71.3306 112.189 71.5101 112.12 71.7064L111.029 71.3429C111.153 70.9815 111.355 70.6516 111.62 70.3766C111.885 70.1015 112.207 69.8879 112.564 69.7508C113.145 69.5489 113.78 69.5636 114.352 69.7922L114.651 69.9141L117.748 88.394V88.4607C117.734 88.7489 117.654 89.0302 117.515 89.2829C117.376 89.5357 117.181 89.7534 116.945 89.9194C116.671 90.0907 116.354 90.1802 116.031 90.1771Z"
        fill="#172B4D"
      />
      <path
        d="M63.5705 59.9516C61.999 59.9516 60.7129 58.4653 60.7129 56.6477C60.7129 54.8301 61.999 53.3438 63.5705 53.3438C65.1419 53.3438 66.428 54.8301 66.428 56.6477C66.428 58.4653 65.1419 59.9516 63.5705 59.9516Z"
        fill="#0517F8"
      />
      <path
        d="M63.5705 60.5239C61.6769 60.5239 60.1377 58.7845 60.1377 56.6447C60.1377 54.505 61.6769 52.7656 63.5705 52.7656C65.464 52.7656 67.0032 54.505 67.0032 56.6447C67.0032 58.7845 65.4617 60.5239 63.5705 60.5239ZM63.5705 53.916C62.3119 53.916 61.2881 55.14 61.2881 56.6447C61.2881 58.1494 62.3119 59.3735 63.5705 59.3735C64.829 59.3735 65.8528 58.1494 65.8528 56.6447C65.8528 55.14 64.829 53.916 63.5705 53.916Z"
        fill="#172B4D"
      />
      <path
        d="M74.6856 105.206L74.6709 106.586L109.489 106.957L109.504 105.576L74.6856 105.206Z"
        fill="#172B4D"
      />
      <path
        d="M0.923877 104.424L0.90918 105.805L22.296 106.032L22.3107 104.652L0.923877 104.424Z"
        fill="#172B4D"
      />
    </g>
    <defs>
      <clipPath id="clip0_26295_40869">
        <rect
          width="118.182"
          height="113.622"
          fill="white"
          transform="translate(0.90918 0.273438)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const BrokenLinkIcon = ({ strokeColor, ...props }) => (
  <svg
    width="18"
    height="17"
    viewBox="0 0 18 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.962 11.9037L17.05 9.32035C17.9559 8.56248 18.0737 7.21554 17.3132 6.31187L15.3235 3.94787C14.563 3.0442 13.212 2.92601 12.3061 3.68388L8.67498 6.72161C8.27577 7.05558 8.13576 7.60688 8.3274 8.0903L8.73553 9.11985C8.92726 9.60351 9.4079 9.91065 9.9286 9.88226L10.6135 9.84492L10.7678 10.8117C10.8413 11.2729 11.1748 11.6505 11.6243 11.7817L12.8437 12.1374C13.2323 12.2508 13.652 12.163 13.962 11.9037ZM16.3928 7.08185C16.7266 7.47846 16.6748 8.06962 16.2772 8.40225L13.1893 10.9856C13.1869 10.9876 13.1836 10.9882 13.1806 10.9874L11.9612 10.6316C11.9578 10.6306 11.9552 10.6277 11.9546 10.6241L11.7633 9.4247C11.6876 8.95016 11.2643 8.60937 10.7832 8.6356L9.86237 8.6858C9.85833 8.68602 9.8546 8.68364 9.85311 8.67989L9.44499 7.65034C9.4435 7.64659 9.44459 7.64231 9.44768 7.63972L13.0788 4.60199C13.4764 4.26936 14.0693 4.32124 14.4031 4.71785L16.3928 7.08185ZM10.5968 11.6528L10.5967 11.6528L9.84982 11.8018L9.67627 10.7636C9.62483 10.4559 9.4878 10.1686 9.28078 9.93462L8.17151 8.68065L8.1715 8.68064C7.73661 8.18901 6.98801 8.13278 6.48462 8.55391L3.94437 10.679C3.03845 11.4369 2.92061 12.7838 3.68116 13.6875L5.66051 16.0393C6.43014 16.9538 7.80173 17.0622 8.70537 16.2799L11.6291 13.7491L11.6291 13.7491C12.5675 12.9367 11.8157 11.4098 10.5968 11.6528ZM10.833 12.8278C10.8424 12.8259 10.8483 12.8378 10.841 12.8441L10.837 12.8476L9.94033 13.6237L9.93935 13.6246L9.25932 14.2132L7.91726 15.3749C7.52067 15.7182 6.91869 15.6707 6.58091 15.2693L4.60156 12.9175C4.26776 12.5209 4.31948 11.9298 4.71708 11.5971L7.25733 9.47201C7.26123 9.46875 7.26704 9.46918 7.27042 9.473L8.37969 10.727C8.43789 10.7928 8.47642 10.8735 8.49088 10.96L8.7089 12.2642C8.74707 12.4925 8.8648 12.6873 9.02873 12.8256C9.20084 12.9708 9.42388 13.0537 9.65926 13.0477C9.71265 13.0464 9.76667 13.0404 9.82088 13.0296L10.8277 12.8289L10.833 12.8278ZM3.82918 7.06132C3.96957 6.76116 4.32697 6.63107 4.62746 6.77077L5.91593 7.36979C6.21641 7.50949 6.3462 7.86606 6.20581 8.16622C6.06542 8.46639 5.70802 8.59647 5.40753 8.45677L4.11906 7.85775C3.81858 7.71806 3.68879 7.36148 3.82918 7.06132ZM13.1523 15.7989C13.2383 16.1189 13.0485 16.4477 12.7283 16.5332C12.4082 16.6188 12.0789 16.4287 11.9929 16.1086L11.625 14.7395C11.539 14.4195 11.7288 14.0908 12.0489 14.0053C12.3691 13.9197 12.6983 14.1098 12.7843 14.4298L13.1523 15.7989ZM7.40039 4.07198C7.08025 4.15751 6.89044 4.48626 6.97645 4.80628L7.3444 6.17538C7.43041 6.49539 7.75966 6.68548 8.0798 6.59996C8.39995 6.51443 8.58975 6.18568 8.50375 5.86566L8.13579 4.49656C8.04979 4.17655 7.72054 3.98646 7.40039 4.07198ZM15.5013 13.8346C15.8018 13.9743 16.1592 13.8442 16.2996 13.544C16.44 13.2439 16.3102 12.8873 16.0097 12.7476L14.7212 12.1486C14.4208 12.0089 14.0634 12.1389 13.923 12.4391C13.7826 12.7393 13.9124 13.0958 14.2129 13.2355L15.5013 13.8346ZM6.77481 6.9259C7.02897 6.71328 7.06203 6.33539 6.84866 6.08186L5.93482 4.99607C5.72145 4.74254 5.34243 4.70938 5.08827 4.92201C4.83411 5.13463 4.80105 5.51252 5.01443 5.76605L5.92826 6.85184C6.14164 7.10537 6.52065 7.13853 6.77481 6.9259ZM14.2197 13.7373C14.0064 13.4838 13.6274 13.4506 13.3732 13.6633C13.119 13.8759 13.086 14.2538 13.2994 14.5073L14.2132 15.5931C14.4266 15.8466 14.8056 15.8798 15.0597 15.6671C15.3139 15.4545 15.347 15.0766 15.1336 14.8231L14.2197 13.7373Z"
      fill={strokeColor || "#172B4D"}
    />
  </svg>
);

export const EnergyIcon = ({ strokeColor, ...props }) => (
  <svg
    width="17"
    height="16"
    viewBox="0 0 17 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M9.20066 2L3.72266 9.33333H8.55599L7.91132 14L13.3893 6.66667H8.55599L9.20066 2Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const IceCrystalIcon = ({ strokeColor, ...props }) => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8.66927 2.92676V14.8385"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.1133 3.86719L8.66799 5.30255L7.22266 3.86719"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.0026 5.8252L3.33594 11.9399"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.767 7.61774L11.793 7.09296L12.3216 5.13281"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.57031 10.1475L5.54431 10.6722L5.01565 12.6324"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.33594 5.8252L14.0026 11.9399"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.57031 7.61774L5.54431 7.09296L5.01565 5.13281"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.767 10.1475L11.793 10.6722L12.3216 12.6324"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.22266 13.8983L8.66799 12.4629L10.1133 13.8983"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const InspectionIcon = ({ strokeColor, ...props }) => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M5.66797 3.58818L6.53464 2.51281C6.6613 2.35597 6.85264 2.26465 7.0553 2.26465H9.61464C9.8173 2.26465 10.0086 2.35597 10.1353 2.51281L11.0013 3.58818V3.58818V4.24994C11.0013 4.61524 10.7026 4.91171 10.3346 4.91171H6.33464C5.96664 4.91171 5.66797 4.61524 5.66797 4.24994V3.58818V3.58818H5.66797Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.66797 11.3973H7.0013"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.66797 8.74983H8.33464"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.57 14.8379H4.33333C3.59667 14.8379 3 14.2456 3 13.5144V4.91142C3 4.18017 3.59667 3.58789 4.33333 3.58789H5.66667"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11 3.58789H12.3333C13.07 3.58789 13.6667 4.18017 13.6667 4.91142V10.5609"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.5191 12.2646L12.0378 13.7351L11.1484 12.853"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.3346 15.5002C10.862 15.5002 9.66797 14.315 9.66797 12.8531C9.66797 11.3913 10.862 10.2061 12.3346 10.2061C13.808 10.2061 15.0013 11.3913 15.0013 12.8531C15.0013 14.315 13.808 15.5002 12.3346 15.5002"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const LightBulbIcon = ({ strokeColor, ...props }) => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10.4453 10.993H6.44531"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M6.13927 10.7993C4.95527 9.96874 4.24527 8.51882 4.49527 6.92264C4.76594 5.19742 6.2146 3.82029 7.96194 3.61713C10.3873 3.33455 12.4453 5.20867 12.4453 7.55926C12.4453 8.89933 11.7746 10.0819 10.7499 10.8006C10.5659 10.9296 10.4453 11.1295 10.4453 11.3532V13.1843C10.4453 14.0982 9.69927 14.8387 8.7786 14.8387H8.11194C7.19127 14.8387 6.44527 14.0982 6.44527 13.1843V11.3558C6.44527 11.1301 6.32394 10.929 6.13927 10.7993Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.7812 4.25039L14.5746 3.46289"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.11365 10.8682L2.32031 11.6557"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M3.78161 3.58828L2.98828 2.80078"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.7812 10.8682L14.5746 11.6557"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.4453 7.55842H15.572"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.32031 7.55842H2.44698"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.44531 12.8533H10.3586"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PreventiveIcon = ({ strokeColor, ...props }) => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M4.58821 13.6987C4.20421 13.4227 3.85421 13.1044 3.54688 12.7471"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.45703 6.89706C2.83103 5.84949 3.48703 4.93493 4.34103 4.25"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.10822 8.88281C2.10822 9.58031 2.23489 10.246 2.45689 10.8681"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.3519 3.53125C12.9732 3.53125 13.4772 4.03154 13.4772 4.64831C13.4772 5.26507 12.9732 5.76537 12.3519 5.76537C11.7306 5.76537 11.2266 5.26507 11.2266 4.64831C11.2272 4.03154 11.7312 3.53125 12.3519 3.53125"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.4786 3.95448C10.5179 3.30595 9.35861 2.92676 8.10995 2.92676C7.50195 2.92676 6.91595 3.01808 6.36328 3.18485"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.36328 14.5807C6.91595 14.7475 7.50195 14.8388 8.10995 14.8388C11.4239 14.8388 14.1099 12.1725 14.1099 8.88291C14.1099 8.52358 14.0726 8.1735 14.0113 7.83203"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M10.1094 9.7098C10.1094 10.8017 8.10938 11.5296 8.10938 11.5296C8.10938 11.5296 6.10938 10.8017 6.10938 9.7098V7.51207L7.56671 6.99127C7.91738 6.86619 8.30004 6.86619 8.65071 6.99127L10.1094 7.51207V9.7098Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ProcedureIcon = ({ strokeColor, ...props }) => (
  <svg
    width="16"
    height="17"
    viewBox="0 0 16 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M12.1419 5.58348L10.2566 3.71201C10.0066 3.46385 9.66722 3.32422 9.31389 3.32422H4.53255C3.79589 3.32422 3.19922 3.9165 3.19922 4.64775V13.9125C3.19922 14.6437 3.79589 15.236 4.53255 15.236H11.1992C11.9359 15.236 12.5326 14.6437 12.5326 13.9125V6.51922C12.5326 6.16848 12.3919 5.83165 12.1419 5.58348V5.58348Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.5326 6.63304H9.86589C9.49789 6.63304 9.19922 6.33657 9.19922 5.97128V3.32422"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.19922 10.6033H9.19922"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.19922 12.5887H8.08589"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ShieldIcon = ({ strokeColor, ...props }) => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M13.5573 9.54407C13.5573 12.4678 11.1693 14.8382 8.22396 14.8382V14.8382C5.27862 14.8382 2.89062 12.4678 2.89062 9.54407V5.51525C2.89062 5.17577 3.14929 4.88591 3.49062 4.85944C5.12996 4.73106 6.62196 4.08319 7.79929 3.08062C8.04062 2.87547 8.40796 2.87547 8.64862 3.08062C9.82596 4.08319 11.318 4.73172 12.9573 4.85944C13.2986 4.88591 13.5573 5.17577 13.5573 5.51525V9.54407Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.4364 7.78516L7.67105 10.5302L6.01172 8.88302"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const SpannersIcon = ({ strokeColor, ...props }) => (
  <svg
    width="17"
    height="17"
    viewBox="0 0 17 17"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M8.78125 10.8671L12.1146 14.176C12.6666 14.7239 13.5626 14.7239 14.1146 14.176V14.176C14.6666 13.628 14.6666 12.7386 14.1146 12.1907L10.7813 8.88184"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.8326 4.81028C14.8486 4.8255 14.8606 4.84535 14.8666 4.86653C15.1799 5.90351 14.9373 7.07021 14.1119 7.88947C13.2779 8.71734 12.0866 8.95425 11.0339 8.62866L5.46928 14.1524C4.92728 14.6904 4.04328 14.7308 3.48261 14.212C2.89328 13.6673 2.88128 12.7515 3.44528 12.1909L9.03395 6.64337C8.70595 5.59844 8.94461 4.41587 9.77861 3.588C10.6039 2.76873 11.7793 2.52785 12.8239 2.83888C12.8453 2.8455 12.8653 2.85675 12.8806 2.87263L12.9886 2.97984C13.0406 3.03146 13.0406 3.1155 12.9886 3.16712L11.2319 4.91153L12.7786 6.44682L14.5359 4.70241C14.5879 4.65079 14.6726 4.65079 14.7246 4.70241L14.8326 4.81028V4.81028Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M2.84525 3.78144L3.29459 5.12019C3.38525 5.39086 3.63992 5.57284 3.92725 5.57284H5.44659V4.06468C5.44659 3.78012 5.26325 3.52666 4.99059 3.43666L3.64192 2.99064C3.52192 2.95093 3.38992 2.98203 3.30059 3.07071L2.92592 3.44262C2.83659 3.5313 2.80525 3.66233 2.84525 3.78144V3.78144Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.44922 5.57324L7.78255 7.88942"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const GlobIcon = ({ strokeColor, ...props }) => (
  <svg
    width="9"
    height="9"
    viewBox="0 0 9 9"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_27155_180099)">
      <path
        d="M4.91142 7.90388C3.25386 7.90388 1.91016 6.56015 1.91016 4.90262C1.91016 3.24507 3.25386 1.90137 4.91142 1.90137C6.56895 1.90137 7.91265 3.24507 7.91265 4.90262"
        stroke={strokeColor || "white"}
        strokeWidth="0.48"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.9114 7.90388C6.56894 7.90388 7.91267 6.56015 7.91267 4.90262C7.91267 3.24507 6.56894 1.90137 4.9114 1.90137C3.25386 1.90137 1.91016 3.24507 1.91016 4.90262"
        stroke={strokeColor || "white"}
        strokeWidth="0.48"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.08203 3.90332H7.70703"
        stroke={strokeColor || "white"}
        strokeWidth="0.48"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M2.08203 5.90332H7.5787"
        stroke={strokeColor || "white"}
        strokeWidth="0.48"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.24186 4.90351C6.24436 3.97187 5.99466 3.05693 5.51919 2.25574C5.39339 2.03711 5.16036 1.90234 4.90809 1.90234C4.65586 1.90234 4.42282 2.03711 4.29702 2.25574C3.33328 3.8895 3.33328 5.91784 4.29702 7.55161C4.42339 7.76978 4.65616 7.90441 4.90826 7.90508"
        stroke={strokeColor || "white"}
        strokeWidth="0.48"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.57424 4.90351C3.57173 3.97187 3.82144 3.05693 4.2969 2.25574C4.4227 2.03711 4.65573 1.90234 4.90796 1.90234C5.16023 1.90234 5.39326 2.03711 5.51906 2.25574C6.4828 3.8895 6.4828 5.91784 5.51906 7.55161C5.3927 7.76978 5.15993 7.90441 4.9078 7.90508"
        stroke={strokeColor || "white"}
        strokeWidth="0.48"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_27155_180099">
        <rect
          width="8"
          height="8"
          fill={strokeColor || "white"}
          transform="translate(0.910156 0.90332)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const LockIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M14.1667 17.5889H5.83341C4.91258 17.5889 4.16675 16.843 4.16675 15.9222V9.25553C4.16675 8.3347 4.91258 7.58887 5.83341 7.58887H14.1667C15.0876 7.58887 15.8334 8.3347 15.8334 9.25553V15.9222C15.8334 16.843 15.0876 17.5889 14.1667 17.5889Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 14.3307V12.1724"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.4419 11.1054C10.686 11.3495 10.686 11.7453 10.4419 11.9893C10.1978 12.2334 9.80215 12.2334 9.55806 11.9893C9.31398 11.7453 9.31398 11.3495 9.55806 11.1054C9.80215 10.8613 10.1978 10.8613 10.4419 11.1054Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6.66675 7.58887V5.9222C6.66675 4.08137 8.15925 2.58887 10.0001 2.58887C11.8409 2.58887 13.3334 4.08137 13.3334 5.9222V7.58887"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const InfoIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="21"
    viewBox="0 0 20 21"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M10 3C5.85787 3 2.5 6.35786 2.5 10.5C2.5 14.6421 5.85786 18 10 18C14.1421 18 17.5 14.6421 17.5 10.5C17.5 6.35786 14.1421 3 10 3Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeMiterlimit="10"
    />
    <path
      d="M10 14.25L10 9.875"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 6.125C9.48223 6.125 9.0625 6.54473 9.0625 7.0625C9.0625 7.58027 9.48223 8 10 8C10.5178 8 10.9375 7.58027 10.9375 7.0625C10.9375 6.54473 10.5178 6.125 10 6.125Z"
      fill={strokeColor || "#748094"}
    />
  </svg>
);

export const CustomFieldIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="14"
    height="8"
    viewBox="0 0 14 8"
    fill="none"
  >
    <rect
      x="0.833008"
      y="0.5"
      width="12.3333"
      height="7"
      rx="1.9"
      stroke="#0517F8"
    />
    <rect
      x="2.99902"
      y="3.32812"
      width="4.66667"
      height="0.96"
      rx="0.48"
      fill="#0517F8"
    />
    <path
      d="M8.99902 3.32812L9.89659 4.40521C9.9499 4.46918 10.0481 4.46918 10.1015 4.40521L10.999 3.32812"
      stroke="#0517F8"
      strokeLinecap="round"
    />
  </svg>
);

export const HelpIcon = ({ ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    {...props}
  >
    <path
      d="M8.07059 8.07062C8.26811 7.21932 9.04347 6.62907 9.91663 6.66531C10.8939 6.61118 11.7316 7.35585 11.7924 8.33267C11.7924 9.58645 10 10 10 10.8337"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="2.49687"
      y="2.49689"
      width="15.0062"
      height="15.0062"
      rx="5"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.1042 13.1263C10.1042 13.1839 10.0576 13.2305 10 13.2305C9.94245 13.2305 9.89579 13.1839 9.89579 13.1263C9.89579 13.0688 9.94245 13.0221 10 13.0221"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 13.0221C10.0576 13.0221 10.1042 13.0687 10.1042 13.1263"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const HelpIconActive = ({ ...props }) => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    {...props}
  >
    <path
      d="M8.07056 8.07062C8.26808 7.21932 9.04344 6.62907 9.9166 6.66531C10.8938 6.61118 11.7316 7.35585 11.7924 8.33267C11.7924 9.58645 9.99997 10 9.99997 10.8337"
      stroke="#0517F8"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="2.49695"
      y="2.49689"
      width="15.0062"
      height="15.0062"
      rx="5"
      stroke="#0517F8"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.1042 13.1263C10.1042 13.1839 10.0575 13.2305 9.99997 13.2305C9.94242 13.2305 9.89576 13.1839 9.89576 13.1263C9.89576 13.0688 9.94242 13.0221 9.99997 13.0221"
      stroke="#0517F8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.99999 13.0221C10.0575 13.0221 10.1042 13.0687 10.1042 13.1263"
      stroke="#0517F8"
      strokeWidth="1.5"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ChatBubbleIcon = () => (
  <svg
    xmlns="http://www.w3.org/2000/svg"
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
  >
    <path
      d="M6 14.001L9.33333 11.3203"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.99935 11.3359V14.0026"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M6 11.3333H5.33333C3.49267 11.3333 2 9.84067 2 8V5.33333C2 3.49267 3.49267 2 5.33333 2H10.6667C12.5073 2 14 3.49267 14 5.33333V8C14 9.84067 12.5073 11.3333 10.6667 11.3333H9.33333"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const GraphIcon = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_29862_205637)">
      <path
        d="M2.00012 5.40699C2.70304 5.40699 3.27286 5.9768 3.27286 6.67969C3.27286 7.38258 2.70304 7.95238 2.00012 7.95238C1.29721 7.95238 0.727382 7.38258 0.727382 6.67969C0.727382 5.9768 1.29721 5.40699 2.00012 5.40699"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.3595 5.40699C12.0624 5.40699 12.6322 5.9768 12.6322 6.67969C12.6322 7.38258 12.0624 7.95238 11.3595 7.95238C10.6566 7.95238 10.0868 7.38258 10.0868 6.67969C10.0868 5.9768 10.6566 5.40699 11.3595 5.40699"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.80011 8.84345L4.52008 9.5634"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.84015 3.79658L9.56012 4.51652"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.67981 10.0867C7.38273 10.0867 7.95255 10.6565 7.95255 11.3594C7.95255 12.0623 7.38273 12.6321 6.67981 12.6321C5.97689 12.6321 5.40707 12.0623 5.40707 11.3594C5.40707 10.6565 5.97689 10.0867 6.67981 10.0867"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.67981 0.727304C7.38273 0.727304 7.95255 1.29711 7.95255 2C7.95255 2.70289 7.38273 3.2727 6.67981 3.2727C5.97689 3.2727 5.40707 2.70289 5.40707 2C5.40707 1.29711 5.97689 0.727303 6.67981 0.727304"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.84015 9.5628L9.56012 8.84285"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.80011 4.52374L4.52008 3.80379"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_29862_205637">
        <rect
          width="13.5995"
          height="13.6"
          fill="white"
          transform="translate(0.200195 0.203125)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const SerialNumberIcon = ({ ...props }) => (
  <svg
    width="14"
    height="8"
    viewBox="0 0 14 8"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M5.43661 2.31044C5.218 2.31044 5.0458 2.13825 4.94926 1.94211C4.87625 1.7938 4.7672 1.66871 4.62209 1.56686C4.3718 1.38947 4.04618 1.30078 3.64523 1.30078C3.36335 1.30078 3.12157 1.3433 2.91988 1.42835C2.71819 1.5134 2.56388 1.62882 2.45696 1.77462C2.35004 1.92042 2.29537 2.08688 2.29294 2.27399C2.29294 2.42951 2.32817 2.56437 2.39864 2.67858C2.47154 2.79279 2.56996 2.88999 2.69389 2.97018C2.81782 3.04794 2.95511 3.11355 3.10577 3.16701C3.25643 3.22047 3.40831 3.26542 3.5614 3.30187L4.26124 3.47683C4.54312 3.54244 4.81406 3.63114 5.07407 3.74292C5.33651 3.8547 5.57101 3.99564 5.77756 4.16574C5.98654 4.33583 6.15178 4.54117 6.27328 4.78174C6.39478 5.02231 6.45553 5.30419 6.45553 5.62738C6.45553 6.06477 6.34375 6.44993 6.12019 6.78284C5.89663 7.11331 5.57344 7.37211 5.15062 7.55922C4.73023 7.7439 4.22114 7.83624 3.62336 7.83624C3.04259 7.83624 2.53837 7.74633 2.11069 7.56651C1.68544 7.38669 1.35253 7.12425 1.11196 6.77919C0.948033 6.54167 0.835922 6.26846 0.775624 5.95955C0.729113 5.72127 0.927592 5.51803 1.17037 5.51803H1.65911C1.88295 5.51803 2.05815 5.69634 2.14184 5.90393C2.18082 6.00064 2.2324 6.08827 2.29658 6.16683C2.43752 6.33936 2.62099 6.46815 2.84698 6.5532C3.0754 6.63825 3.33055 6.68078 3.61243 6.68078C3.90646 6.68078 4.16404 6.63704 4.38517 6.54956C4.60873 6.45965 4.78369 6.33572 4.91005 6.17777C5.03641 6.01739 5.1008 5.83028 5.10323 5.61644C5.1008 5.42204 5.0437 5.26166 4.93192 5.1353C4.82014 5.00651 4.6634 4.89959 4.46171 4.81454C4.26245 4.72706 4.02917 4.6493 3.76187 4.58126L2.91259 4.36257C2.2978 4.20462 1.8118 3.96526 1.45459 3.6445C1.09981 3.32131 0.922416 2.89242 0.922416 2.35782C0.922416 1.91799 1.04149 1.53284 1.27963 1.20236C1.5202 0.871883 1.84703 0.615518 2.26013 0.433269C2.67323 0.24859 3.14101 0.15625 3.66346 0.15625C4.1932 0.15625 4.65733 0.24859 5.05585 0.433269C5.4568 0.615518 5.77148 0.869453 5.99991 1.19507C6.1468 1.40291 6.24797 1.63085 6.30341 1.87889C6.35557 2.11224 6.15741 2.31044 5.91831 2.31044H5.43661Z"
      fill="#A2AAB8"
    />
    <path
      d="M12.8477 0.271277C13.0839 0.271277 13.2753 0.462756 13.2753 0.698957V7.28803C13.2753 7.52423 13.0839 7.71571 12.8477 7.71571H12.2936C12.1549 7.71571 12.0248 7.64846 11.9446 7.53529L8.55505 2.75148H8.49309V7.28803C8.49309 7.52423 8.30161 7.71571 8.06541 7.71571H7.56847C7.33227 7.71571 7.14079 7.52423 7.14079 7.28803V0.698958C7.14079 0.462757 7.33227 0.271277 7.56847 0.271277H8.12404C8.26594 0.271277 8.39859 0.341652 8.47816 0.459139L11.8647 5.45971H11.9303V0.698957C11.9303 0.462756 12.1218 0.271277 12.358 0.271277H12.8477Z"
      fill="#A2AAB8"
    />
  </svg>
);

export const IdActivityLogIcon = ({ ...props }) => (
  <svg
    width="10"
    height="9"
    viewBox="0 0 10 9"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M1.23644 8.39095C0.990977 8.39095 0.791992 8.19196 0.791992 7.9465V1.15538C0.791992 0.909922 0.990977 0.710938 1.23644 0.710938H1.63288C1.87834 0.710938 2.07733 0.909922 2.07733 1.15538V7.9465C2.07733 8.19196 1.87834 8.39095 1.63288 8.39095H1.23644Z"
      fill="#748094"
    />
    <path
      d="M3.44396 8.39095C3.1985 8.39095 2.99951 8.19196 2.99951 7.9465V1.15538C2.99951 0.909922 3.1985 0.710938 3.44396 0.710938H5.44752C5.51152 0.710938 5.63774 0.712715 5.82618 0.716271C6.01819 0.719826 6.20307 0.732271 6.38085 0.753604C6.98885 0.831826 7.50263 1.04872 7.92219 1.40427C8.3453 1.75627 8.6653 2.20605 8.88219 2.75361C9.09908 3.30116 9.20752 3.90027 9.20752 4.55094C9.20752 5.20161 9.09908 5.80072 8.88219 6.34828C8.6653 6.89583 8.3453 7.34739 7.92219 7.70295C7.50263 8.05495 6.98885 8.27006 6.38085 8.34828C6.20663 8.36961 6.02352 8.38206 5.83152 8.38561C5.63952 8.38917 5.51152 8.39095 5.44752 8.39095H3.44396ZM4.30618 7.18028H5.44752C5.55418 7.18028 5.6893 7.17672 5.85285 7.16961C6.01996 7.1625 6.16752 7.1465 6.29552 7.12161C6.65819 7.05406 6.9533 6.89228 7.18085 6.63628C7.40841 6.38028 7.57552 6.06917 7.68219 5.70294C7.79241 5.33672 7.84752 4.95272 7.84752 4.55094C7.84752 4.13494 7.79241 3.74383 7.68219 3.37761C7.57196 3.01138 7.4013 2.70383 7.17019 2.45494C6.94263 2.20605 6.65107 2.04783 6.29552 1.98027C6.16752 1.95183 6.01996 1.93583 5.85285 1.93227C5.6893 1.92516 5.55418 1.92161 5.44752 1.92161H4.30618V7.18028Z"
      fill="#748094"
    />
  </svg>
);

export const HourlyGlassIcon = () => (
  <svg
    width="12"
    height="14"
    viewBox="0 0 12 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.30655 13.0819C1.19339 13.0819 1.08487 13.0361 1.00485 12.9547C0.924835 12.8732 0.879883 12.7628 0.879883 12.6476C0.879883 12.5324 0.924835 12.4219 1.00485 12.3405C1.08487 12.2591 1.19339 12.2133 1.30655 12.2133H2.15988V11.3447C2.15971 10.6073 2.3645 9.88484 2.7506 9.26084C3.13671 8.63683 3.68837 8.13674 4.34186 7.81833C4.58932 7.6976 4.71988 7.49088 4.71988 7.30588V6.69788C4.71988 6.51287 4.58847 6.30615 4.34186 6.18542C3.68837 5.86701 3.13671 5.36691 2.7506 4.74291C2.3645 4.11891 2.15971 3.39646 2.15988 2.65902V1.79045H1.30655C1.19339 1.79045 1.08487 1.74469 1.00485 1.66325C0.924835 1.5818 0.879883 1.47134 0.879883 1.35616C0.879883 1.24098 0.924835 1.13052 1.00485 1.04907C1.08487 0.96763 1.19339 0.921875 1.30655 0.921875H10.6932C10.8064 0.921875 10.9149 0.96763 10.9949 1.04907C11.0749 1.13052 11.1199 1.24098 11.1199 1.35616C11.1199 1.47134 11.0749 1.5818 10.9949 1.66325C10.9149 1.74469 10.8064 1.79045 10.6932 1.79045H9.83988V2.65902C9.84006 3.39646 9.63527 4.11891 9.24916 4.74291C8.86306 5.36691 8.3114 5.86701 7.65791 6.18542C7.41044 6.30615 7.27988 6.51287 7.27988 6.69788V7.30588C7.27988 7.49088 7.4113 7.6976 7.65791 7.81833C8.3114 8.13674 8.86306 8.63683 9.24916 9.26084C9.63527 9.88484 9.84006 10.6073 9.83988 11.3447V12.2133H10.6932C10.8064 12.2133 10.9149 12.2591 10.9949 12.3405C11.0749 12.4219 11.1199 12.5324 11.1199 12.6476C11.1199 12.7628 11.0749 12.8732 10.9949 12.9547C10.9149 13.0361 10.8064 13.0819 10.6932 13.0819H1.30655ZM3.01322 1.79045V2.65902C3.01322 3.12544 3.11562 3.56667 3.30079 3.96187H8.69898C8.8833 3.56667 8.98655 3.12544 8.98655 2.65902V1.79045H3.01322ZM5.57322 7.30588C5.57322 7.91474 5.16532 8.37943 4.7105 8.60178C4.20216 8.84942 3.77303 9.2384 3.47269 9.72377C3.17236 10.2091 3.01306 10.7711 3.01322 11.3447C3.01322 11.3447 3.7522 10.2165 5.57322 10.0592V7.30588ZM6.42655 7.30588V10.0592C8.24756 10.2165 8.98655 11.3447 8.98655 11.3447C8.9867 10.7711 8.82741 10.2091 8.52707 9.72377C8.22673 9.2384 7.7976 8.84942 7.28927 8.60178C6.83444 8.37943 6.42655 7.91561 6.42655 7.30674V7.30588Z"
      fill="#748094"
    />
  </svg>
);

export const DashedFileIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M3.33203 10V12.6667C3.33203 13.4033 3.9287 14 4.66536 14H11.332C12.0687 14 12.6654 13.4033 12.6654 12.6667V10"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.6654 6V5.21867C12.6654 4.86533 12.5247 4.526 12.2747 4.276L10.3894 2.39067C10.1394 2.14067 9.80003 2 9.4467 2H4.66536C3.9287 2 3.33203 2.59667 3.33203 3.33333V6"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.33203 7.9974H10.6654"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M12.668 7.9974H14.0013"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M5.60156 7.9974H6.9349"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2 7.9974H3.33333"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const ActivityLogHeadingIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.99609 2.66941H10.6664"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M14.0015 13.3335L10.6668 6.66406L7.33203 13.3335"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M8 12.0053H13.3356"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.99609 5.33347H7.99859"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M1.99609 8.00535H5.33082"
      stroke="#A2AAB8"
      strokeWidth="1.152"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const AttachmentsActivityLogIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_27058_177740)">
      <path
        d="M2.5 2.5V13.5"
        stroke="#A2AAB8"
        strokeWidth="1.152"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11 3.5H5C4.72386 3.5 4.5 3.72386 4.5 4V6.5C4.5 6.77614 4.72386 7 5 7H11C11.2761 7 11.5 6.77614 11.5 6.5V4C11.5 3.72386 11.2761 3.5 11 3.5Z"
        stroke="#A2AAB8"
        strokeWidth="1.152"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M13.5 9H5C4.72386 9 4.5 9.22386 4.5 9.5V12C4.5 12.2761 4.72386 12.5 5 12.5H13.5C13.7761 12.5 14 12.2761 14 12V9.5C14 9.22386 13.7761 9 13.5 9Z"
        stroke="#A2AAB8"
        strokeWidth="1.152"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_27058_177740">
        <rect width="16" height="16" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const ConnectedNodesIcon = () => (
  <svg
    width="14"
    height="14"
    viewBox="0 0 14 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <g clipPath="url(#clip0_29948_213147)">
      <path
        d="M1.99915 5.43824C2.70206 5.43824 3.27189 6.00805 3.27189 6.71094C3.27189 7.41383 2.70206 7.98363 1.99915 7.98363C1.29623 7.98363 0.726405 7.41383 0.726406 6.71094C0.726406 6.00805 1.29623 5.43824 1.99915 5.43824"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M11.3605 5.43824C12.0634 5.43824 12.6332 6.00805 12.6332 6.71094C12.6332 7.41383 12.0634 7.98363 11.3605 7.98363C10.6576 7.98363 10.0877 7.41383 10.0877 6.71094C10.0877 6.00805 10.6576 5.43824 11.3605 5.43824"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.79913 8.8747L4.5191 9.59465"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.84015 3.82783L9.56012 4.54777"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.68079 10.1179C7.3837 10.1179 7.95353 10.6877 7.95353 11.3906C7.95353 12.0935 7.3837 12.6633 6.68079 12.6633C5.97787 12.6633 5.40805 12.0935 5.40805 11.3906C5.40805 10.6877 5.97787 10.1179 6.68079 10.1179"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M6.68079 0.758554C7.3837 0.758554 7.95353 1.32836 7.95353 2.03125C7.95353 2.73414 7.3837 3.30395 6.68079 3.30395C5.97787 3.30395 5.40805 2.73414 5.40805 2.03125C5.40805 1.32836 5.97787 0.758553 6.68079 0.758554"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M8.84015 9.59405L9.56012 8.8741"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M3.79913 4.55499L4.5191 3.83504"
        stroke="#A2AAB8"
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_29948_213147">
        <rect
          width="13.5995"
          height="13.6"
          fill="white"
          transform="translate(0.199219 0.234375)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const UnscheduleActivityLogIcon = () => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M5.37501 2.75V4.5"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.625 2.75V4.5"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.75 6.54167H13.25"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.25 8.4375V5.625C13.25 4.52043 12.3546 3.625 11.25 3.625H4.75C3.64543 3.625 2.75 4.52043 2.75 5.625V11.25C2.75 12.3546 3.64543 13.25 4.75 13.25H8.58311"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13 11L11 13"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11 11L13 13"
      stroke="#A2AAB8"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const InfoOrangeBackgroundIcon = ({ ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <circle cx="8" cy="8" r="7.5" fill="#FF7A00" stroke="white" />
    <circle cx="8" cy="8" r="8" fill="#FF7A00" />
    <path
      d="M8.08105 8.00293L8.08105 3.20293"
      stroke="white"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M7.1752 11.6032C7.1752 11.1476 7.54456 10.7782 8.0002 10.7782C8.45583 10.7782 8.8252 11.1476 8.8252 11.6032C8.8252 12.0589 8.45583 12.4282 8.0002 12.4282C7.54456 12.4282 7.1752 12.0589 7.1752 11.6032Z"
      fill="white"
      stroke="white"
      strokeWidth="0.75"
    />
  </svg>
);

export const VerticalLine = ({ ...props }) => (
  <svg
    width="2"
    height="12"
    viewBox="0 0 2 12"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path d="M0 12V0H1.068V12H0Z" fill="#0517F8" />
  </svg>
);

export const CommunityIcon = ({ ...props }) => (
  <svg
    width="16"
    height="16"
    viewBox="0 0 16 16"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13.8609 6.34814C14.3876 6.87483 14.3876 7.72875 13.8609 8.25544C13.3342 8.78213 12.4803 8.78213 11.9536 8.25544C11.4269 7.72875 11.4269 6.87483 11.9536 6.34814C12.4803 5.82145 13.3342 5.82145 13.8609 6.34814"
      stroke="#172B4D"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M9.46697 3.94418C10.278 4.75516 10.278 6.07004 9.46697 6.88103C8.65598 7.69202 7.34111 7.69202 6.53012 6.88103C5.71913 6.07004 5.71913 4.75517 6.53012 3.94418C7.3411 3.13319 8.65598 3.13319 9.46697 3.94418"
      stroke="#172B4D"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M4.0445 6.34814C4.57119 6.87483 4.57119 7.72875 4.0445 8.25544C3.51782 8.78213 2.66389 8.78213 2.1372 8.25544C1.61052 7.72875 1.61052 6.87483 2.1372 6.34814C2.66389 5.82145 3.51782 5.82145 4.0445 6.34814"
      stroke="#172B4D"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M15.3335 12.6708V11.9401C15.3335 11.0194 14.5875 10.2734 13.6668 10.2734H13.1328"
      stroke="#172B4D"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M0.666016 12.6708V11.9401C0.666016 11.0194 1.41202 10.2734 2.33268 10.2734H2.86668"
      stroke="#172B4D"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M11.5588 12.6663V11.599C11.5588 10.3103 10.5141 9.26562 9.22545 9.26562H6.77279C5.48412 9.26562 4.43945 10.3103 4.43945 11.599V12.6663"
      stroke="#172B4D"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PdfColoredIcon = ({ ...props }) => (
  <svg
    width="32"
    height="36"
    viewBox="0 0 32 36"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M2.49998 0H21.6171L31.7499 10.0935V33.75C31.7499 34.9931 30.742 36 29.4999 36H2.49998C1.25794 36 0.25 34.9931 0.25 33.75V2.24998C0.25 1.0069 1.25806 0 2.49998 0Z"
      fill="#EE0047"
    />
    <path
      d="M31.7174 10.1259H23.875C22.6329 10.1259 21.625 9.11799 21.625 7.87595V0.0234375L31.7174 10.1259Z"
      fill="white"
      fillOpacity="0.7"
    />
    <path
      d="M23.3107 19.2949C23.6876 19.2949 23.8721 18.9664 23.8721 18.648C23.8721 18.3184 23.6797 18 23.3107 18H21.1642C20.7446 18 20.5106 18.3476 20.5106 18.7313V24.0063C20.5106 24.4766 20.7783 24.7376 21.1406 24.7376C21.5006 24.7376 21.7695 24.4766 21.7695 24.0063V22.5585H23.0678C23.4705 22.5585 23.6719 22.2288 23.6719 21.9015C23.6719 21.5809 23.4705 21.2625 23.0678 21.2625H21.7695V19.2949H23.3107ZM16.0556 18H14.4851C14.0587 18 13.756 18.2925 13.756 18.7267V24.0109C13.756 24.5497 14.1914 24.7185 14.503 24.7185H16.1512C18.1019 24.7185 19.39 23.4349 19.39 21.4537C19.3889 19.359 18.1762 18 16.0556 18ZM16.131 23.4158H15.1736V19.3028H16.0365C17.3427 19.3028 17.9107 20.1792 17.9107 21.3885C17.9107 22.5203 17.3527 23.4158 16.131 23.4158ZM10.3777 18H8.82187C8.38198 18 8.13672 18.2902 8.13672 18.7313V24.0063C8.13672 24.4766 8.41795 24.7376 8.79592 24.7376C9.17388 24.7376 9.45511 24.4766 9.45511 24.0063V22.4662H10.4305C11.6342 22.4662 12.6276 21.6134 12.6276 20.2421C12.6277 18.9 11.6692 18 10.3777 18ZM10.3519 21.2288H9.45523V19.2387H10.3519C10.9054 19.2387 11.2575 19.6706 11.2575 20.2343C11.2564 20.7968 10.9054 21.2288 10.3519 21.2288Z"
      fill="white"
    />
  </svg>
);

export const ActivityLogHourGlassIcon = () => (
  <svg
    width="12"
    height="14"
    viewBox="0 0 12 14"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M1.30655 13.0819C1.19339 13.0819 1.08487 13.0361 1.00485 12.9547C0.924835 12.8732 0.879883 12.7628 0.879883 12.6476C0.879883 12.5324 0.924835 12.4219 1.00485 12.3405C1.08487 12.2591 1.19339 12.2133 1.30655 12.2133H2.15988V11.3447C2.15971 10.6073 2.3645 9.88484 2.7506 9.26084C3.13671 8.63683 3.68837 8.13674 4.34186 7.81833C4.58932 7.6976 4.71988 7.49088 4.71988 7.30588V6.69787C4.71988 6.51287 4.58847 6.30615 4.34186 6.18542C3.68837 5.86701 3.13671 5.36691 2.7506 4.74291C2.3645 4.11891 2.15971 3.39646 2.15988 2.65902V1.79045H1.30655C1.19339 1.79045 1.08487 1.74469 1.00485 1.66325C0.924835 1.5818 0.879883 1.47134 0.879883 1.35616C0.879883 1.24098 0.924835 1.13052 1.00485 1.04907C1.08487 0.96763 1.19339 0.921875 1.30655 0.921875H10.6932C10.8064 0.921875 10.9149 0.96763 10.9949 1.04907C11.0749 1.13052 11.1199 1.24098 11.1199 1.35616C11.1199 1.47134 11.0749 1.5818 10.9949 1.66325C10.9149 1.74469 10.8064 1.79045 10.6932 1.79045H9.83988V2.65902C9.84006 3.39646 9.63527 4.11891 9.24916 4.74291C8.86306 5.36691 8.3114 5.86701 7.65791 6.18542C7.41044 6.30615 7.27988 6.51287 7.27988 6.69787V7.30588C7.27988 7.49088 7.4113 7.6976 7.65791 7.81833C8.3114 8.13674 8.86306 8.63683 9.24916 9.26084C9.63527 9.88484 9.84006 10.6073 9.83988 11.3447V12.2133H10.6932C10.8064 12.2133 10.9149 12.2591 10.9949 12.3405C11.0749 12.4219 11.1199 12.5324 11.1199 12.6476C11.1199 12.7628 11.0749 12.8732 10.9949 12.9547C10.9149 13.0361 10.8064 13.0819 10.6932 13.0819H1.30655ZM3.01322 1.79045V2.65902C3.01322 3.12544 3.11562 3.56667 3.30079 3.96187H8.69898C8.8833 3.56667 8.98655 3.12544 8.98655 2.65902V1.79045H3.01322ZM5.57322 7.30588C5.57322 7.91474 5.16532 8.37943 4.7105 8.60178C4.20216 8.84942 3.77303 9.2384 3.47269 9.72378C3.17236 10.2091 3.01306 10.7711 3.01322 11.3447C3.01322 11.3447 3.7522 10.2165 5.57322 10.0592V7.30588ZM6.42655 7.30588V10.0592C8.24756 10.2165 8.98655 11.3447 8.98655 11.3447C8.9867 10.7711 8.82741 10.2091 8.52707 9.72378C8.22673 9.2384 7.7976 8.84942 7.28927 8.60178C6.83444 8.37943 6.42655 7.91561 6.42655 7.30674V7.30588Z"
      fill="#748094"
    />
  </svg>
);

export const DuplicateReportImage = () => (
  <svg
    width="220"
    height="220"
    viewBox="0 0 220 220"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M58.7182 188.856C58.559 190.109 57.0239 187.597 56.8647 188.849C47.25 140.461 41.2927 91.8889 31.6766 43.5064C31.5511 42.8743 31.5398 42.2138 31.6434 41.5627C31.7471 40.9115 31.9637 40.2825 32.2807 39.7115C32.5978 39.1405 33.0092 38.6387 33.4915 38.2348C33.9737 37.8309 34.5173 37.5328 35.0912 37.3575L58.3867 30.2497C59.2583 81.7996 57.8449 137.303 58.7182 188.856Z"
      fill="#A2AAB8"
    />
    <rect
      x="57.1504"
      y="22"
      width="124.034"
      height="168.274"
      rx="4.14426"
      stroke="#172B4D"
      strokeWidth="2"
    />
    <rect
      x="70.5547"
      y="37.2031"
      width="10.8029"
      height="10.8029"
      rx="2.70074"
      fill="#0517F8"
    />
    <rect
      x="88.5605"
      y="35.4062"
      width="79.2216"
      height="14.4039"
      rx="7.20197"
      fill="#E8EAED"
    />
    <rect
      x="70.5547"
      y="58.8125"
      width="10.8029"
      height="10.8029"
      rx="2.70074"
      fill="#6974FB"
    />
    <rect
      x="88.5605"
      y="57.0156"
      width="79.2216"
      height="14.4039"
      rx="7.20197"
      fill="#E8EAED"
    />
    <g clipPath="url(#clip0_27865_206037)">
      <path
        d="M123.633 171.789C123.746 172.525 123.242 173.163 122.569 173.231C114.972 174.006 107.289 172.854 100.236 169.864C92.4843 166.579 85.7984 161.204 80.9243 154.339C76.0503 147.475 73.1799 139.391 72.6336 130.989C72.0873 122.588 73.8866 114.201 77.8306 106.763C81.7746 99.3245 87.7081 93.129 94.9688 88.8675C102.23 84.606 110.532 82.4461 118.949 82.6291C127.366 82.8121 135.566 85.3307 142.635 89.9037C149.067 94.0648 154.331 99.778 157.953 106.501C158.274 107.097 158.028 107.872 157.357 108.191L157.786 109.094L157.357 108.191L119.09 126.398C117.766 127.027 117.012 128.45 117.233 129.898L123.633 171.789Z"
        fill="#E6E8FE"
        stroke="#172B4D"
        strokeWidth="2"
      />
    </g>
    <path
      d="M183.246 104.221C184.473 103.657 185.88 104.206 186.357 105.398C189.821 114.05 191.458 123.34 191.148 132.679C190.801 143.143 188.021 153.382 183.028 162.585C178.035 171.787 170.967 179.7 162.384 185.695C154.715 191.052 146.394 194.687 137.334 196.487C136.081 196.735 134.858 195.864 134.654 194.535L125.173 132.844C125.009 131.776 125.572 130.729 126.554 130.278L183.246 104.221Z"
      fill="#CDD1FE"
      stroke="#172B4D"
      strokeWidth="2"
    />
    <path
      d="M181.624 143.424L196.775 170.006C197.909 171.996 200.882 171.622 201.489 169.414L204.629 157.991C204.794 157.389 205.174 156.867 205.696 156.524L214.837 150.513C216.748 149.256 216.191 146.317 213.952 145.847L184.387 139.634C182.217 139.178 180.526 141.498 181.624 143.424Z"
      fill="#ECB800"
      stroke="#172B4D"
      strokeWidth="2"
    />
    <defs>
      <clipPath id="clip0_27865_206037">
        <rect
          width="95.1697"
          height="95.1682"
          fill="white"
          transform="translate(71.582 81.7031)"
        />
      </clipPath>
    </defs>
  </svg>
);

export const FilterIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <g clipPath="url(#clip0_27865_203663)">
      <path
        d="M10 8.4375V16.875"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 3.125V5.3125"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M10 8.4375C10.8629 8.4375 11.5625 7.73794 11.5625 6.875C11.5625 6.01206 10.8629 5.3125 10 5.3125C9.13706 5.3125 8.4375 6.01206 8.4375 6.875C8.4375 7.73794 9.13706 8.4375 10 8.4375Z"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.625 14.6875V16.875"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.625 3.125V11.5625"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M15.625 14.6875C16.4879 14.6875 17.1875 13.9879 17.1875 13.125C17.1875 12.2621 16.4879 11.5625 15.625 11.5625C14.7621 11.5625 14.0625 12.2621 14.0625 13.125C14.0625 13.9879 14.7621 14.6875 15.625 14.6875Z"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.375 12.1875V16.875"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.375 3.125V9.0625"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
      <path
        d="M4.375 12.1875C5.23794 12.1875 5.9375 11.4879 5.9375 10.625C5.9375 9.76206 5.23794 9.0625 4.375 9.0625C3.51206 9.0625 2.8125 9.76206 2.8125 10.625C2.8125 11.4879 3.51206 12.1875 4.375 12.1875Z"
        stroke={strokeColor || "#172B4D"}
        strokeWidth="1.2"
        strokeLinecap="round"
        strokeLinejoin="round"
      />
    </g>
    <defs>
      <clipPath id="clip0_27865_203663">
        <rect width="20" height="20" fill="white" />
      </clipPath>
    </defs>
  </svg>
);

export const HorizontalBarChartIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M1 5.5C1 4.87868 1.50368 4.375 2.125 4.375H4.375C4.99632 4.375 5.5 4.87868 5.5 5.5V17.875C5.5 18.4963 4.99632 19 4.375 19H2.125C1.50368 19 1 18.4963 1 17.875V5.5Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
    />
    <path
      d="M7.75 2.125C7.75 1.50368 8.25368 1 8.875 1H11.125C11.7463 1 12.25 1.50368 12.25 2.125V17.875C12.25 18.4963 11.7463 19 11.125 19H8.875C8.25368 19 7.75 18.4963 7.75 17.875V2.125Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
    />
    <path
      d="M14.5 10C14.5 9.37868 15.0037 8.875 15.625 8.875H17.875C18.4963 8.875 19 9.37868 19 10V17.875C19 18.4963 18.4963 19 17.875 19H15.625C15.0037 19 14.5 18.4963 14.5 17.875V10Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
    />
  </svg>
);

export const NumberChartIcon = () => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      d="M6.25 11.6693L8.33333 9.44677L10.4167 11.6693L13.75 8.33594"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <rect
      x="2.5"
      y="2.5"
      width="15"
      height="15"
      rx="4.16493"
      stroke="#748094"
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const PieChartIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M13.6626 6.66729C13.8443 6.66646 13.991 6.51896 13.991 6.33812C13.991 6.15646 13.8435 6.00979 13.6618 6.00979C13.4801 6.00979 13.3326 6.15646 13.3326 6.33812C13.3326 6.51979 13.4801 6.66729 13.6618 6.66729"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M13.6626 3.14323C13.8351 3.1424 13.9743 3.0024 13.9743 2.8299C13.9743 2.6574 13.8343 2.51823 13.6618 2.51823C13.4893 2.51823 13.3493 2.65823 13.3493 2.83073C13.3493 3.00323 13.4893 3.14323 13.6626 3.14323"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M17.4827 6.33771C17.4827 6.16521 17.3427 6.02604 17.1702 6.02604C16.9977 6.02604 16.8585 6.16604 16.8585 6.33854C16.8585 6.51104 16.9985 6.65104 17.171 6.65104C17.3427 6.65104 17.4827 6.51104 17.4827 6.33771"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10 17.5V2.5C5.8575 2.5 2.5 5.8575 2.5 10C2.5 14.1425 5.8575 17.5 10 17.5C14.1425 17.5 17.5 14.1425 17.5 10H10"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const TableChartIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M15.8333 17.5H4.16667C3.24583 17.5 2.5 16.7542 2.5 15.8333V4.16667C2.5 3.24583 3.24583 2.5 4.16667 2.5H15.8333C16.7542 2.5 17.5 3.24583 17.5 4.16667V15.8333C17.5 16.7542 16.7542 17.5 15.8333 17.5Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 7.49479H17.5"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M2.5 12.4948H17.5"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
    <path
      d="M10.0007 7.5V17.5"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
      strokeLinecap="round"
      strokeLinejoin="round"
    />
  </svg>
);

export const VerticalBarChartIcon = ({ strokeColor, ...props }) => (
  <svg
    width="20"
    height="20"
    viewBox="0 0 20 20"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
    {...props}
  >
    <path
      d="M14.5 19C15.1213 19 15.625 18.4963 15.625 17.875V15.625C15.625 15.0037 15.1213 14.5 14.5 14.5H2.125C1.50368 14.5 1 15.0037 1 15.625V17.875C1 18.4963 1.50368 19 2.125 19H14.5Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
    />
    <path
      d="M17.875 12.25C18.4963 12.25 19 11.7463 19 11.125V8.875C19 8.25368 18.4963 7.75 17.875 7.75H2.125C1.50368 7.75 1 8.25368 1 8.875V11.125C1 11.7463 1.50368 12.25 2.125 12.25H17.875Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
    />
    <path
      d="M10 5.5C10.6213 5.5 11.125 4.99632 11.125 4.375V2.125C11.125 1.50368 10.6213 1 10 1H2.125C1.50368 1 1 1.50368 1 2.125V4.375C1 4.99632 1.50368 5.5 2.125 5.5H10Z"
      stroke={strokeColor || "#748094"}
      strokeWidth="1.2"
    />
  </svg>
);
