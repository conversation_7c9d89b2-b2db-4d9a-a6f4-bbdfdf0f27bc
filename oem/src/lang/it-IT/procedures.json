{"createCTA": "<PERSON><PERSON>re una procedura", "listActionDuplicate": "Dup<PERSON><PERSON>", "featureAccessDeniedModalTitle": "Se desidera utilizzare la funzione procedure, si rivolga a", "createProcedureForm": {"nameLengthError": "Il nome non può superare i 150 caratteri", "title": "<PERSON><PERSON>re una procedura", "nameLabel": "Nome della procedura *", "namePlaceholder": "Inserire il nome", "nameError": "Il nome deve essere inferiore a 75 caratteri", "descLabel": "Descrizione della procedura", "descPlaceholder": "Inserire la descrizione", "descError": "La descrizione deve essere inferiore a 270 caratteri", "submitButton": "Iniziare", "markInternal": {"title": "Contrassegnare come interno", "description": "Se selezionata, questa procedura sarà visibile solo al suo team. I clienti non potranno vederla o accedervi nel portale clienti."}, "createProcedureSuccess": "La procedura è stata creata con successo!"}, "saveCTA": "Salvare la procedura", "savingCTA": "Procedura di salvataggio", "partField": {"instanceDropdownTitle": "Scegliere le parti", "label": "Seleziona le parti", "placeholder": "Seleziona"}, "assetsField": {"label": "Selezione di sub-attività", "instanceDropdownTitle": "Selezioni la sotto-attività", "placeholder": "Seleziona"}, "memberField": {"label": "Selezionare l'utente", "placeholder": "Seleziona", "instanceDropdownTitle": "Seleziona l'agente OEM"}, "templateQuitAlertOkText": "Sì, lascia", "templateQuitAlertCancelText": "Annullamento", "tableField": {"maxColTooltip": "Numero massimo di colonne aggiunte", "rowLock": "Abilita l'aggiunta di righe supplementari alla tabella durante il completamento della procedura.", "columnDefaultHeading": "Inserisca il nome della colonna"}, "savingAsDraft": "<PERSON><PERSON><PERSON> come bozza", "instanceQuitAlertTitle": "È sicuro di volersene andare?", "instanceQuitAlertMessage": "Continuando senza salvare, perderà tutto il suo lavoro e gli aggiornamenti. È sicuro di volersene andare?", "instanceQuitAlertCancelText": "Annullamento", "editProcedureForm": {"nameLabel": "Nome della procedura *", "descPlaceholder": "Descrizione della procedura", "nameLengthError": "Il nome non può superare i 75 caratteri", "namePlaceholder": "Nome della procedura", "submitButton": "<PERSON><PERSON><PERSON> le modifiche", "title": "Modifica i dettagli della procedura", "descLabel": "Descrizione della procedura"}, "templateSaveAlertTitle": "Procedura di aggiornamento?", "finalizeAlertConfirmText": "Finalizzare", "browseFromDeviceText": "navigare dal suo dispositivo", "fieldType": "Tipo di campo", "fieldLabelPlaceholder": "Inserisca l'etichetta del campo", "procedureFieldLabelMaxCharError": "Il limite massimo di caratteri è di {num} caratteri", "fieldDescription": "Descrizione del campo", "valueText": "Valore", "addFieldToSection": "Aggiungere un campo alla sezione", "deleteSectionInfo": "Questa azione eliminerà questa sezione e tutti i campi al suo interno. È sicuro di voler continuare?", "optionText": "Opzione", "addOption": "Aggiungi opzione", "selectDateText": "Selezionare la data", "enterSignatoryTitle": "Inserisca il titolo del firmatario", "saveSignatureSettings": "Sal<PERSON><PERSON> le impostazioni", "uploadDocumentsBtnText": "Caricare i documenti", "field": "Campo", "instanceDraftSaveSuccess": "Le sue modifiche sono state salvate con successo", "instanceDraftSaveFail": "Impossibile salvare l'istanza della procedura", "downloadingPdf": "Scaricare il pdf", "customizeHeaderBtnText": "Personalizzare l'intestazione", "templateListColumns": {"lastUpdated": "Ultimo aggiornamento", "creationDate": "Data di creazione", "name": "Nome", "createdBy": "<PERSON><PERSON><PERSON> <PERSON>"}, "deleteProcedureModalInfo": "Elimineremo solo il modello di procedura. Le istanze allegate all'ordine di lavoro non saranno eliminate.", "successUpdatingAttachedProcedures": "Le procedure allegate sono state aggiornate con successo", "saveSettingsButtonText": "Sal<PERSON><PERSON> le impostazioni", "procedureTemplateSaveSuccess": "La procedura è stata salvata con successo", "procedureTemplateSaveFailed": "Non è riuscito a salvare la procedura", "procedureAttachFailed": "Impossibile allegare l'istanza di procedura", "finalizedText": "Finalizzato", "attachingProcedure": "Allegare la procedura", "procedureSignatureMaxCharLenErrorText": "Il limite massimo di caratteri è di {num} caratteri", "proceduresNotFoundText": "Non è stata trovata alcuna procedura. Clic<PERSON> sul pulsante 'Crea una procedura' per creare una nuova procedura.", "fieldAttachmentLargeFile": "Il suo file è più grande di {size} MB. La preghiamo di caricare un file più piccolo", "uploadingFieldAttachments": "Caricare i file...", "uploadedFieldAttachments": "File caricati", "singnatureDatePlaceholder": "Selezionare la data", "singnatureInputPlaceholder": "Inserire il nome", "hideAttachmentsText": "Nascondere gli allegati", "procedureStates": {"NOT_STARTED": "NON AVVIATO", "DRAFT": "PROGETTO", "FINALIZED": "FINALIZZATO"}, "deleteFieldBtnTooltip": "Cancellare il campo", "attachments": "Allegati", "searchPlaceholder": "Ricerca per nome", "listActionOpen": "Aperto", "listActionDelete": "Procedura di cancellazione", "templateQuitAlertTitle": "È sicuro di volersene andare?", "templateQuitAlertMessage": "Continuando senza salvare, perderà tutto il suo lavoro e gli aggiornamenti. È sicuro di volersene andare?", "saveAsDraft": "<PERSON>va come bozza", "finalizingProcedure": "Finalizzazione della procedura", "instanceQuitAlertOkText": "Sì, lascia", "templateSaveAlertConfirmText": "Sì, aggiornamento", "finalizeAlertMessage": "È sicuro di voler finalizzare questa procedura? Una volta che la procedura è definitiva, non potrà più modificarla.", "finalizingToastMsg": "La preghiamo di attendere mentre elaboriamo la sua richiesta...", "dragNdropImageText": "Trascinare e rilasciare immagini o", "dragNdropFileText": "Trascinare e rilasciare i file o", "fieldAttachementSupportedFileText": "Formati supportati: JPEG, PNG, PDF, Excel; fino a 10MB", "numberFieldPlaceholder": "Inserire il valore", "fieldRequired": "Campo obbligatorio", "deleteSectionHeadline": "Cancellare la sezione?", "checklistItems": "Articoli della lista di controllo", "procedureSignature": "Firma della procedura", "includeCustomerSignature": "Includere la firma", "enterDescription": "Inserire la descrizione", "singleSelect": "Selezione singola", "textFiledContentText": "Contenuto del campo di testo", "attachFiles": "Allega i file", "addItem": "Aggiungi articolo", "heading": "Intestazione", "instanceFinalizeSaveFail": "Non è riuscito a finalizzare la procedura", "signatureName": "nome", "signature": "Firma", "headerUploadSafeZone": "Zona sicura: 1500x500", "deleteProcedureModalTitle": "Cancellare la procedura?", "deleteProcedureInstanceModalInfo": "Elimineremo solo l'istanza della procedura collegata all'ordine di lavoro. È sicuro di voler continuare?", "duplicatingProcedure": "Procedura di duplicazione...", "updatingAttachedProcedures": "Aggiornamento delle procedure allegate...", "procedureDuplicateSuccessText": "Copia creata con successo", "procedurePreview": "Anteprima della procedura", "procedureAttachSuccess": "La procedura è stata allegata con successo", "procedureDownloadFailed": "Impossibile scaricare il PDF", "fieldAttachmentUnsupportedFile": "Il suo tipo di file non è supportato", "finalizingProcedureToastMsg": "Finalizzazione della procedura, attendere prego!", "procedureInstanceDeleteTooltipText": "La procedura finalizzata non può essere cancellata", "viewAttachmentsText": "Visualizza gli allegati", "addDescriptionBtnTooltip": "Aggiungi una descrizione", "attachFilesBtnTooltip": "Allega i file", "pageTitle": "Procedure", "fieldLabel": "Etichetta del campo", "dropHereText": "Lasci cadere qui", "customizeProcedureHeader": "Personalizzare l'intestazione della procedura", "duplicateFieldBtnTooltip": "Campo duplicato", "a4TagText": "Ritratto A4", "procedureSignatureInfo": "Aggiunga la firma se è necessaria per completare la procedura allegata ad un ordine di lavoro. Può vedere l'aspetto della firma nell'anteprima della procedura. La firma è un campo obbligatorio e la procedura non può essere finalizzata senza di essa.", "procedureDuplicateFailText": "La procedura di duplicazione è fallita", "finalizeProcedure": "Finalizzare la procedura", "templateSaveAlertMessage": "Le modifiche saranno applicate anche a tutte le procedure allegate all'ordine di lavoro in stato di bozza o non avviato e il suo lavoro sarà sovrascritto. È sicuro di voler continuare?", "finalizeAlertTitle": "Finalizzare la procedura?", "imageSupportedFileText": "Formati supportati: JPEG, PNG; fino a 10MB", "numberFieldError": "Sono ammessi solo i numeri", "enterTitle": "Inserire il titolo", "addSignature": "Aggiungere la firma", "dropdown": "Elementi a discesa", "section": "Sezione", "instanceFinalizeSaveSuccess": "Procedura conclusa con successo", "dateSigned": "Data della firma", "errorUpdatingAttachedProcedures": "Non è riuscito ad aggiornare le procedure allegate", "procedureDownloadSuccess": "Il PDF è stato scaricato con successo", "savingProcedureToastMsg": "Procedura di salvataggio...", "closedTicketProcedureInstanceDeleteTooltipText": "La procedura non può essere eliminata su un Ordine di lavoro chiuso.", "createdText": "<PERSON><PERSON><PERSON>", "allowUsersToAddMoreSignaturesTitle": "Consentire agli utenti di aggiungere più firme", "allowUsersToAddMoreSignaturesSubtitle": "Se è selezionata, gli utenti possono aggiungere altre firme durante la compilazione della procedura.", "notFound": {"title": "Non ci sono ancora modelli di procedura", "description": {"startingText": "Inizia a documentare i flussi di lavoro passo-passo per la manutenzione, l'installazione, le ispezioni e altro ancora.", "useTemplatesTo": {"title": "Con le procedure, può:", "bullet1": "Standardizzare il modo in cui il lavoro viene svolto dai vari team", "bullet2": "Allegare le procedure agli ordini di lavoro per un'esecuzione coerente", "bullet3": "Riutilizza i modelli di procedura per macchine e compiti simili.", "bullet4": "<PERSON><PERSON><PERSON><PERSON> istruzioni, tabelle e misure di sicurezza in un unico posto.", "bullet5": "Raccogliere le firme per la convalida"}}}, "editFields": "Modifica i campi"}