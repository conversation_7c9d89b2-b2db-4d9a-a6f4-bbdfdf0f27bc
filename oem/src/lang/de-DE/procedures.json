{"pageTitle": "Formulare", "createCTA": "Vor<PERSON> er<PERSON>", "searchPlaceholder": "Suche nach Name", "listActionOpen": "<PERSON><PERSON><PERSON>", "listActionDuplicate": "Duplizieren", "listActionDelete": "Löschen", "featureAccessDeniedModalTitle": "<PERSON>n Sie die Funktion \"Formulare\" nutzen möchten, wenden <PERSON>e sich bitte an", "createProcedureForm": {"title": "<PERSON><PERSON><PERSON><PERSON> ein Formular", "nameLabel": "Name des Formulars *", "namePlaceholder": "Name e<PERSON>ben", "nameError": "Der Name sollte weniger als 75 Zeichen umfassen", "descLabel": "Beschreibung des Formulars", "descPlaceholder": "Beschreibung eingeben", "descError": "Die Beschreibung sollte weniger als 270 Zeichen umfassen.", "submitButton": "<PERSON> geht's", "nameLengthError": "Der Name darf nicht länger als 150 Zeichen sein", "markInternal": {"title": "Als intern mark<PERSON>en", "description": "<PERSON>n Si<PERSON> diese Option wählen, ist dieser Vorgang nur für Ihr Team sichtbar. Kunden können es nicht im Kundenportal sehen oder darauf zugreifen."}, "createProcedureSuccess": "Die Prozedur wurde erfolgreich erstellt!"}, "saveCTA": "Vorlage speichern", "savingCTA": "Vorlage speichern", "partField": {"label": "Ersatzteile auswählen", "instanceDropdownTitle": "Ersatzteile auswählen", "placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "memberField": {"label": "Benutzer auswählen", "instanceDropdownTitle": "Mitarbeiter auswählen", "placeholder": "<PERSON><PERSON><PERSON><PERSON>"}, "templateQuitAlertTitle": "Sind Si<PERSON> sicher, dass Sie abbrechen möchten?", "templateQuitAlertMessage": "<PERSON>n <PERSON> fort<PERSON>, ohne zu s<PERSON>ichern, verlieren Sie Ihre gesamte Arbeit und alle Aktualisierungen. Sind Si<PERSON> sicher, dass Sie abbrechen möchten?", "templateQuitAlertOkText": "Ja, abbrechen", "templateQuitAlertCancelText": "Abbrechen", "tableField": {"columnDefaultHeading": "Spaltenname eingeben", "maxColTooltip": "Maximale Anzahl der hinzugefügten Spalten", "rowLock": "Aktivieren Sie das Hinzufügen zusätzlicher Zeilen in dieser Tabelle beim Ausfüllen des Formulars"}, "saveAsDraft": "Als Entwurf speichern", "savingAsDraft": "Als Entwurf speichern", "finalizeProcedure": "Formular finalisieren", "finalizingProcedure": "Formular finalisieren", "instanceQuitAlertTitle": "Sind Si<PERSON> sicher, dass Sie abbrechen möchten?", "instanceQuitAlertMessage": "<PERSON>n <PERSON> fort<PERSON>, ohne zu s<PERSON>ichern, verlieren Sie Ihre gesamte Arbeit und alle Aktualisierungen. Sind Si<PERSON> sicher, dass Sie abbrechen möchten?", "instanceQuitAlertOkText": "Ja, abbrechen", "instanceQuitAlertCancelText": "Abbrechen", "editProcedureForm": {"title": "Formulardetails bearbeiten", "nameLabel": "Name des Formulars *", "namePlaceholder": "Name des Formulars", "descLabel": "Beschreibung des Formulars", "descPlaceholder": "Beschreibung des Formulars", "submitButton": "Änderungen speichern", "nameLengthError": "Der Name darf nicht länger als 75 Zeichen sein"}, "templateSaveAlertTitle": "Formular aktualisieren?", "templateSaveAlertMessage": "Die Änderungen werden auch auf alle Formulare angewandt, die dem Arbeitsauftrag im Status \"Entwurf\" oder \"Nicht begonnen\" angehängt sind, und Ihre Arbeit wird überschrieben. Sind Si<PERSON> sicher, dass Sie fortfahren möchten?", "templateSaveAlertConfirmText": "Ja, aktualisieren", "finalizeAlertTitle": "Formular abschließen?", "finalizeAlertMessage": "Sind <PERSON> sic<PERSON>, dass Sie dieses Formular abschließen möchten? Sobald das Formular abgeschlossen ist, können Sie es nicht mehr bearbeiten.", "finalizeAlertConfirmText": "Fertigstellen", "finalizingToastMsg": "Bitte warten <PERSON>, während wir Ihre Anfrage bearbeiten...", "dragNdropImageText": "Bilder ziehen und ablegen oder", "dragNdropFileText": "<PERSON><PERSON><PERSON> und Ablegen von <PERSON> oder", "browseFromDeviceText": "von Ihrem Gerät aus browsen", "imageSupportedFileText": "Unterstützte Formate: JPEG, PNG; bis zu 10 MB", "fieldAttachementSupportedFileText": "Unterstützte Formate: JPEG, PNG, PDF, Excel; bis zu 10 MB", "numberFieldError": "<PERSON><PERSON> Zahlen sind erlaubt", "numberFieldPlaceholder": "<PERSON><PERSON> e<PERSON>ben", "fieldType": "<PERSON><PERSON>", "fieldLabel": "Feldbezeichnung", "fieldLabelPlaceholder": "Feldbezeichnung eingeben", "procedureFieldLabelMaxCharError": "Maximale Zeichenbegrenzung ist {num} Zeichen", "fieldDescription": "<PERSON>ld Beschreibung", "fieldRequired": "Erforderliches Feld", "valueText": "Wert", "addFieldToSection": "Feld zum Abschnitt hinzufügen", "deleteSectionHeadline": "Abschnitt löschen?", "deleteSectionInfo": "Diese Aktion löscht diesen Abschnitt und alle darin enthaltenen Felder. Sind Si<PERSON> sicher, dass Sie fortfahren möchten?", "optionText": "Option", "addOption": "Option hinzufügen", "checklistItems": "Punkte der Checkliste", "selectDateText": "Da<PERSON> ausw<PERSON>en", "dropHereText": "<PERSON><PERSON> <PERSON><PERSON>", "procedureSignature": "Formular Unterschrift", "procedureSignatureInfo": "Fügen Sie die Unterschrift hinzu, wenn sie erforderlich ist, um das an einen Arbeitsauftrag angehängte Formular abzuschließen. Sie können sehen, wie die Unterschrift in der Vorgangsvorschau aussehen würde. Die Unterschrift ist ein Pflichtfeld und der Vorgang kann ohne sie nicht abgeschlossen werden.", "includeCustomerSignature": "Unterschrift einfügen", "enterSignatoryTitle": "Titel des Unterzeichners eingeben", "enterTitle": "Titel e<PERSON>ben", "enterDescription": "Beschreibung eingeben", "addSignature": "Unterschrift hinzufügen", "saveSignatureSettings": "Einstellungen speichern", "singleSelect": "Einzelauswahl", "textFiledContentText": "Inhalt des Textfeldes", "attachFiles": "<PERSON><PERSON>", "uploadDocumentsBtnText": "Dokumente hochladen", "addItem": "<PERSON><PERSON><PERSON>", "field": "<PERSON><PERSON>", "section": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "heading": "Überschrift", "instanceDraftSaveSuccess": "Ihre Änderungen wurden erfolgreich gespeichert", "instanceDraftSaveFail": "Formular konnte nicht gespeichert werden", "instanceFinalizeSaveSuccess": "Formular <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> finalisiert", "instanceFinalizeSaveFail": "Fehlgeschlagen", "downloadingPdf": "<PERSON><PERSON><PERSON><PERSON><PERSON> von pdf", "dateSigned": "Datum der Unterschrift", "signatureName": "Name", "signature": "Unterschrift", "customizeHeaderBtnText": "Kopfzeile anpassen", "customizeProcedureHeader": "Kopfzeile anpassen", "headerUploadSafeZone": "Sichere Zone: 1500x500", "templateListColumns": {"name": "Name", "creationDate": "Erstellungsdatum", "lastUpdated": "Zuletzt aktualisiert", "createdBy": "<PERSON><PERSON><PERSON><PERSON> von"}, "deleteProcedureModalTitle": "Formular löschen?", "deleteProcedureModalInfo": "Es wird nur die Vorlage gelöscht. Die Formulare, die mit dem Arbeitsauftrag verbunden sind, werden nicht gelöscht.", "deleteProcedureInstanceModalInfo": "Wir löschen nur die mit dem Arbeitsauftrag verbundene Verfahrensinstanz. Sind Si<PERSON> sicher, dass Sie fortfahren möchten?", "duplicatingProcedure": "Formular dupli<PERSON><PERSON>", "updatingAttachedProcedures": "Aktualisierung der beigefügten Formulare...", "successUpdatingAttachedProcedures": "Angehängte Formulare erfolgreich aktualisiert", "errorUpdatingAttachedProcedures": "Aktualisierung der angehängten Formulare fehlgeschlagen", "procedureDuplicateSuccessText": "<PERSON><PERSON> erfolgreich erstellt", "procedureDuplicateFailText": "Kopie fehlgeschlagen", "procedurePreview": "Vorschau des Formulars", "saveSettingsButtonText": "Einstellungen speichern", "procedureTemplateSaveSuccess": "Formular er<PERSON><PERSON><PERSON><PERSON><PERSON> g<PERSON>peichert", "procedureTemplateSaveFailed": "Formular konnte nicht gespeichert werden", "procedureAttachSuccess": "Formular er<PERSON><PERSON><PERSON><PERSON><PERSON> an<PERSON>ä<PERSON>", "procedureAttachFailed": "Formular konnte nicht angehängt werden", "procedureDownloadSuccess": "PDF erfolgreich heruntergeladen", "procedureDownloadFailed": "PDF-Download fehlgeschlagen", "finalizedText": "Fertiggestellt", "attachingProcedure": "Anhängen des Formulars", "procedureSignatureMaxCharLenErrorText": "Maximale Zeichenbegrenzung ist {num} Zeichen", "proceduresNotFoundText": "Kein Formular gefunden. Klicken Sie auf die Schaltfläche 'Formular erstellen', um ein neues Formular zu erstellen.", "fieldAttachmentUnsupportedFile": "<PERSON><PERSON> Dateityp wird nicht unterstützt", "fieldAttachmentLargeFile": "Ihre Datei ist größer als {size} MB. Bitte laden Sie eine kleinere Datei hoch", "uploadingFieldAttachments": "<PERSON><PERSON><PERSON><PERSON> von <PERSON>...", "uploadedFieldAttachments": "Hochgeladene Dateien", "savingProcedureToastMsg": "Formular zum Speichern...", "singnatureDatePlaceholder": "Da<PERSON> ausw<PERSON>en", "singnatureInputPlaceholder": "Name e<PERSON>ben", "finalizingProcedureToastMsg": "A<PERSON>chluss des Formulars, bitte warten!", "procedureInstanceDeleteTooltipText": "Abgeschlossene Formulare können nicht gelöscht werden", "viewAttachmentsText": "<PERSON><PERSON><PERSON><PERSON> an<PERSON>", "hideAttachmentsText": "Anhänge ausblenden", "createdText": "<PERSON><PERSON><PERSON><PERSON>", "procedureStates": {"NOT_STARTED": "NICHT GESTARTET", "DRAFT": "<PERSON><PERSON><PERSON><PERSON>", "FINALIZED": "Fertiggestellt"}, "duplicateFieldBtnTooltip": "<PERSON><PERSON>", "addDescriptionBtnTooltip": "Beschreibung hinzufügen", "attachFilesBtnTooltip": "<PERSON><PERSON>", "deleteFieldBtnTooltip": "<PERSON><PERSON>", "a4TagText": "A4 hoch", "attachments": "<PERSON><PERSON><PERSON><PERSON>", "dropdown": "Dropdown-Elemente", "closedTicketProcedureInstanceDeleteTooltipText": "Formulare können bei einem geschlossenen Arbeitsauftrag nicht gelöscht werden.", "assetsField": {"label": "Auswahl der Unteranlage", "instanceDropdownTitle": "<PERSON>ter-<PERSON><PERSON> au<PERSON>en", "placeholder": "Wählen Sie"}, "allowUsersToAddMoreSignaturesTitle": "Erlauben Sie Benutzern, weitere Signaturen hinzuzufügen", "allowUsersToAddMoreSignaturesSubtitle": "<PERSON><PERSON> diese Option aktiviert ist, können Benutzer beim Ausfüllen des Verfahrens weitere Unterschriften hinzufügen.", "notFound": {"title": "Noch keine Vorlagen für Verfahren", "description": {"startingText": "Beginnen Si<PERSON> damit, Schritt für Schritt Arbeitsabläufe für Wartung, Installation, Inspektionen und mehr zu dokumentieren.", "useTemplatesTo": {"title": "Mit Verfahren können Sie:", "bullet1": "Standardisieren Sie die Arbeitsabläufe in den Teams", "bullet2": "Verbinden Sie Verfahren mit Arbeitsaufträgen für eine konsistente Ausführung", "bullet3": "Wiederverwendung von Verfahrensvorlagen für ähnliche Maschinen und Aufgaben", "bullet4": "Fügen Sie Anweisungen, Tabellen und Sicherheitsmaßnahmen an einem Ort hinzu", "bullet5": "Sammeln Sie Unterschriften für die Validierung"}}}, "editFields": "<PERSON><PERSON>"}