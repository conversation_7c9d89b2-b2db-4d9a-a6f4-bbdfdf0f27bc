{"pageTitle": "Procedures", "createCTA": "Create procedure", "searchPlaceholder": "Search by Name/ID", "listActionOpen": "Open", "listActionDuplicate": "Duplicate", "listActionDelete": "Delete procedure", "featureAccessDeniedModalTitle": "If you want to use the procedures feature, please contact", "createProcedureForm": {"title": "Create a procedure", "nameLabel": "Procedure name *", "nameLengthError": "Name cannot be more than 150 characters", "namePlaceholder": "Enter name", "nameError": "Name should be less than 75 character", "descLabel": "Procedure description", "descPlaceholder": "Enter description", "descError": "Description should be less than 270 character", "submitButton": "Get started", "markInternal": {"title": "<PERSON> as <PERSON>", "description": "If selected, this procedure will only be visible to your team. Customers won't be able to see or access it in the customer portal."}, "createProcedureSuccess": "The procedure is created successfully!"}, "notFound": {"title": "No Procedure Templates Yet", "description": {"startingText": "Start documenting step-by-step workflows for maintenance, installation, inspections, and more.", "useTemplatesTo": {"title": "With procedures, you can:", "bullet1": "Standardise how work gets done across teams", "bullet2": "Attach procedures to work orders for consistent execution", "bullet3": "Reuse procedure templates across similar machines and tasks", "bullet4": "Add instructions, tables, and safety steps in one place", "bullet5": "Collect signatures for validation"}}}, "saveCTA": "Save procedure", "savingCTA": "Saving procedure", "partField": {"label": "Select parts", "instanceDropdownTitle": "Choose parts", "placeholder": "Select"}, "assetsField": {"label": "Sub Asset Selection", "instanceDropdownTitle": "Select Sub Asset", "placeholder": "Select"}, "memberField": {"label": "Select user", "instanceDropdownTitle": "Select OEM agent", "placeholder": "Select"}, "templateQuitAlertTitle": "Are you sure you want to leave?", "templateQuitAlertMessage": "By continuing without saving, you will lose all your work and updates. Are you sure you want to leave?", "templateQuitAlertOkText": "Yes, leave", "templateQuitAlertCancelText": "Cancel", "tableField": {"columnDefaultHeading": "Enter column name", "maxColTooltip": "Maximum number of columns added", "rowLock": "Enable adding extra rows to the table during procedure completion."}, "saveAsDraft": "Save as Draft", "savingAsDraft": "Saving as Draft", "finalizeProcedure": "Finalize Procedure", "finalizingProcedure": "Finalizing Procedure", "instanceQuitAlertTitle": "Are you sure you want to leave?", "instanceQuitAlertMessage": "By continuing without saving, you will lose all your work and updates. Are you sure you want to leave?", "instanceQuitAlertOkText": "Yes, leave", "instanceQuitAlertCancelText": "Cancel", "editProcedureForm": {"title": "Edit procedure details", "nameLengthError": "Name cannot be more than 75 characters", "nameLabel": "Procedure name *", "namePlaceholder": "Procedure name", "descLabel": "Procedure description", "descPlaceholder": "Procedure description", "submitButton": "Save changes"}, "templateSaveAlertTitle": "Update procedure?", "templateSaveAlertMessage": "The changes will also be applied to all the procedures attached to work order in draft or not started status and your work will be overwritten. Are you sure you want to continue?", "templateSaveAlertConfirmText": "Yes, update", "finalizeAlertTitle": "Finalize procedure?", "finalizeAlertMessage": "Are you sure you want to finalize this procedure? Once the procedure is final, you won’t be able to edit it.", "finalizeAlertConfirmText": "Finalize", "finalizingToastMsg": "Please wait while we process your request...", "dragNdropImageText": "Drag and drop images or", "dragNdropFileText": "Drag and drop files or", "browseFromDeviceText": "browse from your device", "imageSupportedFileText": "Supported formats: JPEG, PNG; up to 10MB", "fieldAttachementSupportedFileText": "Supported formats: JPEG, PNG, PDF, Excel; up to 10MB", "numberFieldError": "Only numbers are allowed", "numberFieldPlaceholder": "Enter value", "fieldType": "Field type", "fieldLabel": "Field label", "fieldLabelPlaceholder": "Enter field label", "procedureFieldLabelMaxCharError": "Maximum character limit is {num} characters", "fieldDescription": "Field description", "fieldRequired": "Required field", "valueText": "Value", "addFieldToSection": "Add field to section", "deleteSectionHeadline": "Delete section?", "deleteSectionInfo": "This action will delete this section and all fields inside it. Are you sure you want to continue?", "optionText": "Option", "addOption": "Add option", "checklistItems": "Checklist Items", "selectDateText": "Select date", "dropHereText": "Drop here", "procedureSignature": "Procedure Signature", "procedureSignatureInfo": "Add the signature if it is required to complete the procedure attached to a work order. You can see what the signature would look like in the procedure preview. Signature is a required field and the procedure cannot be finalized without it.", "includeCustomerSignature": "Include signature", "enterSignatoryTitle": "Enter signatory title", "enterTitle": "Enter title", "enterDescription": "Enter description", "addSignature": "Add signature", "saveSignatureSettings": "Save settings", "singleSelect": "Single select", "dropdown": "Dropdown items", "textFiledContentText": "Text field content", "attachFiles": "Attach files", "uploadDocumentsBtnText": "Upload documents", "addItem": "Add item", "field": "Field", "section": "Section", "heading": "Heading", "instanceDraftSaveSuccess": "Your changes are saved successfully", "instanceDraftSaveFail": "Failed to save procedure instance", "instanceFinalizeSaveSuccess": "Finalized procedure successfully", "instanceFinalizeSaveFail": "Failed to finalize the procedure", "downloadingPdf": "Downloading pdf", "dateSigned": "Date signed", "signatureName": "name", "signature": "Signature", "customizeHeaderBtnText": "Customize header", "customizeProcedureHeader": "Customize procedure header", "headerUploadSafeZone": "Safe zone: 1500x500", "templateListColumns": {"name": "Name", "creationDate": "Creation date", "lastUpdated": "Last updated", "createdBy": "Created by"}, "deleteProcedureModalTitle": "Delete procedure?", "deleteProcedureModalInfo": "We will only delete the procedure template. The instances attached to the work order will not be deleted.", "deleteProcedureInstanceModalInfo": "We will only delete the procedure instance attached to the work order. Are you sure you want to continue?", "duplicatingProcedure": "Duplicating Procedure...", "updatingAttachedProcedures": "Updating attached procedures...", "successUpdatingAttachedProcedures": "Attached procedures updated successfully", "errorUpdatingAttachedProcedures": "Failed to update attached procedures", "procedureDuplicateSuccessText": "Copy created successfully", "procedureDuplicateFailText": "Duplicating procedure failed", "procedurePreview": "Procedure Preview", "saveSettingsButtonText": "Save settings", "procedureTemplateSaveSuccess": "Procedure saved successfully", "procedureTemplateSaveFailed": "Failed to save procedure", "procedureAttachSuccess": "Procedure attached successfully", "procedureAttachFailed": "Failed to attach procedure instance", "procedureDownloadSuccess": "PDF downloaded successfully", "procedureDownloadFailed": "Failed to download PDF", "finalizedText": "Finalized", "attachingProcedure": "Attaching the procedure", "procedureSignatureMaxCharLenErrorText": "Maximum character limit is {num} characters", "proceduresNotFoundText": "No procedure found. Click on the 'Create a procedure' button to create a new procedure.", "fieldAttachmentUnsupportedFile": "Your file type is not supported", "fieldAttachmentLargeFile": "Your file is larger than {size} MB. Please upload a smaller file", "uploadingFieldAttachments": "Uploading files...", "uploadedFieldAttachments": "Files uploaded", "savingProcedureToastMsg": "Saving procedure...", "singnatureDatePlaceholder": "Select date", "singnatureInputPlaceholder": "Enter name", "finalizingProcedureToastMsg": "Finalizing Procedure, please wait!", "procedureInstanceDeleteTooltipText": "Finalized procedure cannot be deleted", "closedTicketProcedureInstanceDeleteTooltipText": "Procedure cannot be deleted on a closed Work Order.", "viewAttachmentsText": "View attachments", "hideAttachmentsText": "Hide attachments", "createdText": "Created", "procedureStates": {"NOT_STARTED": "NOT STARTED", "DRAFT": "DRAFT", "FINALIZED": "FINALIZED"}, "duplicateFieldBtnTooltip": "Duplicate field", "addDescriptionBtnTooltip": "Add description", "attachFilesBtnTooltip": "Attach files", "deleteFieldBtnTooltip": "Delete field", "a4TagText": "A4 portrait", "attachments": "Attachments", "allowUsersToAddMoreSignaturesTitle": "Allow users to add more signatures", "allowUsersToAddMoreSignaturesSubtitle": "If checked, users can add more signatures while filling out the procedure.", "editFields": "<PERSON>"}