{"pageTitle": "Procedimientos", "createCTA": "<PERSON><PERSON><PERSON> proced<PERSON><PERSON>o", "searchPlaceholder": "Buscar por nombre", "listActionOpen": "Abrir", "listActionDuplicate": "Duplicar", "listActionDelete": "<PERSON><PERSON><PERSON> proced<PERSON><PERSON>", "featureAccessDeniedModalTitle": "Si desea utilizar la función de trámites, póngase en contacto con", "createProcedureForm": {"title": "Crear un procedimiento", "nameLabel": "Nombre del procedimiento *", "namePlaceholder": "Introduzca el nombre", "nameError": "El nombre debe tener menos de 75 caracteres", "descLabel": "Descripción del procedimiento", "descPlaceholder": "Introducir descripción", "descError": "La descripción debe tener menos de 270 caracteres", "submitButton": "Empezar", "nameLengthError": "El nombre no puede tener más de 150 caracteres", "markInternal": {"title": "Marcar como interno", "description": "Si se selecciona, este procedimiento sólo será visible para su equipo. Los clientes no podrán verlo ni acceder a él en el portal del cliente."}, "createProcedureSuccess": "¡El procedimiento se ha creado con éxito!"}, "saveCTA": "Guardar procedimiento", "savingCTA": "Procedimiento de guardado", "partField": {"label": "Seleccionar repuest<PERSON>", "instanceDropdownTitle": "Elegir repuestos", "placeholder": "Seleccione"}, "memberField": {"label": "Se<PERSON><PERSON><PERSON>r usuario", "instanceDropdownTitle": "Seleccionar agente OEM", "placeholder": "Seleccione"}, "templateQuitAlertTitle": "¿Se<PERSON>ro que quieres irte?", "templateQuitAlertMessage": "Si continúas sin guardar, perderás todo tu trabajo y actualizaciones. ¿Seguro que quieres irte?", "templateQuitAlertOkText": "Sí, vete.", "templateQuitAlertCancelText": "<PERSON><PERSON><PERSON>", "tableField": {"columnDefaultHeading": "Introduzca el nombre de la columna", "maxColTooltip": "Número máximo de columnas añadidas", "rowLock": "Permite añadir filas adicionales a la tabla durante la finalización del procedimiento."}, "saveAsDraft": "Guardar como borrador", "savingAsDraft": "Guardar como proyecto", "finalizeProcedure": "Finalizar el procedimiento", "finalizingProcedure": "Finalización del procedimiento", "instanceQuitAlertTitle": "¿Se<PERSON>ro que quieres irte?", "instanceQuitAlertMessage": "Si continúas sin guardar, perderás todo tu trabajo y actualizaciones. ¿Seguro que quieres irte?", "instanceQuitAlertOkText": "Sí, vete.", "instanceQuitAlertCancelText": "<PERSON><PERSON><PERSON>", "editProcedureForm": {"title": "Editar detalles del procedimiento", "nameLabel": "Nombre del procedimiento *", "namePlaceholder": "Nombre del procedimiento", "descLabel": "Descripción del procedimiento", "descPlaceholder": "Descripción del procedimiento", "submitButton": "Guardar cambios", "nameLengthError": "El nombre no puede tener más de 75 caracteres"}, "templateSaveAlertTitle": "¿Procedimiento de actualización?", "templateSaveAlertMessage": "Los cambios también se aplicarán a todos los procedimientos adjuntos a la orden de trabajo en estado borrador o no iniciado y su trabajo se sobrescribirá. ¿Está seguro de que desea continuar?", "templateSaveAlertConfirmText": "Sí, actualizar", "finalizeAlertTitle": "¿Finalizar el procedimiento?", "finalizeAlertMessage": "¿Está seguro de que desea finalizar este procedimiento? Una vez finalizado el procedimiento, no podrá editarlo.", "finalizeAlertConfirmText": "Finalizar", "finalizingToastMsg": "Espere mientras procesamos su solicitud...", "dragNdropImageText": "Arrastre y suelte imágenes o", "dragNdropFileText": "Arrastre y suelte archivos o", "browseFromDeviceText": "navegar desde tu dispositivo", "imageSupportedFileText": "Formatos admitidos: JPEG, PNG; hasta 10 MB", "fieldAttachementSupportedFileText": "Formatos admitidos: JPEG, PNG, PDF, Excel; hasta 10 MB", "numberFieldError": "<PERSON><PERSON><PERSON> se <PERSON>en nú<PERSON>", "numberFieldPlaceholder": "Introduzca el valor", "fieldType": "Tipo de campo", "fieldLabel": "Etiqueta de campo", "fieldLabelPlaceholder": "Introducir etiqueta de campo", "procedureFieldLabelMaxCharError": "El límite máximo de caracteres es {num} caracteres", "fieldDescription": "Cargar documentos", "fieldRequired": "Campo obligatorio", "valueText": "Valor", "addFieldToSection": "Añadir campo a la sección", "deleteSectionHeadline": "¿Suprimir sección?", "deleteSectionInfo": "Esta acción eliminará esta sección y todos los campos que contiene. ¿Está seguro de que desea continuar?", "optionText": "Opción", "addOption": "Añadir opción", "checklistItems": "Lista de control", "selectDateText": "Seleccione la fecha", "dropHereText": "<PERSON><PERSON><PERSON><PERSON> a<PERSON>", "procedureSignature": "Procedimiento Firma", "procedureSignatureInfo": "Añada la firma si es necesaria para completar el procedimiento adjunto a una orden de trabajo. Puede ver el aspecto de la firma en la vista previa del procedimiento. La firma es un campo obligatorio y el procedimiento no puede finalizarse sin ella.", "includeCustomerSignature": "Incluir firma", "enterSignatoryTitle": "Introduzca el título del firmante", "enterTitle": "<PERSON><PERSON><PERSON><PERSON><PERSON> t<PERSON>", "enterDescription": "Introducir descripción", "addSignature": "<PERSON><PERSON><PERSON>", "saveSignatureSettings": "<PERSON><PERSON>", "singleSelect": "Selección individual", "textFiledContentText": "Contenido del campo de texto", "attachFiles": "Adjuntar archivos", "uploadDocumentsBtnText": "Cargar documentos", "addItem": "<PERSON><PERSON><PERSON>", "field": "Campo", "section": "Sección", "heading": "Rúbrica", "instanceDraftSaveSuccess": "Sus cambios se han guardado correctamente", "instanceDraftSaveFail": "Error al guardar la instancia del procedimiento", "instanceFinalizeSaveSuccess": "Procedimiento finalizado con éxito", "instanceFinalizeSaveFail": "No se ha podido finalizar el procedimiento", "downloadingPdf": "Descargar pdf", "dateSigned": "<PERSON><PERSON> firma", "signatureName": "nombre", "signature": "Firma", "customizeHeaderBtnText": "Personalizar cabecera", "customizeProcedureHeader": "Personalizar la cabecera del procedimiento", "headerUploadSafeZone": "Zona segura: 1500x500", "templateListColumns": {"name": "Nombre", "creationDate": "Fecha de creación", "lastUpdated": "Última actualización", "createdBy": "<PERSON><PERSON>o por"}, "deleteProcedureModalTitle": "¿<PERSON><PERSON>r procedimiento?", "deleteProcedureModalInfo": "Sólo borraremos la plantilla del procedimiento. Las instancias adjuntas a la orden de trabajo no se eliminarán.", "deleteProcedureInstanceModalInfo": "<PERSON><PERSON>lo eliminaremos la instancia de procedimiento adjunta a la orden de trabajo. ¿Está seguro de que desea continuar?", "duplicatingProcedure": "Procedimiento de duplicación...", "updatingAttachedProcedures": "Actualización de los procedimientos adjuntos...", "successUpdatingAttachedProcedures": "Procedimientos adjuntos actualizados correctamente", "errorUpdatingAttachedProcedures": "No se han podido actualizar los procedimientos adjuntos", "procedureDuplicateSuccessText": "Copia creada con éxito", "procedureDuplicateFailText": "Error en el procedimiento de duplicación", "procedurePreview": "Vista previa del procedimiento", "saveSettingsButtonText": "<PERSON><PERSON>", "procedureTemplateSaveSuccess": "Procedimiento guardado correctamente", "procedureTemplateSaveFailed": "Error al guardar el procedimiento", "procedureAttachSuccess": "Procedimiento conectado correctamente", "procedureAttachFailed": "Fallo al adjuntar instancia de procedimiento", "procedureDownloadSuccess": "PDF descargado correctamente", "procedureDownloadFailed": "Fallo al descargar PDF", "finalizedText": "Finalizado", "attachingProcedure": "Adjuntar el procedimiento", "procedureSignatureMaxCharLenErrorText": "El límite máximo de caracteres es {num} caracteres", "proceduresNotFoundText": "No se ha encontrado ningún procedimiento. Haga clic en el botón \"Crear un procedimiento\" para crear un nuevo procedimiento.", "fieldAttachmentUnsupportedFile": "Su tipo de archivo no es compatible", "fieldAttachmentLargeFile": "Su archivo pesa más de {size} MB. <PERSON>r favor, cargue un archivo más pequeño", "uploadingFieldAttachments": "Cargar archivos...", "uploadedFieldAttachments": "Archivos cargados", "savingProcedureToastMsg": "Procedimiento de guardado...", "singnatureDatePlaceholder": "<PERSON><PERSON><PERSON><PERSON><PERSON> fecha", "singnatureInputPlaceholder": "Introduzca el nombre", "finalizingProcedureToastMsg": "Finalizando el procedimiento, ¡por favor espere!", "procedureInstanceDeleteTooltipText": "El procedimiento finalizado no puede borrarse", "viewAttachmentsText": "Ver archivos adjuntos", "hideAttachmentsText": "Ocultar archivos adjuntos", "createdText": "<PERSON><PERSON><PERSON>", "procedureStates": {"NOT_STARTED": "NO COMENZADO", "DRAFT": "PROYECTO", "FINALIZED": "FINALIZADO"}, "duplicateFieldBtnTooltip": "Duplicar campo", "addDescriptionBtnTooltip": "Añadir <PERSON>", "attachFilesBtnTooltip": "Adjuntar archivos", "deleteFieldBtnTooltip": "Borrar campo", "a4TagText": "A4 retrato", "attachments": "Archivos adjuntos", "dropdown": "Elementos desplegables", "closedTicketProcedureInstanceDeleteTooltipText": "El procedimiento no puede borrarse en una Orden de Trabajo cerrada.", "assetsField": {"label": "Selección de subactivos", "instanceDropdownTitle": "Seleccionar subactivo", "placeholder": "Seleccione"}, "allowUsersToAddMoreSignaturesTitle": "Permitir a los usuarios añadir más firmas", "allowUsersToAddMoreSignaturesSubtitle": "Si está marcada, los usuarios pueden añadir más firmas mientras rellenan el procedimiento.", "notFound": {"title": "Aún no hay plantillas de procedimientos", "description": {"startingText": "Empiece a documentar los flujos de trabajo paso a paso para el mantenimiento, la instalación, las inspecciones y mucho más.", "useTemplatesTo": {"title": "Con los procedimientos, puede:", "bullet1": "Normalizar cómo se realiza el trabajo en todos los equipos", "bullet2": "Adjunte procedimientos a las órdenes de trabajo para una ejecución coherente", "bullet3": "Reutilice plantillas de procedimientos en máquinas y tareas similares", "bullet4": "Añada instrucciones, tablas y pasos de seguridad en un solo lugar", "bullet5": "Recoger firmas para la validación"}}}, "editFields": "<PERSON><PERSON> camp<PERSON>"}