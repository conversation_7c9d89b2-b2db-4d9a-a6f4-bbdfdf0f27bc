import {
  Dnd<PERSON>ontext,
  Drag<PERSON><PERSON><PERSON>,
  KeyboardSensor,
  PointerSensor,
  defaultDropAnimationSideEffects,
  useSensor,
  useSensors,
} from "@dnd-kit/core";
import {
  restrictToVerticalAxis,
  restrictToWindowEdges,
} from "@dnd-kit/modifiers";
import {
  SortableContext,
  arrayMove,
  sortableKeyboardCoordinates,
  verticalListSortingStrategy,
} from "@dnd-kit/sortable";
import { TOAST_AUTO_CLOSE_DURATION } from "@shared/constants/index";
import ProcedureTemplateSaveAlertIcon from "@shared/svg/general-assign.svg?react";
import { AlertBox } from "@shared/ui/AlertBox";
import Breadcrumbs from "@shared/ui/Breadcrumbs";
import Button from "@shared/ui/Button";
import { TOAST_TYPES } from "@shared/ui/Toast";
import { isArray } from "lodash";
import { Spinner } from "phosphor-react";
import React, { useEffect, useMemo, useRef, useState } from "react";
import { createPortal } from "react-dom";
import { useIntl } from "react-intl";
import { useParams } from "react-router-dom";

import ProcedureInstancePreview from "./ProcedureInstancePreview";
import ProcedurePreview from "./ProcedurePreview";
import Loading from "../../../components/general/_loading";
import useAuth from "../../../components/general/_use-auth";
import ContentBox from "../../../components/general/ContentBox";
import ProcedureNameDesc from "../../../components/procedures/ProcedureNameDesc";
import SortableField from "../../../components/procedures/template/field/SortableField";
import SortableHeading from "../../../components/procedures/template/heading/SortableHeading";
import SignatureField from "../../../components/procedures/template/SignatureField";
import RouterPrompt from "../../../components/RouterPrompt";
import {
  PROCEDURE_UPDATING_TOAST_ID,
  PROCEDURE_FIELD_TYPE,
  PROCEDURE_NODE_PROPERTY,
  PROCEDURE_NODE_TYPE,
} from "../../../constants";
import { useUploadFiles } from "../../../hooks";
import useUpdatableToast from "../../../hooks/_useToast";
import { useSaveProcedure } from "../../../services/procedures/_mutation";
import { cloneAttachments, cloneProcedureTemplate } from "../../../utils";

import { useRoleManager } from "#/src/hooks/_useRoleManager";
import EmptySpace from "~/components/_emptySpace";
import AppHeaderWithSidebarOption from "~/components/AppHeaderWithSidebarOption";
import LoadingOverlay from "~/components/common/LoadingOverlay";
import ProcedureFormOptions from "~/components/procedures/ProcedureFormOptions";
import DragOverlayComp from "~/components/procedures/template/DragOverlay";
import SortableSection from "~/components/procedures/template/section/SortableSection";
import { useGetProcedureTemplateById } from "~/services/procedures/_query";

const REMOVEABLE_PROPERTIES = [
  "_id",
  "__typename",
  "showDescription",
  "uniqueId",
];

const dropAnimation = {
  sideEffects: defaultDropAnimationSideEffects({
    styles: {
      active: {
        opacity: "0.5",
      },
    },
  }),
};

const ProcedureEdit = () => {
  const { id } = useParams();
  const { messages } = useIntl();
  const {
    user: { oem = {}, role = "" },
  } = useAuth();

  const fileUploader = useUploadFiles();
  const { startToast, updateToast } = useUpdatableToast();

  const { isOemTechnician } = useRoleManager(role);

  const fileUploading = useRef(false);
  const tempPageHeader = useRef(null);

  const [isDirty, setIsDirty] = useState(false);
  const [isSaving, setIsSaving] = useState(false);
  const [showSaveAlert, setShowSaveAlert] = useState(false);

  const [procedureNode, setProcedureNode] = useState({
    name: "",
    description: "",
    children: [],
    allowUsersToAddMoreSignatures: false,
  });

  const [activeField, setActiveField] = useState(null);
  const headingElements = useMemo(
    () =>
      procedureNode.children.filter(
        (item) => item.type === PROCEDURE_NODE_TYPE.HEADING,
      ),
    [procedureNode.children],
  );

  const sectionElements = useMemo(
    () =>
      procedureNode.children.filter(
        (item) => item.type === PROCEDURE_NODE_TYPE.SECTION,
      ),
    [procedureNode.children],
  );

  const dragOverlayItem = useMemo(() => {
    if (activeField?.type === PROCEDURE_NODE_TYPE?.HEADING) {
      return { ...activeField, nodeType: "Heading" };
    } else if (activeField?.type === PROCEDURE_NODE_TYPE?.SECTION) {
      return { ...activeField, nodeType: "Section" };
    } else {
      return { ...activeField, nodeType: "Field" };
    }
  }, [activeField]);

  const { procedure, loading } = useGetProcedureTemplateById(id);
  const { saveProcedureTemplate } = useSaveProcedure();

  const sensors = useSensors(
    useSensor(PointerSensor),
    useSensor(KeyboardSensor, {
      coordinateGetter: sortableKeyboardCoordinates,
    }),
  );

  const fieldIds = useMemo(
    () => procedureNode?.children?.map((field) => field?._id),
    [procedureNode?.children],
  );

  const breadCrumbs = React.useMemo(
    () => [
      {
        label: messages?.menus?.procedures?.title,
        link: "/app/work/procedures",
      },
      { label: procedure?.name || "" },
    ],
    [procedure?.name],
  );

  const onFieldClick = () => {
    const newProcedureState = cloneProcedureTemplate(procedureNode);
    const field = {
      _id: `field-${new Date().getTime()}`,
      type: PROCEDURE_FIELD_TYPE.TEXT_AREA_FIELD,
      name: "",
    };
    const children = [...newProcedureState.children, field];

    setProcedureNode((prev) => ({ ...prev, children }));
    setIsDirty(true);
  };

  const onSectionClick = () => {
    const newProcedureState = cloneProcedureTemplate(procedureNode);
    const newSection = {
      _id: `section-${new Date().getTime()}`,
      type: PROCEDURE_NODE_TYPE.SECTION,
      name: `Section ${(sectionElements?.length || 0) + 1}`,
      children: [
        {
          _id: `field-${new Date().getTime()}`,
          type: PROCEDURE_FIELD_TYPE.TEXT_AREA_FIELD,
          name: "",
        },
      ],
    };
    const children = [...newProcedureState.children, newSection];

    setProcedureNode((prev) => ({ ...prev, children }));
    setIsDirty(true);
  };

  const onHeadingClick = () => {
    const newProcedureState = cloneProcedureTemplate(procedureNode);
    const newHeading = {
      _id: `field-${new Date().getTime()}`,
      type: PROCEDURE_NODE_TYPE.HEADING,
      name: `Heading ${(headingElements?.length || 0) + 1}`,
    };
    const children = [...newProcedureState.children, newHeading];

    setProcedureNode((prev) => ({ ...prev, children }));
    setIsDirty(true);
  };

  const handleNameDescUpdate = (value) => {
    setProcedureNode((prev) => ({ ...prev, ...value }));
    setIsDirty(true);
  };

  const handleHeadingUpdate = (itemIndex, content) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      const children = clonedProcedureTemplate.children;
      children[itemIndex].name = content;

      return { ...prev, children };
    });
    setIsDirty(true);
  };

  const handleHeadingDuplicate = (itemIndex) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      const item = JSON.parse(
        JSON.stringify(clonedProcedureTemplate.children[itemIndex]),
      );
      item._id = `heading-${new Date().getTime()}`;
      clonedProcedureTemplate.children.splice(itemIndex, 0, item);
      return clonedProcedureTemplate;
    });
    setIsDirty(true);
  };

  const handleHeadingDelete = (itemIndex) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      clonedProcedureTemplate.children.splice(itemIndex, 1);

      return clonedProcedureTemplate;
    });
    setIsDirty(true);
  };

  const handleFieldUpdate = (
    parentIndex,
    itemIndex,
    property,
    value,
    isTouched = true,
  ) => {
    setProcedureNode((prev) => {
      const clonedState = cloneProcedureTemplate(prev);
      const children = clonedState.children;

      if (parentIndex === undefined || parentIndex === null) {
        children[itemIndex][property] = value;
        if (children[itemIndex].type === PROCEDURE_FIELD_TYPE.TABLE_FIELD) {
          children[itemIndex][PROCEDURE_NODE_PROPERTY.IS_REQUIRED] = false;
        }
      } else {
        children[parentIndex].children[itemIndex][property] = value;
        if (
          children[parentIndex].children[itemIndex].type ===
          PROCEDURE_FIELD_TYPE.TABLE_FIELD
        ) {
          children[parentIndex].children[itemIndex][
            PROCEDURE_NODE_PROPERTY.IS_REQUIRED
          ] = false;
        }
      }

      return { ...prev, children };
    });

    if (isTouched) {
      setIsDirty(true);
    }
  };

  const handleFieldDuplicate = (parentIndex, itemIndex) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      const children = clonedProcedureTemplate.children;

      if (parentIndex === undefined || parentIndex === null) {
        const item = JSON.parse(JSON.stringify(children[itemIndex]));
        item.attachments = cloneAttachments(children[itemIndex].attachments);
        item._id = `field-${new Date().getTime()}`;
        children.splice(itemIndex + 1, 0, item);
      } else {
        const item = JSON.parse(
          JSON.stringify(children[parentIndex].children[itemIndex]),
        );
        item.attachments = cloneAttachments(
          children[parentIndex].children[itemIndex].attachments,
        );
        item._id = `field-${new Date().getTime()}`;
        children[parentIndex].children.splice(itemIndex + 1, 0, item);
      }

      return { ...prev, children };
    });
    setIsDirty(true);
  };

  const handleSectionRemove = (itemIndex) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      const children = clonedProcedureTemplate.children;

      children.splice(itemIndex, 1);

      return { ...prev, children };
    });
    setIsDirty(true);
  };

  const handleSectionTitleUpdate = (itemIndex, content) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      clonedProcedureTemplate.children[itemIndex].name = content;

      return clonedProcedureTemplate;
    });
    setIsDirty(true);
  };

  const handleSectionDuplicate = (itemIndex) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      const item = cloneProcedureTemplate(
        clonedProcedureTemplate.children[itemIndex],
      );
      item._id = `section-${new Date().getTime()}`;
      item.children = item.children.map((item, index) => {
        const newFieldUid = `field-${new Date().getTime()}`;
        return {
          ...item,
          _id: newFieldUid,
        };
      });
      clonedProcedureTemplate.children.splice(itemIndex + 1, 0, item);
      return clonedProcedureTemplate;
    });
    setIsDirty(true);
  };

  const handleDragEndOnSection = (
    sectionIndex,
    fieldOldIndex,
    fieldNewIndex,
  ) => {
    setProcedureNode((prev) => {
      const newState = cloneProcedureTemplate(prev);
      const item = newState.children[sectionIndex];
      const children = item.children;
      const newChildren = arrayMove(children, fieldOldIndex, fieldNewIndex);
      item.children = newChildren;

      return {
        ...prev,
        children: prev.children.map((field) => {
          if (field._id === item._id) {
            return item;
          }
          return field;
        }),
      };
    });
    setIsDirty(true);
  };

  const handleAddSectionField = (index) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      const section = clonedProcedureTemplate.children[index];

      const field = {
        _id: `field-${new Date().getTime()}`,
        type: PROCEDURE_FIELD_TYPE.TEXT_AREA_FIELD,
        name: "",
      };
      section.children = [...section.children, field];
      clonedProcedureTemplate.children[index] = section;

      return clonedProcedureTemplate;
    });
    setIsDirty(true);
  };

  const handleFieldRemove = (parentIndex, itemIndex) => {
    setProcedureNode((prev) => {
      const clonedProcedureTemplate = cloneProcedureTemplate(prev);
      const children = clonedProcedureTemplate.children;
      if (parentIndex === undefined || parentIndex === null) {
        children.splice(itemIndex, 1);
      } else {
        children[parentIndex].children.splice(itemIndex, 1);
      }
      return { ...prev, children };
    });
    setIsDirty(true);
  };

  const handleSaveClick = () => {
    setShowSaveAlert(true);
  };

  const handleSaveCancelClick = () => {
    const procedure = { ...procedureNode };
    procedure.pageHeader = tempPageHeader?.current || null;
    setProcedureNode(procedure);
    setShowSaveAlert(false);
  };

  const uploadHeaderImage = async (file) => {
    if (!file) return null;
    if (file?.url) return file;
    try {
      const fileObj = {
        name: file.name,
        size: String(file.size),
        type: file.type,
      };

      updateToast(
        messages?.common?.upload?.file,
        TOAST_TYPES.UPLOADING,
        false,
        {
          hideProgressBar: false,
        },
      );

      const uploadData = {
        folder: `oem/${oem?.slug}/procedures/${procedureNode?._id}/file`,
        files: [file],
        forceCustomPath: true,
      };

      fileUploading.current = true;

      const { urls } = await fileUploader(uploadData, false);
      if (urls[0]) {
        fileObj["url"] = urls[0];
      }
      return fileObj;
    } catch (error) {
      console.log("Header Image upload error", error);
    }
  };

  const processAttachment = async (attachments) => {
    const files = [...attachments];

    if (!files || !isArray(files)) return [];
    const list = [];

    for await (const file of files) {
      if (file?.url) {
        list.push(file);
        continue;
      }

      try {
        const fileObj = {
          name: file.name,
          size: String(file.size),
          type: file.type,
        };

        updateToast(
          messages?.common?.upload?.file,
          TOAST_TYPES.UPLOADING,
          false,
          {
            hideProgressBar: false,
          },
        );

        const uploadData = {
          folder: `oem/${oem?.slug}/procedures/${procedureNode?._id}/file`,
          files: [file],
          forceCustomPath: true,
        };

        fileUploading.current = true;

        const { urls } = await fileUploader(uploadData, false);
        if (urls[0]) {
          fileObj["url"] = urls[0];
          list.push(fileObj);
        }
      } catch (error) {
        console.log("attachment upload error", error);
      }
    }

    return list;
  };

  const traverseTemplateNodes = async (obj, path = []) => {
    if (obj === null || obj === undefined) {
      return obj;
    }

    if (Array.isArray(obj)) {
      for (let i = 0; i < obj.length; i++) {
        await traverseTemplateNodes(obj[i], path.concat(i.toString()));
      }
      return obj;
    }

    for (const [key, value] of Object.entries(obj)) {
      const newPath = path.concat(key);

      if (typeof value === "object" && value !== null) {
        await traverseTemplateNodes(value, newPath);
      }

      if (REMOVEABLE_PROPERTIES.includes(key)) {
        if (
          !(
            key === "_id" &&
            newPath.at(-4) === "tableOption" &&
            newPath.at(-3) === "columns"
          )
        ) {
          delete obj[key];
        }
      }

      if (key === "attachments") {
        obj[key] = await processAttachment(obj[key]);
      }

      if (key === "value" && Array.isArray(obj[key])) {
        obj[key] = obj[key].map((row) => ({ ...row }));
      }
    }

    return obj;
  };

  const removeInvalidValues = async (children) => {
    if (children === null || children === undefined) return children;

    for (const child of children) {
      if (
        child.type === PROCEDURE_FIELD_TYPE.IMAGE_UPLOADER_FIELD &&
        child.value
      ) {
        child.value = child.value.filter((child) => child.url && child.type);
      } else if (
        child.type === PROCEDURE_FIELD_TYPE.DATE_FIELD &&
        typeof child.value !== typeof ""
      ) {
        child.value = "";
      } else if (
        child.type === PROCEDURE_FIELD_TYPE.NUMBER_FIELD &&
        (typeof child.value !== typeof "" || typeof child.value !== typeof 0)
      ) {
        child.value = "";
      } else if (child.type === PROCEDURE_NODE_TYPE.SECTION) {
        child.children = await removeInvalidValues(child.children);
      }
    }

    return children;
  };
  const prepareProcedureData = async () => {
    const procedure = { ...procedureNode };

    if (procedure.__typename) delete procedure.__typename;
    if (procedure?.pageHeader?.__typename) {
      delete procedure?.pageHeader?.__typename;
    }
    delete procedure.createdAt;
    delete procedure.updatedAt;
    delete procedure.createdBy;

    try {
      let children = await traverseTemplateNodes(procedure.children);
      children = await removeInvalidValues(children);
      const pageHeader = await uploadHeaderImage(procedure.pageHeader);
      const signatures = await traverseTemplateNodes(
        procedure.signatures || [],
      );
      return { ...procedure, signatures, pageHeader, children };
    } catch (error) {
      console.log("traverse error", error);
      return false;
    }
  };

  const handleSaveProcedure = async () => {
    try {
      setShowSaveAlert(false);
      setIsSaving(true);
      startToast(
        messages?.procedures?.savingProcedureToastMsg,
        TOAST_TYPES.SUCCESS,
        false,
      );
      const data = await prepareProcedureData();

      if (fileUploading.current) {
        fileUploading.current = false;
        updateToast(
          messages?.common?.upload?.success,
          TOAST_TYPES.SUCCESS,
          false,
        );
      }
      if (!data) {
        updateToast(
          messages?.procedures?.procedureTemplateSaveFailed,
          TOAST_TYPES.ERROR,
          false,
          {
            autoClose: TOAST_AUTO_CLOSE_DURATION,
            hideProgressBar: true,
          },
        );
        return;
      }
      await saveProcedureTemplate(data);
      updateToast(
        messages?.procedures?.procedureTemplateSaveSuccess,
        TOAST_TYPES.SUCCESS,
        false,
        {
          autoClose: TOAST_AUTO_CLOSE_DURATION,
          hideProgressBar: true,
        },
      );
      startToast(
        messages?.procedures?.updatingAttachedProcedures,
        TOAST_TYPES.PROCESSING,
        false,
        {
          toastId: PROCEDURE_UPDATING_TOAST_ID,
        },
      );
      setIsDirty(false);
    } catch (error) {
      updateToast(
        messages?.procedures?.procedureTemplateSaveFailed,
        TOAST_TYPES.ERROR,
        false,
        {
          autoClose: TOAST_AUTO_CLOSE_DURATION,
          hideProgressBar: true,
        },
      );
      console.error(error);
    } finally {
      setIsSaving(false);
    }
  };

  const handleSignatureUpdate = (
    updatedSignatures,
    allowUsersToAddMoreSignatures,
  ) => {
    setProcedureNode((prev) => {
      return {
        ...prev,
        signatures: updatedSignatures,
        allowUsersToAddMoreSignatures: allowUsersToAddMoreSignatures,
      };
    });
    setIsDirty(true);
  };

  const storePageHeaderTemp = () => {
    tempPageHeader.current = procedure.pageHeader;
  };

  const handleHeaderImageUpdate = async (file) => {
    const procedure = { ...procedureNode };
    procedure.pageHeader = file || null;
    setProcedureNode(procedure);
    setIsDirty(true);
    setShowSaveAlert(true);
  };

  useEffect(() => {
    if (procedure && !loading) {
      setProcedureNode(JSON.parse(JSON.stringify(procedure)));
    }
  }, [procedure, loading]);

  if (loading || !procedure) return <Loading />;

  return (
    <ContentBox>
      {isSaving && <LoadingOverlay />}
      <RouterPrompt
        when={isDirty}
        okText={messages?.procedures?.templateQuitAlertOkText}
        cancelText={messages?.procedures?.templateQuitAlertCancelText}
        title={messages?.procedures?.templateQuitAlertTitle}
        description={messages?.procedures?.templateQuitAlertMessage}
      />
      <AppHeaderWithSidebarOption>
        <Breadcrumbs options={breadCrumbs} />
        <div className="flex items-center">
          {!isOemTechnician && (
            <>
              <SignatureField
                onChange={handleSignatureUpdate}
                signatures={procedureNode?.signatures || []}
                intialAllowUsersToAddMoreSignaturesValue={
                  procedureNode?.allowUsersToAddMoreSignatures
                }
                hasSignatures={procedureNode?.signatures?.length > 0}
              />
              <ProcedurePreview
                data={procedureNode}
                onUpdateHeader={handleHeaderImageUpdate}
                storePageHeaderTemp={storePageHeaderTemp}
              />
              <div className="pipe"></div>
              <Button
                text={
                  isSaving
                    ? messages?.procedures?.savingCTA
                    : messages?.procedures?.saveCTA
                }
                disabled={isSaving || !isDirty}
                trailingIcon={
                  isSaving && (
                    <Spinner size={16} className="animate-spin-slow" />
                  )
                }
                onClick={handleSaveClick}
              />
            </>
          )}
        </div>
      </AppHeaderWithSidebarOption>
      {isOemTechnician ? (
        <ProcedureInstancePreview
          procedureData={procedureNode}
          isTechnicianView
        />
      ) : (
        <div className="procedure-content-area">
          <ProcedureNameDesc
            procedure={procedureNode}
            onUpdate={handleNameDescUpdate}
          />
          <DndContext
            sensors={sensors}
            modifiers={[restrictToVerticalAxis, restrictToWindowEdges]}
            onDragStart={(event) => {
              const { active } = event;
              if (!active?.id) return;
              const newProcedureState = cloneProcedureTemplate(procedureNode);
              const children = newProcedureState.children;
              const activeField = children.find(
                (field) => field._id === active.id,
              );
              setActiveField(activeField);
            }}
            onDragEnd={(event) => {
              const { active, over } = event;
              const newProcedureState = cloneProcedureTemplate(procedureNode);
              const children = newProcedureState.children;
              if (active.id !== over.id) {
                const oldIndex = children.findIndex(
                  (item) => item._id === active.id,
                );
                const newIndex = children.findIndex(
                  (item) => item._id === over.id,
                );
                const newChildren = arrayMove(children, oldIndex, newIndex);
                setProcedureNode((prev) => ({
                  ...prev,
                  children: newChildren,
                }));
                setActiveField(null);
                setIsDirty(true);
              }
            }}
          >
            <SortableContext
              items={fieldIds}
              strategy={verticalListSortingStrategy}
            >
              {procedureNode.children.length > 0 &&
                procedureNode.children.map((node, index) => {
                  if (node.type === PROCEDURE_NODE_TYPE.HEADING) {
                    return (
                      <SortableHeading
                        key={node._id}
                        sortingDisabled={procedureNode.children.length < 2}
                        index={index}
                        data={node}
                        handleHeadingUpdate={handleHeadingUpdate}
                        handleHeadingDuplicate={handleHeadingDuplicate}
                        handleHeadingDelete={handleHeadingDelete}
                        hasActiveField={!!activeField?._id}
                      />
                    );
                  } else if (node.type === PROCEDURE_NODE_TYPE.SECTION) {
                    return (
                      <SortableSection
                        key={node._id}
                        sortingDisabled={procedureNode.children.length < 2}
                        index={index}
                        data={node}
                        onFieldUpdate={handleFieldUpdate}
                        onFieldDuplicate={handleFieldDuplicate}
                        onFieldRemove={handleFieldRemove}
                        handleSectionTitleUpdate={handleSectionTitleUpdate}
                        handleSectionRemove={handleSectionRemove}
                        handleAddSectionField={handleAddSectionField}
                        handleSectionDuplicate={handleSectionDuplicate}
                        handleDragEndOnSection={handleDragEndOnSection}
                        hasActiveField={!!activeField?._id}
                      />
                    );
                  } else {
                    return (
                      <SortableField
                        key={node._id}
                        sortingDisabled={procedureNode.children.length < 2}
                        parentIndex={null}
                        itemIndex={index}
                        data={node}
                        onChange={handleFieldUpdate}
                        onDuplicateRequest={handleFieldDuplicate}
                        onRemoveRequest={handleFieldRemove}
                        hasActiveField={!!activeField?._id}
                      />
                    );
                  }
                })}
            </SortableContext>
            {createPortal(
              <DragOverlay adjustScale={false} dropAnimation={dropAnimation}>
                {activeField?._id && (
                  <DragOverlayComp
                    title={dragOverlayItem?.name || dragOverlayItem?.nodeType}
                  />
                )}
              </DragOverlay>,
              document.body,
            )}
          </DndContext>
          <ProcedureFormOptions
            actions={{ onFieldClick, onSectionClick, onHeadingClick }}
          />
        </div>
      )}
      <EmptySpace height="160px" />

      <AlertBox
        overlay
        isOpen={showSaveAlert}
        cancelButtonText={messages?.common?.cancel}
        acceptButtonText={messages?.procedures?.templateSaveAlertConfirmText}
        onCancel={handleSaveCancelClick}
        onAccept={handleSaveProcedure}
        image={<ProcedureTemplateSaveAlertIcon width="136" height="131" />}
        title={messages?.procedures?.templateSaveAlertTitle}
        description={messages?.procedures?.templateSaveAlertMessage}
      />
    </ContentBox>
  );
};

export default ProcedureEdit;
